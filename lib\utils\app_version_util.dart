import 'package:ddone/service_locator.dart';
import 'package:logger/logger.dart';

Future<String> getReleaseVersion(Future<String> Function() loadPubspec) async {
  try {
    final contents = await loadPubspec();
    final lines = contents.split('\n');
    for (var line in lines) {
      if (line.startsWith('version:')) {
        final version = line.split(':')[1].trim();
        return version;
      }
    }
  } catch (e) {
    sl.get<Logger>().e('Failed to load pubspec.yaml', error: e);
  }
  return 'Error: Version not found';
}

import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/selected_contact/selected_contact_cubit.dart';
import 'package:ddone/models/hive/local_contact.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AddContactDialog extends StatefulWidget {
  final String? contactName;
  final String? contactNumber;
  final bool isEdit;

  const AddContactDialog({
    this.contactName,
    this.contactNumber,
    required this.isEdit,
    super.key,
  });

  @override
  State<AddContactDialog> createState() => _AddContactDialogState();
}

class _AddContactDialogState extends State<AddContactDialog> {
  late HiveService _hiveService;

  late ContactsCubit _contactsCubit;
  late SelectedContactCubit _selectedContactCubit;

  late TextEditingController _contactNumberController, _displayNameController;

  late bool isEdit;

  @override
  void initState() {
    super.initState();

    _contactsCubit = BlocProvider.of<ContactsCubit>(context);
    _selectedContactCubit = BlocProvider.of<SelectedContactCubit>(context);

    _contactNumberController = TextEditingController();
    _displayNameController = TextEditingController();

    _hiveService = sl.get<HiveService>();

    _displayNameController.text = widget.contactName ?? '';
    _contactNumberController.text = widget.contactNumber ?? '';

    isEdit = widget.isEdit;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        return AlertDialog(
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(32.0))),
          insetPadding: const EdgeInsets.all(10),
          backgroundColor: const Color.fromARGB(255, 54, 54, 54),
          content: SizedBox(
            height: context.deviceHeight(0.35),
            width: context.deviceWidth(0.4),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  children: [
                    SizedBox(
                      width: context.deviceWidth(0.17),
                      child: Text(
                        'Display Name :',
                        style: TextStyle(color: colorTheme.primaryColor),
                      ),
                    ),
                    Container(
                      width: context.deviceWidth(0.2),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(width: 1, color: colorTheme.primaryColor),
                        ),
                      ),
                      child: TextField(
                        controller: _displayNameController,
                        style: const TextStyle(color: Colors.white),
                        cursorColor: colorTheme.primaryColor,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                        ),
                      ),
                    )
                  ],
                ),
                SizedBox(
                  height: context.deviceHeight(0.03),
                ),
                Row(
                  children: [
                    SizedBox(
                      width: context.deviceWidth(0.17),
                      child: Text(
                        'Contact Number :',
                        style: TextStyle(color: colorTheme.primaryColor),
                      ),
                    ),
                    Container(
                      width: context.deviceWidth(0.2),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            width: 1,
                            color: colorTheme.primaryColor,
                          ),
                        ),
                      ),
                      child: TextField(
                        controller: _contactNumberController,
                        style: TextStyle(color: colorTheme.onBackgroundColor),
                        cursorColor: colorTheme.primaryColor,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                        ),
                      ),
                    )
                  ],
                ),
                SizedBox(
                  height: context.deviceHeight(0.05),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        _displayNameController.clear();
                        _contactNumberController.clear();

                        // Dialpad.textfieldFocused = false;

                        pop();
                      },
                      child: SizedBox(
                        // width: context.deviceWidth(0.1),
                        height: context.deviceHeight(0.07),
                        child: Align(
                          alignment: Alignment.center,
                          child: Text(
                            'Cancel',
                            style: textTheme.titleMedium!.copyWith(color: colorTheme.errorColor),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: context.deviceWidth(0.03),
                    ),
                    InkWell(
                      onTap: () {
                        bool isDuplicate = false;
                        int index = 0;
                        final List<LocalContact> lcBox = _hiveService.getAllData<LocalContact>();
                        if (_contactNumberController.text.isEmpty || _displayNameController.text.isEmpty) {
                          EasyLoadingService().showErrorWithText('Display name/contact number cannot be empty');

                          return;
                        }

                        final contactModelList = _contactsCubit.state.contactModelList;

                        for (int i = 0; i < lcBox.length; i++) {
                          var box = lcBox[i];

                          if (isEdit &&
                              (widget.contactNumber == box.contactNumber || widget.contactName == box.displayName)) {
                            index = i;
                            break;
                          }

                          if (_contactNumberController.text == contactModelList[i].contactId ||
                              _displayNameController.text == contactModelList[i].displayName) {
                            isDuplicate = true;

                            break;
                          }
                        }

                        if (!isDuplicate && !isEdit) {
                          _hiveService.addData<LocalContact>(
                            data: LocalContact(
                              displayName: _displayNameController.text,
                              contactNumber: _contactNumberController.text,
                            ),
                          );

                          _hiveService.updateAllCallRecordsName(
                            newName: _displayNameController.text,
                            did: _contactNumberController.text,
                          );

                          _displayNameController.clear();
                          _contactNumberController.clear();

                          // Dialpad.textfieldFocused = false;

                          pop();
                        } else {
                          if (isEdit) {
                            debugPrint('Index in update contact $index');
                            _hiveService.updateDataByIndex<LocalContact>(
                              index: index,
                              data: LocalContact(
                                displayName: _displayNameController.text,
                                contactNumber: _contactNumberController.text,
                              ),
                            );

                            _hiveService.updateAllCallRecordsName(
                              newName: _displayNameController.text,
                              did: _contactNumberController.text,
                            );

                            pop();

                            _selectedContactCubit.setContactInfo(
                              displayName: _displayNameController.text,
                              contactId: _contactNumberController.text,
                              sipAddress: _selectedContactCubit.state.sipAddress,
                              isLocalContact: _selectedContactCubit.state.isLocalContact,
                              isPhoneBookContact: _selectedContactCubit.state.isPhoneBookContact,
                            );

                            return;
                          }

                          EasyLoadingService().showErrorWithText('Contact with same display name/contact number exist');
                        }
                      },
                      child: SizedBox(
                        // width: context.deviceWidth(0.1),
                        height: context.deviceHeight(0.07),
                        child: Align(
                          alignment: Alignment.center,
                          child: Text(
                            isEdit ? 'Edit' : 'Add',
                            style: textTheme.titleMedium!.copyWith(color: colorTheme.connectedColor),
                          ),
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }
}

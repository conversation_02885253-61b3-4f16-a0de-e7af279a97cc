part of 'audio_device_cubit.dart';

abstract class AudioDeviceState extends Equatable {
  final List<AudioDeviceModel> availableDevices;
  final AudioDeviceModel? selectedDevice;
  final bool isLoading;
  final String? errorMessage;

  const AudioDeviceState({
    this.availableDevices = const [],
    this.selectedDevice,
    this.isLoading = false,
    this.errorMessage,
  });

  @override
  List<Object?> get props => [
        availableDevices,
        selectedDevice,
        isLoading,
        errorMessage,
      ];
}

class AudioDeviceInitial extends AudioDeviceState {
  const AudioDeviceInitial() : super();

  @override
  List<Object?> get props => super.props..addAll([]);
}

class AudioDeviceLoading extends AudioDeviceState {
  const AudioDeviceLoading({
    super.availableDevices,
    super.selectedDevice,
  }) : super(isLoading: true);

  @override
  List<Object?> get props => super.props..addAll([]);
}

class AudioDeviceLoaded extends AudioDeviceState {
  const AudioDeviceLoaded({
    required super.availableDevices,
    super.selectedDevice,
  });

  @override
  List<Object?> get props => super.props..addAll([]);
}

class AudioDeviceError extends AudioDeviceState {
  const AudioDeviceError({
    required super.errorMessage,
    super.availableDevices,
    super.selectedDevice,
  });

  @override
  List<Object?> get props => super.props..addAll([]);
}

class AudioDeviceSelectionChanged extends AudioDeviceState {
  const AudioDeviceSelectionChanged({
    required super.availableDevices,
    required super.selectedDevice,
  });

  @override
  List<Object?> get props => super.props..addAll([]);
}

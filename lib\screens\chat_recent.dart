import 'package:ddone/components/button/square_shape_ink_well.dart';
import 'package:ddone/components/button/text_button.dart';
import 'package:ddone/components/common/common_text_field.dart';
import 'package:ddone/components/split_view_widget.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/constants/regex_constants.dart';
import 'package:ddone/cubit/chat_recent/chat_recent_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
// import 'package:ddone/cubit/delete_bookmark/delete_bookmark_cubit.dart';
// import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/screens/chat.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/add_bookmark/add_bookmark_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/cubit/update/info_cubit.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/components/chat_category/chat_category.dart';
import 'package:ddone/utils/pop_out_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:split_view/split_view.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';

class RecentChat extends StatefulWidget {
  static const routeName = '/recentChat';

  const RecentChat({super.key});

  @override
  State<RecentChat> createState() => _RecentChatState();
}

class _RecentChatState extends State<RecentChat> {
  final TextEditingController _mucEditingController = TextEditingController();
  final TextEditingController _mucNickEditingController = TextEditingController();
  final TextEditingController _nickEditingController = TextEditingController();
  late TextEditingController _contactEditingController;

  late LoginCubit _loginCubit;
  late ChatRecentCubit _chatRecentCubit;
  late BookmarkListCubit _bookmarkListCubit;
  // late HomeCubit _homeCubit;
  late InfoCubit _infoCubit;
  late GroupUiCubit _groupUiCubit;
  late ContactsCubit _contactsCubit;
  late MamListCubit _mamListCubit;
  late AddBookmarkCubit _addBookmarkCubit;
  // late DeleteBookmarkCubit _deleteBookmarkCubit;
  late PageController _pageController;
  late SharedPreferences _sharedPreferences;

  var initialPage = kRecentChatsIndex;
  bool _isConnected = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (ModalRoute.of(context)!.isCurrent) {
      debugPrint('didChangeDependencies page resumed ${_chatRecentCubit.state.pageIndex}');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _pageController.jumpToPage(_chatRecentCubit.state.pageIndex);
        }
      });
    }
  }

  @override
  void didUpdateWidget(covariant RecentChat oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (ModalRoute.of(context)!.isCurrent) {
      debugPrint('didUpdateWidget page resumed ${_chatRecentCubit.state.pageIndex}');
    }
  }

  @override
  void initState() {
    super.initState();
    _loginCubit = BlocProvider.of<LoginCubit>(context);
    _chatRecentCubit = BlocProvider.of<ChatRecentCubit>(context);
    _bookmarkListCubit = BlocProvider.of<BookmarkListCubit>(context);
    // _homeCubit = BlocProvider.of<HomeCubit>(context);
    _groupUiCubit = BlocProvider.of<GroupUiCubit>(context);
    _contactsCubit = BlocProvider.of<ContactsCubit>(context);
    _infoCubit = BlocProvider.of<InfoCubit>(context);
    _mamListCubit = BlocProvider.of<MamListCubit>(context);

    _addBookmarkCubit = AddBookmarkCubit.initial();
    // _deleteBookmarkCubit = DeleteBookmarkCubit.initial();

    _contactEditingController = TextEditingController();
    _sharedPreferences = sl.get<SharedPreferences>();

    if (_loginCubit.state is LoginAuthenticated) {
      final LoginAuthenticated loginState = _loginCubit.state as LoginAuthenticated;
      _isConnected = loginState.connection.isAuthenticated;
    } else {
      _isConnected = false;
    }

    _pageController = PageController(
      initialPage: initialPage,
      keepPage: true,
    );

    _chatRecentCubit.getItmes(_loginCubit);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      firstLoadRecentChat();
    });
  }

  Future<void> firstLoadRecentChat() async {
    await _contactsCubit.getContactList();
    await pullRecentChat();
  }

  Future<void> pullRecentChat() async {
    if (_loginCubit.state is LoginAuthenticated) {
      final loginAuthenticatedState = _loginCubit.state as LoginAuthenticated;
      final muc = loginAuthenticatedState.connection.getManagerById<MUCManager>(mucManager)!;
      await muc.getBookMarks();
      await _bookmarkListCubit.getbookmarkList();
      _contactsCubit.setBookmarkGroupList(bookmarkListCubit: _bookmarkListCubit);
    }
    _mamListCubit.getMamList();
    _mamListCubit.getCombinedFullList(contactsCubit: _contactsCubit);
    _mamListCubit.getCombinedContactList(contactsCubit: _contactsCubit);
    _mamListCubit.getBookmarkList(contactsCubit: _contactsCubit);
  }

  @override
  void dispose() {
    _mucEditingController.dispose();
    _mucNickEditingController.dispose();
    _nickEditingController.dispose();
    _contactEditingController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void animateToChatCategoryPage(int index) {
    // Check if mounted before accessing context or state during async gaps or callbacks
    if (mounted) {
      _pageController.jumpToPage(index);
      _chatRecentCubit.setPageIndex(index); // Keep track of index in cubit
    }
  }

  Widget _buildChatListScaffold(BuildContext context, ThemeState themeState) {
    final colorTheme = themeState.colorTheme;
    final textTheme = themeState.themeData.textTheme;
    // Ensure sipDomain is accessible here. Get it from sharedPreferences.
    final sipDomain = _sharedPreferences.getString(CacheKeys.sipDomain) ?? ''; // Provide default or handle null

    return Scaffold(
      backgroundColor: colorTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: colorTheme.backgroundColor,
        // leading: const Tooltip(
        //   message: 'Notifications',
        //   child: NotificationsDropdown(),
        // ),
        title: Text(
          'Chat List',
          style: TextStyle(
            color: colorTheme.primaryColor,
          ),
        ),
        centerTitle: true,

        /// TEMP: HIDE GROUP CHAT FUNCTIONALITY BEFORE IT IS READY!
        // actions: [
        //   Tooltip(
        //     message: 'Create Group',
        //     child: SquareShapeInkWell(
        //       onTap: () {
        //         if (_isConnected) {
        //           _showCreateGroupDialog(context, colorTheme, textTheme, sipDomain);
        //         } else {
        //           errorSnackBar(context, 'Not connected. Cannot create group.'); // More specific error
        //         }
        //       },
        //       width: 35,
        //       height: 35,
        //       color: Colors.transparent,
        //       contentWidget: Icon(
        //         Icons.group_add_outlined,
        //         color: colorTheme.primaryColor,
        //       ),
        //     ),
        //   ),
        //   const SizedBox(width: spacingSmall), // Add some padding if needed
        // ],
      ),
      body: BlocBuilder<GroupUiCubit, GroupUiState>(
        // Keep BlocBuilder for UI updates if needed
        builder: (context, groupState) {
          // Use groupState if needed
          return Container(
            // Consider removing Container if Scaffold's backgroundColor is sufficient
            color: context.colorTheme().surface, // Use themeState's surface color directly
            child: BlocBuilder<ChatRecentCubit, ChatRecentState>(
              builder: (context, chatRecentState) {
                return Column(
                  children: [
                    // --- Category Buttons ---
                    Padding(
                      padding: const EdgeInsets.only(
                          left: 10.0, top: spacingMedium, bottom: spacingSmall), // Add some vertical padding
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            _buildCategoryButton(context, themeState, chatRecentState, kRecentChatsIndex, 'Recent'),
                            const SizedBox(width: 10),
                            _buildCategoryButton(context, themeState, chatRecentState, kDirectMessagesIndex, 'Message',
                                tooltip: 'Direct Messages'),
                            const SizedBox(width: 10),

                            /// TEMP: HIDE GROUP CHAT FUNCTIONALITY BEFORE IT IS READY!
                            // _buildCategoryButton(context, themeState, chatRecentState, kGroupChatsIndex, 'Groups',
                            //     tooltip: 'Group Messages'),
                          ],
                        ),
                      ),
                    ),
                    // --- Search Field ---
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20.0),
                      child: CommonTextField(
                        textEditingController: _contactEditingController,
                        onValueChanged: (value) {
                          _mamListCubit.filterBookmarkList(value);
                          _mamListCubit.filterContactList(value);
                          _mamListCubit.filterFullList(value);
                        },
                        hintText: 'Search',
                        prefixIcon: const Icon(
                          Icons.search,
                          size: iconSizeMedium,
                        ),
                      ),
                    ),
                    const SizedBox(height: spacingLarge), // Add spacing
                    // --- Page View ---
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 3), // Adjust padding as needed
                        child: PageView(
                          physics: const NeverScrollableScrollPhysics(),
                          controller: _pageController,
                          onPageChanged: (index) {
                            // If you ever allow swiping, update cubit state here
                            // _chatRecentCubit.setPageIndex(index);
                          },
                          children: const [
                            ChatCategory(index: kRecentChatsIndex),
                            ChatCategory(index: kDirectMessagesIndex),
                            ChatCategory(index: kGroupChatsIndex),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildCategoryButton(
      BuildContext context, ThemeState themeState, ChatRecentState chatRecentState, int index, String text,
      {String? tooltip}) {
    final colorTheme = themeState.colorTheme;
    final bool isActive = chatRecentState.pageIndex == index;
    return Tooltip(
      message: tooltip ?? text,
      child: TextButtonWidget(
        onPressed: () => animateToChatCategoryPage(index),
        buttonStyle: ButtonStyle(
          backgroundColor: WidgetStatePropertyAll(
            isActive
                ? colorTheme.onPrimaryColor.withOpacity(opacityLow)
                : Colors.black45, // Consider using a theme color here too
          ),
          // Add padding or minimum size if needed
          padding: WidgetStateProperty.all(const EdgeInsets.symmetric(horizontal: 12, vertical: 8)),
          shape:
              WidgetStateProperty.all(RoundedRectangleBorder(borderRadius: BorderRadius.circular(8))), // Add some shape
        ),
        contentWidget: Text(
          text,
          // Style the text if needed, e.g., based on active state
          // style: TextStyle(color: isActive ? colorTheme.primaryColor : Colors.white),
        ),
      ),
    );
  }

  void _showCreateGroupDialog(BuildContext context, ColorTheme colorTheme, TextTheme textTheme, String sipDomain) {
    _mucEditingController.clear(); // Clear previous input
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        // Use dialogContext
        backgroundColor: colorTheme.backgroundColor,
        title: const Center(
          child: Text(
            'Create Group',
            style: TextStyle(
              // Use textTheme or specific style
              color: Colors.white, // Consider theme color
              fontSize: 17,
            ),
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              style: const TextStyle(
                color: Colors.white, // Consider theme color
              ),
              inputFormatters: [FilteringTextInputFormatter.allow(createGroupNameRegex)],
              cursorColor: colorTheme.primaryColor,
              controller: _mucEditingController,
              decoration: InputDecoration(
                hintText: 'Group name...',
                hintStyle: textTheme.bodyLarge!.copyWith(
                  color: Colors.white54, // Consider theme color
                  fontWeight: FontWeight.w500,
                ),
                enabledBorder: const OutlineInputBorder(
                  borderSide: BorderSide(
                    color: Colors.white70, // Consider theme color
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  // Use theme color for focus
                  borderSide: BorderSide(
                    color: colorTheme.primaryColor, // Example: Use primary color
                    width: 2.0,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            // No need for BlocBuilder here unless the buttons depend on GroupUiState
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const TextButtonWidget(
                  onPressed: pop,
                  contentWidget: Text(
                    'Cancel',
                    style: TextStyle(
                      color: Colors.red, // Consider theme color for destructive action
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                TextButtonWidget(
                  onPressed: () async {
                    final groupName = _mucEditingController.text.trim();
                    if (groupName.isEmpty) {
                      errorSnackBar(context, 'Group name cannot be empty');
                      return;
                    }
                    final groupJid = '${groupName.replaceAll(' ', '').toLowerCase()}@muc.$sipDomain';

                    pop();

                    await _chatRecentCubit.createChatRoom(
                      _loginCubit,
                      groupJid,
                      _nickEditingController.text, // Ensure these have values if needed
                      _mucNickEditingController.text, // Ensure these have values if needed
                      _addBookmarkCubit,
                      _bookmarkListCubit,
                      _groupUiCubit,
                      _contactsCubit,
                    );

                    // Trigger updates - consider debouncing or combining updates if possible
                    _infoCubit.triggerBookmarkList();
                    if (context.mounted) context.read<GroupUiCubit>().update(); // Use read if only triggering an action
                    await pullRecentChat();

                    // Delay might not be reliable, ideally wait for confirmation
                    await Future.delayed(const Duration(milliseconds: 500)); // Shorter delay?

                    _infoCubit.getGroupChatHistory(
                      receiver: groupJid,
                      nick: groupJid,
                      name: groupJid,
                      loginCubit: _loginCubit,
                      mamListCubit: _mamListCubit,
                    );
                  },
                  contentWidget: const Text(
                    'Create',
                    style: TextStyle(
                      color: Colors.green, // Consider theme color for constructive action
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Build the common UI part using BlocBuilder<ThemeCubit, ThemeState>
    final Widget chatListScaffold = BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        // Pass context and themeState to the helper method
        return _buildChatListScaffold(context, themeState);
      },
    );

    // Return the appropriate layout
    if (isMobile) {
      return chatListScaffold; // Just the chat list scaffold
    } else {
      return SplitViewWidget(
        weightList: const [0.40, 0.60], // Define weights clearly
        weightLimitList: [
          WeightLimit(min: 0.40), // Minimum weight for the first pane
          WeightLimit(max: 0.60, min: 0.5), // Limits for the second pane
        ],
        splitViewWidgetList: [
          chatListScaffold, // The common chat list UI
          const ChatPage(), // The chat detail view
        ],
      );
    }
  }
}

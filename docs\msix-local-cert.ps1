# TO BUILD MSIX that can be installed locally.

# Change your directory
Set-Location -Path "C:\Users\<USER>\Project\ddone\windows" # this should be your project's windows folder

# 1. Create a new self-signed certificate.
$newCert = New-SelfSignedCertificate -Type Custom -Subject "CN=5249AA42-463B-4631-A982-BFCAA733DE2D" `
    -KeyUsage DigitalSignature `
    -FriendlyName "Local DDOne MSIX Certificate" `
    -CertStoreLocation "Cert:\CurrentUser\My" `
    -TextExtension @("2.5.29.37={text}1.3.6.1.5.5.7.3.3", "2.5.29.19={text}") `
    -KeyExportPolicy Exportable `
    -KeyAlgorithm RSA -KeyLength 2048 `
    -NotAfter (Get-Date).AddYears(10)
    
# 2. Export the new certificate to a PFX file.
$password = ConvertTo-SecureString -String "d0tdash123" -AsPlainText -Force
$pfxPath = "DDOneLocalCert.pfx"
Export-PfxCertificate -Cert $newCert -FilePath $pfxPath -Password $password

# 3. Trust the new certificate by adding it to the Local Machine's Root store
$tempCerPath = "DDOneLocalCert.cer"
Export-Certificate -Cert $newCert -FilePath $tempCerPath
Import-Certificate -FilePath $tempCerPath -CertStoreLocation Cert:\LocalMachine\Root

# This will output something like:
# Thumbprint                                Subject
# ----------                                -------
# A1B2C3D4E5F6...                         CN=YourCompany

# 4. Verify that it's in your personal store and trusted root
# - alternatively, can look into certmgr.msc > Personal > Certificates
Get-ChildItem -Path Cert:\CurrentUser\My | Where-Object {$_.Subject -like "*5249AA42-463B-4631-A982-BFCAA733DE2D*"}
Get-ChildItem -Path Cert:\LocalMachine\Root | Where-Object {$_.Subject -like "*5249AA42-463B-4631-A982-BFCAA733DE2D*"}

# 5. If bundeling it with dart run msix:create does not work (mine duno why hit "Error: SignerSign() failed."), try manually sign:
# - look into your windows kit folder, find the latest version of signtool. Below show it is using 10.0.22621.0 version
& "C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\x64\signtool.exe" sign /v /fd SHA256 /f "C:\Users\<USER>\Project\ddone\windows\DDOneLocalCert.pfx" /p "d0tdash123" "C:\Users\<USER>\Project\ddone\build\windows\x64\runner\Release\ddone.msix"

# in case anything goes wrong and we need to remove all the cert
# - replace FF6B46E1CDB8E260D905669FF58D01BFA84227B6 with your thumbprint from Import-Certificate command.
Get-ChildItem -Path Cert:\CurrentUser\My\FF6B46E1CDB8E260D905669FF58D01BFA84227B6 | Remove-Item -ErrorAction SilentlyContinue
Get-ChildItem -Path Cert:\LocalMachine\Root\FF6B46E1CDB8E260D905669FF58D01BFA84227B6 | Remove-Item -ErrorAction SilentlyContinue
Remove-Item -Path "C:\Users\<USER>\Project\ddone\windows\DDOneLocalCert.pfx" -ErrorAction SilentlyContinue
Remove-Item -Path "C:\Users\<USER>\Project\ddone\windows\DDOneLocalCert.cert" -ErrorAction SilentlyContinue

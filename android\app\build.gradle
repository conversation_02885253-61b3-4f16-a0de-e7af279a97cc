def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}



// Initialize a new Properties() object called keystoreProperties.
def keystoreProperties = new Properties()

def keystorePropertiesFile = rootProject.file("key.properties")

// Load your keystore.properties file into the keystoreProperties object.
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}


apply plugin: 'com.android.application'
// START: FlutterFire Configuration
apply plugin: 'com.google.gms.google-services'
// END: FlutterFire Configuration
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

android {

    // ----- BEGIN flavorDimensions (autogenerated by flutter_flavorizr) -----
    flavorDimensions += "flavor-type"

    productFlavors {
        development {
            dimension "flavor-type"
            applicationId "com.dotdashtech.ddone.dev"
            minSdkVersion 26
            compileSdkVersion 34
            resValue "string", "app_name", "DDOne Dev"
        }
        staging {
            dimension "flavor-type"
            applicationId "com.dotdashtech.ddone.staging"
            minSdkVersion 26
            compileSdkVersion 34
            resValue "string", "app_name", "DDOne Staging"
        }
        production {
            dimension "flavor-type"
            applicationId "com.dotdashtech.ddone"
            minSdkVersion 26
            compileSdkVersion 34
            resValue "string", "app_name", "DDOne"
        }
    }

    // ----- END flavorDimensions (autogenerated by flutter_flavorizr) -----

    namespace 'com.example.ddone' 
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.dotdashtech.ddone"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-build-configuration.
        minSdkVersion 26
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

      compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }

    // compileOptions {
    //     sourceCompatibility JavaVersion.VERSION_11
    //     targetCompatibility JavaVersion.VERSION_11
    //     coreLibraryDesugaringEnabled true
    // }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.0.3'
}

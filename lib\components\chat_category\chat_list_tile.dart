import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/constants/right_click_menu_items/menu_items.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/members_presence/members_presence_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_context_menu/flutter_context_menu.dart';
import 'package:flutter_randomcolor/flutter_randomcolor.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ChatListTile extends StatelessWidget {
  final VoidCallback? onTap;
  final Function(dynamic)? onItemSelected;
  final bool isTileSelected;
  final String title;
  const ChatListTile({
    this.onTap,
    this.isTileSelected = false,
    this.onItemSelected,
    required this.title,
    super.key,
  });

  //get rgb to color
  Color rgbToColor(String rgb) {
    final RegExp regex = RegExp(r'rgb\((\d+),(\d+),(\d+)\)');
    final Match? match = regex.firstMatch(rgb);

    if (match != null) {
      final int r = int.parse(match.group(1)!);
      final int g = int.parse(match.group(2)!);
      final int b = int.parse(match.group(3)!);

      return Color.fromARGB(255, r, g, b); // Assuming full opacity
    }

    // Return a default color if parsing fails
    return Colors.black;
  }

  //check light color
  bool _isLightColor(Color color) {
    // Calculate luminance
    double luminance = (0.299 * color.red + 0.587 * color.green + 0.114 * color.blue) / 255;
    return luminance > 0.5; // Threshold for determining light or dark color
  }

  @override
  Widget build(BuildContext context) {
    final xmppAccount = sl.get<SharedPreferences>();
    final sipNum = xmppAccount.get(CacheKeys.sipNumber);
    final sipDomain = xmppAccount.get(CacheKeys.sipDomain);
    final sipAccount = '$sipNum@$sipDomain';
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;
        return BlocBuilder<PresencesCubit, PresencesState>(
          builder: (context, presencesState) {
            final member = presencesState.presenceList[title];

            bool isMeOwner = false;
            if (member != null) {
              isMeOwner = member.any((members) => (members.affiliation == 'owner' && members.jid == sipAccount));
            } else {
              isMeOwner = false;
            }

            var options = Options();
            var color = RandomColor.getColor(options);
            Color rgbColor = rgbToColor(color);

            bool isLightColor = _isLightColor(rgbColor);

            return ContextMenuRegion(
              contextMenu: ContextMenu(
                entries:
                    isMeOwner || !title.contains('muc') ? chatPopUpRightClickMenu : chatPopUpRightClickMenuNotOwner,
              ),
              onItemSelected: onItemSelected,
              child: SizedBox(
                height: 70,
                child: Center(
                  child: ListTile(
                      leading: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: CircleAvatar(
                          backgroundColor: rgbColor,
                          child: Text(
                            title[0].toUpperCase(),
                            style: textTheme.displaySmall!.copyWith(
                              color: isLightColor ? colorTheme.backgroundColor : colorTheme.secondaryBackgroundColor,
                            ),
                          ),
                        ),
                      ),
                      horizontalTitleGap: 4,
                      contentPadding: const EdgeInsets.only(right: 5.0),
                      // horizontalTitleGap: 5,
                      title: isTileSelected
                          ? Padding(
                              padding: const EdgeInsets.only(bottom: 5.0),
                              child: Text(
                                title,
                                style: textTheme.bodyLarge!.copyWith(
                                  color: colorTheme.onPrimaryColor.withOpacity(opacityMedium),
                                ),
                                // TextStyle(
                                //   color: colorTheme.onPrimaryColor.withOpacity(opacityMedium),
                                // ),
                                maxLines: 1,
                                overflow: TextOverflow.fade,
                                softWrap: false,
                              ),
                            )
                          : Padding(
                              padding: const EdgeInsets.only(bottom: 5.0),
                              child: Text(
                                title,
                                style: textTheme.bodyLarge!.copyWith(
                                  color: colorTheme.onPrimaryColor,
                                ),
                                // const TextStyle(
                                //   color: Colors.white70,
                                // ),
                                maxLines: 1,
                                overflow: TextOverflow.fade,
                                softWrap: false,
                              ),
                            ),
                      onTap: onTap,
                      tileColor:
                          isTileSelected ? colorTheme.onPrimaryColor.withOpacity(opacityExtraLow) : Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          7,
                        ),
                      )),
                ),
              ),
            );
          },
        );
      },
    );
  }
}

// Example usage of the new tablet detection methods in screen_util.dart

import 'package:flutter/material.dart';
import 'package:ddone/utils/screen_util.dart';

class ExampleTabletUsage extends StatelessWidget {
  const ExampleTabletUsage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tablet Detection Example'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Platform detection
            Text('Platform Detection:', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            Text('Is Android: $isAndroid'),
            Text('Is iOS: $isIOS'),
            Text('Is Mobile: $isMobile'),
            Text('Is Desktop: $isDesktop'),
            
            const SizedBox(height: 24),
            
            // Tablet detection
            Text('Tablet Detection:', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            
            // Android tablet detection (synchronous)
            Text('Is Android Tablet: ${isAndroidTablet(context)}'),
            
            // iPad detection (asynchronous)
            FutureBuilder<bool>(
              future: isIpad,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Text('Is iPad: Loading...');
                }
                return Text('Is iPad: ${snapshot.data ?? false}');
              },
            ),
            
            // General tablet detection (asynchronous)
            FutureBuilder<bool>(
              future: isTablet(context),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Text('Is Tablet: Loading...');
                }
                return Text('Is Tablet: ${snapshot.data ?? false}');
              },
            ),
            
            const SizedBox(height: 24),
            
            // Screen information
            Text('Screen Information:', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            Text('Screen Size: ${MediaQuery.of(context).size}'),
            Text('Device Pixel Ratio: ${MediaQuery.of(context).devicePixelRatio}'),
            
            const SizedBox(height: 24),
            
            // Conditional UI based on tablet detection
            Text('Conditional UI:', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            
            // Example of using tablet detection for UI decisions
            FutureBuilder<bool>(
              future: isTablet(context),
              builder: (context, snapshot) {
                final isTabletDevice = snapshot.data ?? false;
                
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const CircularProgressIndicator();
                }
                
                return Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isTabletDevice ? Colors.blue.shade100 : Colors.green.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isTabletDevice ? 'Tablet Layout' : 'Phone Layout',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        isTabletDevice 
                          ? 'This UI is optimized for tablets with larger screens'
                          : 'This UI is optimized for phones with smaller screens',
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

// Example of how to use tablet detection in different scenarios
class TabletDetectionExamples {
  
  // Example 1: Synchronous Android tablet detection
  static Widget buildAndroidSpecificUI(BuildContext context) {
    if (isAndroidTablet(context)) {
      return const Text('Android Tablet UI');
    } else {
      return const Text('Android Phone UI');
    }
  }
  
  // Example 2: Asynchronous iPad detection
  static Future<Widget> buildIOSSpecificUI() async {
    if (await isIpad) {
      return const Text('iPad UI');
    } else {
      return const Text('iPhone UI');
    }
  }
  
  // Example 3: General tablet detection with fallback
  static Future<Widget> buildResponsiveUI(BuildContext context) async {
    final isTabletDevice = await isTablet(context);
    
    if (isTabletDevice) {
      // Tablet-specific UI
      return const Row(
        children: [
          Expanded(flex: 1, child: Text('Sidebar')),
          Expanded(flex: 2, child: Text('Main Content')),
        ],
      );
    } else {
      // Phone-specific UI
      return const Column(
        children: [
          Text('Header'),
          Expanded(child: Text('Main Content')),
        ],
      );
    }
  }
  
  // Example 4: Using tablet detection for different layouts
  static Widget buildAdaptiveLayout(BuildContext context) {
    return FutureBuilder<bool>(
      future: isTablet(context),
      builder: (context, snapshot) {
        final isTabletDevice = snapshot.data ?? false;
        
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (isTabletDevice) {
          // Two-column layout for tablets
          return const Row(
            children: [
              Expanded(
                flex: 1,
                child: Card(child: Center(child: Text('Navigation Panel'))),
              ),
              Expanded(
                flex: 2,
                child: Card(child: Center(child: Text('Content Panel'))),
              ),
            ],
          );
        } else {
          // Single-column layout for phones
          return const Column(
            children: [
              Card(child: Center(child: Text('Navigation'))),
              Expanded(
                child: Card(child: Center(child: Text('Content'))),
              ),
            ],
          );
        }
      },
    );
  }
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

class MessageModel {
  final String text;
  final String timeStamp;
  final bool isSender;

  MessageModel({
    required this.text,
    required this.timeStamp,
    required this.isSender,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'text': text,
      'timeStamp': timeStamp,
      'isSender': isSender,
    };
  }

  factory MessageModel.fromMap(Map<String, dynamic> map) {
    return MessageModel(
      text: map['text'] as String,
      timeStamp: map['timeStamp'] as String,
      isSender: map['isSender'] as bool,
    );
  }

  String toJson() => json.encode(toMap());

  factory MessageModel.fromJson(String source) =>
      MessageModel.fromMap(json.decode(source) as Map<String, dynamic>);
}

# DDOne
### Copyright Free Ringtone Website (MIT, APACHE, BSD)
https://pixabay.com/sound-effects/search/phone-ring/

Ext, pdf, filetype Icon
1. <PERSON><PERSON><PERSON>, https://icon-icons.com/icon/ext-pdf-filetype/176234

Ext, xlsx Icon
2. <PERSON><PERSON><PERSON>, https://icon-icons.com/icon/ext-xlsx/176245

Text, xml Icon
3. <PERSON><PERSON><PERSON>, https://icon-icons.com/icon/ext-audio-generic-filetype/176230

Ext, file, generic, filetype Icon
4. <PERSON><PERSON><PERSON>, https://icon-icons.com/icon/ext-file-generic-filetype/176256

Ext, txt, filetype Icon
5. <PERSON><PERSON><PERSON>, https://icon-icons.com/icon/ext-txt-filetype/176233

Ext, doc, filetype Icon
6. <PERSON><PERSON><PERSON>, https://icon-icons.com/icon/ext-doc-filetype/176249

Ext, ppt, filetype Icon
7. <PERSON><PERSON><PERSON>, https://icon-icons.com/icon/ext-ppt-filetype/176231

Ext, audio, generic, filetype Icon
8. Jeremiah, https://icon-icons.com/icon/text-xml/103703

Text, javascript Icon
9. <PERSON>, https://icon-icons.com/icon/text-javascript/103696

DTMF sound
*: https://onlinesound.net/download/audio/dtmf-dialing-button-asterisk
#: https://onlinesound.net/download/audio/dtmf-dialing-button-octothorpe
0: https://onlinesound.net/download/audio/dtmf-dialing-button-0
1: https://onlinesound.net/download/audio/dtmf-dialing-button-1
2: https://onlinesound.net/download/audio/dtmf-dialing-button-2
3: https://onlinesound.net/download/audio/dtmf-dialing-button-3
4: https://onlinesound.net/download/audio/dtmf-dialing-button-4
5: https://onlinesound.net/download/audio/dtmf-dialing-button-5
6: https://onlinesound.net/download/audio/dtmf-dialing-button-6
7: https://onlinesound.net/download/audio/dtmf-dialing-button-7
8: https://onlinesound.net/download/audio/dtmf-dialing-button-8
9: https://onlinesound.net/download/audio/dtmf-dialing-button-9

## Set dart line
Open Settings.json and add or modify settings below

"dart.lineLength": 120,
"editor.codeActionsOnSave": {
    "source.fixAll": "explicit"
},
"editor.formatOnSave": true

## Install FVM (Flutter Version Management)

#### MacOS
```
curl -fsSL https://fvm.app/install.sh | bash
```
or
```
brew tap leoafarias/fvm
brew install fvm
```

#### Windows
```
choco install fvm
```

### Activate FVM
```
dart pub global activate fvm
```

### Install Flutter Sdk with FVM
```
fvm install 3.22.0 --setup
for windows please install 3.24.3
```

### Use Downloaded Flutter Sdk
```
fvm use 3.22.0
for windows please use 3.24.3
```

## Update Flavor Setting ([git stash save] before run command below incase any happen)
```
1. Stash all your changes
2. Temporarily move [app.dart, main_development.dart, main_production.dart, main_staging.dart, main.dart] out from repo.
3. fvm flutter pub run flutter_flavorizr (windows platform will have gem error which can be ignored)
4. fvm flutter pub run flutter_flavorizr -p ide:config
5. Discard [app.dart, flavors.dart, main_development.dart, main_production.dart, main_staging.dart, main.dart, my_home_page.dart]
6. Move [app.dart, main_development.dart, main_production.dart, main_staging.dart, main.dart] back to repo.
5. Apply your stash

refer to https://pub.dev/packages/flutter_flavorizr
```

## Get sensitive files from developer
1. all .env files
2. google-service.json
3. upload-keystore.jks
4. keys.properties

## Build Runner Project
```
fvm dart run build_runner build --delete-conflicting-outputs
```

## Run Project
```
fvm flutter run --flavor [development/staging/production] -t lib/main_[development/staging/production].dart
```
#### Development env 
```
fvm flutter run --flavor development -t lib/main_development.dart
```
#### Staging env
```
fvm flutter run --flavor staging -t lib/main_staging.dart
```
#### Production env
```
fvm flutter run --flavor production -t lib/main_production.dart
```

## Build Project 
```
fvm flutter build windows --dart-define=FLAVOR=production -t lib/main_production.dart --release

fvm flutter build apk --no-tree-shake-icons --dart-define=FLAVOR=production -t lib/main_production.dart --release

fvm flutter build appbundle --no-tree-shake-icons --dart-define=FLAVOR=production -t lib/main_production.dart --release
```

## How to deploy to firebase app distribution (Android)
```
cd android
```
### Distribute for dev
```
fastlane distribute_dev
```

# Build MSIX bundle for uploading to Microsoft Partner Center
```
dart run msix:create
```

## Backup
```
flutter pub run flutter_flavorizr -p [ ]
android:buildGradle,android:androidManifest,android:icons
ios:xcconfig,ios:schema,ios:plist,ios:podfile,ios:plist,ios:icons,ios:buildTargets
macos:podfile,macos:xcconfig,macos:configs,macos:schema,macos:plist,macos:icons,macos:buildTargets
ide:config
```

## Sending a Notification Message:
FCM allows you to send notification messages to your app users’ devices. These messages are automatically handled by the system when your app is in the background, and the notification appears in the device’s notification center. Here’s an example of sending a notification message from the Firebase console:
```
{
  "notification": {
    "title": "New Message",
    "body": "You've received a new message!"
  },
  "to": "DEVICE_TOKEN"
}
```

## Sending Data Messages:
Data messages, also known as silent or background messages, are handled by your Flutter app itself, even when it’s in the background or terminated. You can use data messages to send custom data to your app. Here’s an example of sending a data message:
```
{
  "data": {
    "title": "New Message",
    "body": "You've received a new message!",
    "chatId": "CHAT_ID"
  },
  "to": "DEVICE_TOKEN"
}
```
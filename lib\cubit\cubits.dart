import 'package:ddone/cubit/add_bookmark/add_bookmark_cubit.dart';
import 'package:ddone/cubit/audio_device/audio_device_cubit.dart';
import 'package:ddone/cubit/auth/auth_cubit.dart';
import 'package:ddone/cubit/chat_recent/chat_recent_cubit.dart';
import 'package:ddone/cubit/common/network/network_cubit.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/delete_bookmark/delete_bookmark_cubit.dart';
import 'package:ddone/cubit/focus/focus_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/invitation/invitation_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/messaging/messages_cubit.dart';
import 'package:ddone/cubit/messaging/receive_message_cubit.dart';
import 'package:ddone/cubit/selected_contact/selected_contact_cubit.dart';
import 'package:ddone/cubit/selected_group/selected_group_cubit.dart';
import 'package:ddone/cubit/update/chat_ui_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/cubit/update/info_cubit.dart';
import 'package:ddone/cubit/version_management/version_management_cubit.dart';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/cubit/xmpp/connection/xmpp_connection_cubit.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:moxxmpp/cubit/decline_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:moxxmpp/cubit/member_cubit.dart';
import 'package:ddone/cubit/members_presence/members_presence_cubit.dart';

final globalBlocProviders = <BlocProvider>[
  BlocProvider<AuthCubit>(
    create: (context) => AuthCubit.initial(),
  ),
  BlocProvider<AudioDeviceCubit>(
    create: (context) => AudioDeviceCubit.initial(),
  ),
  BlocProvider<LoginCubit>(
    create: (context) => LoginCubit.initial(),
  ),
  BlocProvider<GroupUiCubit>(
    create: (context) => GroupUiCubit.initial(),
  ),
  BlocProvider<ChatUiCubit>(
    create: (context) => ChatUiCubit.initial(),
  ),
  BlocProvider<MessagesCubit>(
    create: (context) => MessagesCubit.initial(),
  ),
  BlocProvider<BookmarkListCubit>(
    create: (context) => BookmarkListCubit(),
    lazy: false,
  ),
  BlocProvider<MamListCubit>(
    create: (context) => MamListCubit(),
    lazy: false,
  ),
  BlocProvider<DeclineCubit>(
    create: (context) => DeclineCubit(),
    lazy: false,
  ),
  BlocProvider<PresencesCubit>(
    create: (context) => PresencesCubit(),
    lazy: false,
  ),
  BlocProvider<ChatRecentCubit>(
    create: (context) => ChatRecentCubit(),
  ),
  BlocProvider<InvitationCubit>(
    create: (context) => InvitationCubit.initial(),
  ),
  BlocProvider<InfoCubit>(
    create: (context) => InfoCubit.initial(),
  ),
  BlocProvider<AddBookmarkCubit>(
    create: (context) => AddBookmarkCubit.initial(),
  ),
  BlocProvider<DeleteBookmarkCubit>(
    create: (context) => DeleteBookmarkCubit.initial(),
  ),
  BlocProvider<HomeCubit>(
    create: (context) => HomeCubit.initial(),
  ),
  BlocProvider<ThemeCubit>(
    create: (context) => ThemeCubit.initial(),
  ),
  BlocProvider<NetworkCubit>(
    create: (context) => NetworkCubit.initial(),
  ),
  BlocProvider<VoipCubit>(
    create: (context) => VoipCubit.initial(),
  ),
  BlocProvider<ContactsCubit>(
    create: (context) => ContactsCubit.initial(),
  ),
  BlocProvider<SelectedContactCubit>(
    create: (context) => SelectedContactCubit.initial(),
  ),
  BlocProvider<SelectedGroupCubit>(
    create: (context) => SelectedGroupCubit.initial(),
  ),
  BlocProvider<FocusCubit>(
    create: (context) => FocusCubit(),
  ),
  BlocProvider<ReceiveMessageCubit>(
    create: (context) => ReceiveMessageCubit.initial(),
  ),
  BlocProvider<XmppConnectionCubit>(
    create: (context) => XmppConnectionCubit.initial(),
  ),
  BlocProvider<VersionManagementCubit>(
    create: (context) => VersionManagementCubit.initial(),
  ),
];

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:ddone/models/message_model.dart';

class ConversationModel {
  final String user;
  List<MessageModel> sessions;

  ConversationModel({
    required this.user,
    required this.sessions,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'user': user,
      'sessions': sessions.map((x) => x.toMap()).toList(),
    };
  }

  factory ConversationModel.fromMap(Map<String, dynamic> map) {
    return ConversationModel(
      user: map['user'] as String,
      sessions: List<MessageModel>.from(
        (map['sessions'] as List<int>).map<MessageModel>(
          (x) => MessageModel.fromMap(x as Map<String, dynamic>),
        ),
      ),
    );
  }

  String toJson() => json.encode(toMap());

  factory ConversationModel.fromJson(String source) =>
      ConversationModel.fromMap(json.decode(source) as Map<String, dynamic>);
}

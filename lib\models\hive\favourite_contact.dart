import 'package:ddone/constants/hive_id_constants.dart';
import 'package:hive/hive.dart';

part 'favourite_contact.g.dart';

@HiveType(typeId: favouriteContactId)
class FavouriteContact extends HiveObject {
  @HiveField(0)
  final String displayName;

  @HiveField(1)
  final String contactNumber;

  @HiveField(2)
  final bool isLocalContact;

  @HiveField(3)
  final String? uri;

  FavouriteContact({
    required this.displayName,
    required this.contactNumber,
    required this.isLocalContact,
    required this.uri,
  });
}

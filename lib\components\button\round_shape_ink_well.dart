import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/network/network_cubit.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/pop_out_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RoundShapeInkWell extends StatelessWidget {
  final GestureTapCallback? onTap, onLongPress;
  final Widget contentWidget;
  final Color? color;
  final double? size;
  final bool checkNetwork;

  const RoundShapeInkWell({
    required this.contentWidget,
    this.color,
    this.onTap,
    this.onLongPress,
    this.size,
    this.checkNetwork = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;

        double? responsiveSize = size;

        responsiveSize ??= context.responsiveSize<double>(
          moileSize: context.deviceWidth(0.18),
          tabletSize: heightMedium,
          desktopSize: context.deviceHeight(0.07),
          largeScreenSize: context.deviceHeight(0.06),
        );

        return Padding(
          padding: paddingSmall,
          child: BlocBuilder<NetworkCubit, NetworkState>(
            builder: (context, networkState) {
              return InkWell(
                borderRadius: BorderRadius.circular(100.0),
                onTap: () {
                  if (checkNetwork) {
                    if (networkState is NetworkDisconnected) {
                      showSnackBarWithText(
                        context,
                        'Disconnected to network',
                      );

                      return;
                    }
                  }
                  if (onTap != null) onTap!();
                },
                onLongPress: onLongPress,
                child: Ink(
                  height: responsiveSize,
                  width: responsiveSize,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: color ?? colorTheme.roundShapeInkWellColor,
                  ),
                  child: contentWidget,
                ),
              );
            },
          ),
        );
      },
    );
  }
}

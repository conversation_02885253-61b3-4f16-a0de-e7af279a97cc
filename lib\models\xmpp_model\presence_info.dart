class PresenceInfo {
  final String jid;
  final String affiliation;
  final String role;
  final String nick;
  // final String type;
  // final String time;
  // final String msgId;
  // final String grpaffiliation;

  PresenceInfo(
    this.jid,
    this.affiliation,
    this.role,
    this.nick,
    // this.type,
    // this.time,
    // this.msgId,
    // this.grpSender,
  );

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is PresenceInfo && runtimeType == other.runtimeType && jid == other.jid;

  @override
  int get hashCode => jid.hashCode;

  @override
  String toString() {
    return 'Jid: $jid, Affiliation: $affiliation, Role: $role, Nick: $nick';
  }
}

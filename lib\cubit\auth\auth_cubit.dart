import 'package:bloc/bloc.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/chat_recent/chat_recent_cubit.dart';
import 'package:ddone/cubit/common/network/network_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/delete_bookmark/delete_bookmark_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/invitation/invitation_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/cubit/messaging/messages_cubit.dart';
import 'package:ddone/cubit/messaging/receive_message_cubit.dart';
import 'package:ddone/cubit/update/chat_ui_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/cubit/update/info_cubit.dart';
import 'package:ddone/cubit/version_management/version_management_cubit.dart';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/cubit/xmpp/connection/xmpp_connection_cubit.dart';
import 'package:ddone/mixins/api_handler.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/repositories/auth_repository.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/services/firebase_service.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:ddone/cubit/members_presence/members_presence_cubit.dart';
import 'package:moxxmpp/moxxmpp.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> with ApiHandler, PrefsAware {
  final Dio dio;
  final FirebaseService firebaseService;
  AuthRepository? authRepository;

  AuthCubit._({
    AuthState? state,
  })  : dio = sl.get<Dio>(),
        firebaseService = sl.get<FirebaseService>(),
        super(
          state ?? const AuthInitial(),
        );

  factory AuthCubit.initial({AuthState? state}) {
    //the case of is register only happen when you mock the cubit in test case
    if (sl.isRegistered<AuthCubit>()) {
      return sl.get<AuthCubit>();
    }

    return AuthCubit._(state: state);
  }

  void setAuthRepo(AuthRepository authRepository) {
    this.authRepository = authRepository;
  }

  Future<void> logInWithUrl({
    required String apiUrl,
    required VoipCubit voipCubit,
    required HomeCubit homeCubit,
    required AuthCubit authCubit,
    required ContactsCubit contactsCubit,
    required LoginCubit loginCubit,
    required InvitationCubit invitationCubit,
    required MessagesCubit messagesCubit,
    required MamListCubit mamListCubit,
    required BookmarkListCubit bookmarkListCubit,
    required PresencesCubit presencesCubit,
    required ChatRecentCubit chatRecentCubit,
    required ChatUiCubit chatUiCubit,
    required DeleteBookmarkCubit deleteBookmarkCubit,
    required GroupUiCubit groupUiCubit,
    required ReceiveMessageCubit receiveMessageCubit,
    required XmppConnectionCubit xmppConnectionCubit,
    required VersionManagementCubit versionManagementCubit,
    required NetworkCubit networkCubit,
  }) async {
    // Check network connectivity first before any QR validation
    if (networkCubit.state is NetworkDisconnected) {
      EasyLoadingService().showErrorWithText('Network error. Please check your internet connection.');
      return;
    }

    final hiveService = sl.get<HiveService>();

    String sipNumber;
    String sipProxy;
    String sipDisplayName;
    String sipDomain;
    String sipSecret;
    String sipContactUrlBase;
    String sipContactUrlParams;
    String sipWsUrl;
    String pnUrl;

    if (apiUrl.isEmpty || apiUrl.trim().isEmpty) {
      EasyLoadingService().showErrorWithText('No QR code detected. Please try scanning again.');
      return;
    }

    if (apiUrl == 'error') {
      EasyLoadingService().showErrorWithText('Image does not contain QR code');
      return;
    }

    if (!apiUrl.contains('dir.uc1.dotdashtech.com')) {
      EasyLoadingService().showErrorWithText('Invalid QR code. Please scan a valid QR code.');

      return;
    }

    setAuthRepo(
      AuthRepository(
        dio,
        baseUrl: apiUrl,
      ),
    );

    EasyLoadingService().showLoadingWithText('Registering...');

    try {
      final response = await authRepository!.logIn();
      sipNumber = response.userName!;
      sipProxy = response.proxy!;
      sipDisplayName = response.displayName!;
      sipDomain = response.domain!;
      sipSecret = response.password!;
      sipContactUrlBase = response.urlContactBase!;
      sipContactUrlParams = response.urlContactParams!;
      sipWsUrl = response.urlSunaj ?? '';
      pnUrl = response.urlPn ?? '';

      if (!pnUrl.endsWith('/')) {
        pnUrl += '/';
      }

      emit(
        AuthRegistering(
          sipDomain: sipDomain,
          sipName: sipDisplayName,
          sipNumber: sipNumber,
          sipProxy: sipProxy,
          sipSecret: sipSecret,
          sipContactUrlBase: sipContactUrlBase,
          sipContactUrlParams: sipContactUrlParams,
          sipWsUrl: sipWsUrl,
        ),
      );

      await homeCubit.initRtcClient(
        authCubit: authCubit,
      );

      await Future.delayed(const Duration(seconds: 5));

      if (voipCubit.state is! VoipSipRegistered) {
        EasyLoadingService().dismissLoading();
        EasyLoadingService().showErrorWithText('Cannot connect to server. Please check your internet connection.');
        emit(const AuthFail());
        return;
      }

      await storeLoginCredential(
          sipUsername: sipDisplayName,
          sipProxy: sipProxy,
          sipSecret: sipSecret,
          sipDomain: sipDomain,
          sipNumber: sipNumber,
          sipContactUrlBase: sipContactUrlBase,
          sipContactUrlParams: sipContactUrlParams,
          sipWsUrl: sipWsUrl,
          pnUrl: pnUrl);

      await Future.wait([
        hiveService.registerUniqueHiveServices(sipNumber, sipDomain),
        firebaseService.storeFcmToken(
          sipNumber: sipNumber,
          sipDomain: sipDomain,
          pnUrl: pnUrl,
        ),
        versionManagementCubit.updateUser(),
      ]);

      //TODO: add splash screen in future
      contactsCubit.initHiveBoxListener();
      contactsCubit.init(sipContactUrlBase);

      emit(
        AuthSuccess(
          displaySipName: sipDisplayName,
          displaySipDomain: sipDomain,
          displaySipNumber: sipNumber,
        ),
      );

      // xmppConnectionCubit.connect();

      loginCubit.authenticateUser(
        invitationCubit: invitationCubit,
        messagesCubit: messagesCubit,
        mamListCubit: mamListCubit,
        bookmarkListCubit: bookmarkListCubit,
        presencesCubit: presencesCubit,
        chatRecentCubit: chatRecentCubit,
        contactsCubit: contactsCubit,
        chatUiCubit: chatUiCubit,
        loginCubit: loginCubit,
        groupUiCubit: groupUiCubit,
        deleteBookmarkCubit: deleteBookmarkCubit,
        receiveMessageCubit: receiveMessageCubit,
      );

      // contactsCubit.getContactList();
    } catch (error) {
      EasyLoadingService().dismissLoading();

      if (error is DioException) {
        final statusCode = error.response?.statusCode;

        if (statusCode == 401) {
          EasyLoadingService().showErrorWithText('QR code has expired. Please scan a new QR code.');
        } else if (statusCode == 403 || statusCode == 404) {
          EasyLoadingService().showErrorWithText('Invalid QR code. Please scan a valid QR code.');
        } else {
          EasyLoadingService().showErrorWithText('Login failed. Please try using a different QR code.');
        }
      } else {
        EasyLoadingService().showErrorWithText('Network error. Please check your internet connection.');
      }

      log.e('Login with Url Failed', error: error);
      emit(const AuthFail());
    }
  }

  Future<void> manualLogIn({
    required HomeCubit homeCubit,
    required VoipCubit voipCubit,
    required AuthCubit authCubit,
    required ContactsCubit contactsCubit,
    required LoginCubit loginCubit,
    required InvitationCubit invitationCubit,
    required MessagesCubit messagesCubit,
    required MamListCubit mamListCubit,
    required BookmarkListCubit bookmarkListCubit,
    required PresencesCubit presencesCubit,
    required ChatRecentCubit chatRecentCubit,
    required ChatUiCubit chatUiCubit,
    required DeleteBookmarkCubit deleteBookmarkCubit,
    required GroupUiCubit groupUiCubit,
    required ReceiveMessageCubit receiveMessageCubit,
    required XmppConnectionCubit xmppConnectionCubit,
    required VersionManagementCubit versionManagementCubit,
    required String sipNumber,
    required String sipDomain,
    required String sipProxy,
    required String sipSecret,
    required String sipContactUrlBase,
    required String sipContactUrlParams,
    required String sipWsUrl,
    required String pnUrl,
  }) async {
    emit(AuthManualRegistering(
      sipName: sipNumber,
      sipNumber: sipNumber,
      sipDomain: sipDomain,
      sipProxy: sipProxy,
      sipSecret: sipSecret,
      sipContactUrlBase: sipContactUrlBase,
      sipContactUrlParams: sipContactUrlParams,
      sipWsUrl: sipWsUrl,
    ));
    await homeCubit.initRtcClient(
      authCubit: authCubit,
    );

    await Future.delayed(const Duration(seconds: 3));

    if (voipCubit.state is VoipSipRegistered) {
      final hiveService = sl.get<HiveService>();

      await storeLoginCredential(
        sipUsername: sipNumber,
        sipProxy: sipProxy,
        sipSecret: sipSecret,
        sipDomain: sipDomain,
        sipNumber: sipNumber,
        sipContactUrlBase: sipContactUrlBase,
        sipContactUrlParams: sipContactUrlParams,
        sipWsUrl: sipWsUrl,
        pnUrl: pnUrl,
      );
      await Future.wait([
        hiveService.registerUniqueHiveServices(sipNumber, sipDomain),
        versionManagementCubit.updateUser(),
      ]);

      //TODO: add splash screen in future
      // contactsCubit.initHiveBoxListener();
      contactsCubit.init(sipContactUrlBase);

      emit(
        AuthSuccess(
          displaySipName: sipNumber,
          displaySipDomain: sipDomain,
          displaySipNumber: sipNumber,
        ),
      );

      // xmppConnectionCubit.connect();
      loginCubit.authenticateUser(
        invitationCubit: invitationCubit,
        messagesCubit: messagesCubit,
        mamListCubit: mamListCubit,
        bookmarkListCubit: bookmarkListCubit,
        presencesCubit: presencesCubit,
        chatRecentCubit: chatRecentCubit,
        contactsCubit: contactsCubit,
        chatUiCubit: chatUiCubit,
        loginCubit: loginCubit,
        groupUiCubit: groupUiCubit,
        deleteBookmarkCubit: deleteBookmarkCubit,
        receiveMessageCubit: receiveMessageCubit,
      );
    } else {
      emit(const AuthFail());
    }
  }

  Future<void> storeLoginCredential({
    required String sipUsername,
    required String sipProxy,
    required String sipSecret,
    required String sipNumber,
    required String sipDomain,
    required String sipContactUrlBase,
    required String sipContactUrlParams,
    required String sipWsUrl,
    required String pnUrl,
  }) async {
    await Future.wait([
      prefs.setString(CacheKeys.sipProxy, sipProxy),
      prefs.setString(CacheKeys.sipDomain, sipDomain),
      prefs.setString(CacheKeys.sipNumber, sipNumber),
      prefs.setString(CacheKeys.sipName, sipUsername),
      prefs.setString(CacheKeys.sipPwd, sipSecret),
      prefs.setString(CacheKeys.sipContactUrlBase, sipContactUrlBase),
      prefs.setString(CacheKeys.sipContactUrlParams, sipContactUrlParams),
      prefs.setString(CacheKeys.sipWsUrl, sipWsUrl),
      prefs.setString(CacheKeys.pnUrl, pnUrl),
    ]);
  }

  void autoLogin({
    required HomeCubit homeCubit,
    required VoipCubit voipCubit,
    required AuthCubit authCubit,
    required ContactsCubit contactsCubit,
    required LoginCubit loginCubit,
    required InvitationCubit invitationCubit,
    required MessagesCubit messagesCubit,
    required MamListCubit mamListCubit,
    required BookmarkListCubit bookmarkListCubit,
    required PresencesCubit presencesCubit,
    required ChatRecentCubit chatRecentCubit,
    required ChatUiCubit chatUiCubit,
    required DeleteBookmarkCubit deleteBookmarkCubit,
    required GroupUiCubit groupUiCubit,
    required ReceiveMessageCubit receiveMessageCubit,
    required XmppConnectionCubit xmppConnectionCubit,
    required VersionManagementCubit versionManagementCubit,
  }) async {
    final hiveService = sl.get<HiveService>();

    String? sipProxy = prefs.getString(CacheKeys.sipProxy);
    String? sipDomain = prefs.getString(CacheKeys.sipDomain);
    String? sipNumber = prefs.getString(CacheKeys.sipNumber);
    String? sipDisplayName = prefs.getString(CacheKeys.sipName);
    String? sipPwd = prefs.getString(CacheKeys.sipPwd);
    String? sipContactUrlBase = prefs.getString(CacheKeys.sipContactUrlBase);
    String? sipContactUrlParams = prefs.getString(CacheKeys.sipContactUrlParams);
    String? sipWsUrl = prefs.getString(CacheKeys.sipWsUrl);
    String? pnUrl = prefs.getString(CacheKeys.pnUrl);

    if (sipProxy == null) {
      return;
    }

    if (state is AuthSuccess) {
      return;
    }

    emit(
      AuthRegistering(
        sipDomain: sipDomain!,
        sipName: sipDisplayName!,
        sipNumber: sipNumber!,
        sipProxy: sipProxy,
        sipSecret: sipPwd!,
        sipContactUrlBase: sipContactUrlBase!,
        sipContactUrlParams: sipContactUrlParams!,
        sipWsUrl: sipWsUrl!,
      ),
    );

    try {
      await homeCubit.initRtcClient(
        authCubit: authCubit,
      );

      //TODO: why need listener
      // Home.sipRegistered.addListener(() async {

      await Future.wait([
        hiveService.registerUniqueHiveServices(sipNumber, sipDomain),
        firebaseService.storeFcmToken(
          sipNumber: sipNumber,
          sipDomain: sipDomain,
          pnUrl: pnUrl!,
        ),
        versionManagementCubit.updateUser(),
      ]);

      //TODO: add splash screen in future
      contactsCubit.initHiveBoxListener();
      contactsCubit.init(sipContactUrlBase);

      emit(
        AuthSuccess(
          displaySipName: sipDisplayName,
          displaySipDomain: sipDomain,
          displaySipNumber: sipNumber,
        ),
      );

      // xmppConnectionCubit.connect();

      loginCubit.authenticateUser(
        invitationCubit: invitationCubit,
        messagesCubit: messagesCubit,
        mamListCubit: mamListCubit,
        bookmarkListCubit: bookmarkListCubit,
        presencesCubit: presencesCubit,
        chatRecentCubit: chatRecentCubit,
        contactsCubit: contactsCubit,
        chatUiCubit: chatUiCubit,
        loginCubit: loginCubit,
        groupUiCubit: groupUiCubit,
        deleteBookmarkCubit: deleteBookmarkCubit,
        receiveMessageCubit: receiveMessageCubit,
      );
    } on Exception catch (e) {
      log.e('Auto Login Failed', error: e);
      emit(const AuthFail());
      resetPrefs();
    } catch (e) {
      log.e('Auto Login Failed', error: e);
      emit(const AuthFail());
      resetPrefs();
    }
  }

  void logout({
    required HomeCubit homeCubit,
    required VoipCubit voipCubit,
    required ContactsCubit contactsCubit,
    required LoginCubit loginCubit,
    required MamListCubit mamListCubit,
    required BookmarkListCubit bookmarkListCubit,
    required InfoCubit infoCubit,
    required XmppConnectionCubit xmppConnectionCubit,
  }) async {
    final String? sipDomain = prefs.getString(CacheKeys.sipDomain);
    final String? sipNumber = prefs.getString(CacheKeys.sipNumber);
    final String? pnUrl = prefs.getString(CacheKeys.pnUrl);
    if (sipDomain == null || sipNumber == null) {
      EasyLoadingService().showInfoWithText('No account to log out from');
      return;
    }

    await voipCubit.logout();
    await Future.delayed(const Duration(seconds: 1));
    if (voipCubit.state is VoipSipUnregistered) {
      emit(const AuthLogOutSuccess());
      await firebaseService.deleteFcmToken(sipNumber: sipNumber, sipDomain: sipDomain, pnUrl: pnUrl!);
      resetPrefs();

      contactsCubit.clearContacts(bookmarkListCubit: bookmarkListCubit);

      // print('bookmarkListCubitbookmarkListCubitbookmarkListCubit.state: ${bookmarkListCubit.state.bookmarkList}');
      // print('bookmarkListCubitbookmarkListCubitbookmarkListCubit.state: ${bookmarkListCubit.state}');

      mamListCubit.clearAllContactList();

      MamListCubit.stanza.clear();
      MamListCubit.test.clear();

      // print('authmamListCubit.state.mamgroupList ${mamListCubit.state.mamgroupList}');

      infoCubit.removeInfo();

      final hiveService = sl.get<HiveService>();

      hiveService.initialHiveBox();

      // xmppConnectionCubit.disconnect();

      if (loginCubit.state is LoginAuthenticated) {
        final loginAuthenticatedState = loginCubit.state as LoginAuthenticated;
        final presenceUnsubscribe =
            loginAuthenticatedState.connection.getManagerById<PresenceManager>(presenceManager)!;
        await presenceUnsubscribe.sendUnavailablePresence();

        await loginAuthenticatedState.connection.disconnect();

        loginCubit.logout();
      }

      EasyLoadingService().showSuccessWithText('Log out successful');
    } else {
      log.e('Logout failed');
      EasyLoadingService().showErrorWithText('Log out failed');
    }
  }
}

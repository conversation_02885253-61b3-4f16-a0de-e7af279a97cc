import 'dart:async';

import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/pop_out_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';

class VideoChatBubble extends StatefulWidget {
  final String videoUrl;
  const VideoChatBubble({required this.videoUrl, super.key});

  @override
  State<VideoChatBubble> createState() => _VideoChatBubbleState();
}

class _VideoChatBubbleState extends State<VideoChatBubble> {
  Player? _player;
  VideoController? _videoController;
  bool _showPlayer = false;

  @override
  void initState() {
    super.initState();

    _player = Player();
    _videoController = VideoController(_player!);

    _initializeVideoPlayer();
  }

  Future<void> _initializeVideoPlayer() async {
    try {
      if (widget.videoUrl.isEmpty) {
        throw Exception('Invalid video URL: ${widget.videoUrl}');
      } else {
        Uri? videoUri = Uri.tryParse(widget.videoUrl);
        if (videoUri == null || !videoUri.isAbsolute || !['http', 'https'].contains(videoUri.scheme)) {
          throw Exception('Invalid video URL: ${widget.videoUrl}');
        }
      }
      await _player!.open(Media(widget.videoUrl), play: false);

      if (mounted) {
        setState(() {
          _showPlayer = true;
        });
      }
    } catch (e) {
      log.e('Error initializing video', error: e);
      if (mounted) {
        setState(() {
          _showPlayer = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _player?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;

        return GestureDetector(
          onTap: () {
            // Ensure videoUrl is passed
            videoPreviewDialog(
              context: context,
              videoUrl: widget.videoUrl,
            );
          },
          child: SizedBox(
            height: 300,
            width: context.deviceWidth(0.5),
            child: _showPlayer
                ? Stack(
                    children: [
                      // Use Media Kit Video widget
                      if (_videoController != null) Video(controller: _videoController!),
                      Positioned(
                        bottom: 125,
                        left: 50,
                        right: 50,
                        child: Icon(
                          Icons.play_circle_outline_rounded,
                          size: 50,
                          color: colorTheme.onPrimaryColor.withOpacity(0.5),
                        ),
                      ),
                    ],
                  )
                : const Center(child: CircularProgressIndicator()),
          ),
        );
      },
    );
  }
}

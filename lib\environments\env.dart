import 'dart:io';

import 'package:flutter/foundation.dart';

enum BuildFlavor {
  development,
  test,
  staging,
  production,
}

BuildEnvironment? get env => _env;
BuildEnvironment? _env;

class BuildEnvironment {
  static BuildFlavor? appFlavor;

  final BuildFlavor flavor;

  final String appName;
  final String apiUrl;
  final String webSocketUrl;

  final String pnUrl;
  final String minioUrl;
  final String audioSource;
  final String incomingCallAudioSource;
  final String contactUsUrl;
  final String appVersion;
  final String appBuildNumber;
  final String xmppAccount;
  final String xmppAccountSec;
  final String mongooseimXmppHost;
  final String mongooseimXmppPort;
  final String endpointmgtUrl;
  final String checkAppUpdatePath;
  final String updateUserPath;
  final String storeCallHistoryPath;
  final String retrieveCallHistoryPath;
  final String userGuideUrl;

  BuildEnvironment._init({
    required this.appName,
    required this.flavor,
    required this.apiUrl,
    required this.webSocketUrl,
    required this.pnUrl,
    required this.minioUrl,
    required this.audioSource,
    required this.incomingCallAudioSource,
    required this.contactUsUrl,
    required this.appVersion,
    required this.appBuildNumber,
    required this.xmppAccount,
    required this.xmppAccountSec,
    required this.mongooseimXmppHost,
    required this.mongooseimXmppPort,
    required this.endpointmgtUrl,
    required this.checkAppUpdatePath,
    required this.updateUserPath,
    required this.storeCallHistoryPath,
    required this.retrieveCallHistoryPath,
    required this.userGuideUrl,
  });

  static void init({
    required BuildFlavor flavor,
    required String appName,
    required String apiUrl,
    required String webSocketUrl,
    required String pnUrl,
    required String minioUrl,
    required String audioSource,
    required String incomingCallAudioSource,
    required String contactUsUrl,
    required String appVersion,
    required String appBuildNumber,
    required String xmppAccount,
    required String xmppAccountSec,
    required String mongooseimXmppHost,
    required String mongooseimXmppPort,
    required String endpointmgtUrl,
    required String checkAppUpdatePath,
    required String updateUserPath,
    required String storeCallHistoryPath,
    required String retrieveCallHistoryPath,
    required String userGuideUrl,
  }) {
    if (isUnitTest() || _env == null) {
      _env = BuildEnvironment._init(
        appName: appName,
        flavor: flavor,
        apiUrl: apiUrl,
        webSocketUrl: webSocketUrl,
        pnUrl: pnUrl,
        minioUrl: minioUrl,
        audioSource: audioSource,
        incomingCallAudioSource: incomingCallAudioSource,
        contactUsUrl: contactUsUrl,
        appVersion: appVersion,
        appBuildNumber: appBuildNumber,
        xmppAccount: xmppAccount,
        xmppAccountSec: xmppAccountSec,
        mongooseimXmppHost: mongooseimXmppHost,
        mongooseimXmppPort: mongooseimXmppPort,
        endpointmgtUrl: endpointmgtUrl,
        checkAppUpdatePath: checkAppUpdatePath,
        updateUserPath: updateUserPath,
        storeCallHistoryPath: storeCallHistoryPath,
        retrieveCallHistoryPath: retrieveCallHistoryPath,
        userGuideUrl: userGuideUrl,
      );
    }
  }
}

bool isDevMode({bool? mockKReleaseMode}) {
  return !(mockKReleaseMode ?? kReleaseMode);
}

bool isUnitTest() => Platform.environment.containsKey('FLUTTER_TEST');

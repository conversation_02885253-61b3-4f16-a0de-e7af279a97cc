part of 'members_presence_cubit.dart';

sealed class PresencesState extends Equatable {
  final Map<String, List<PresenceInfo>> presenceList;
  const PresencesState(this.presenceList);

  @override
  List<Object> get props => [presenceList];
}

final class PresencesInitial extends PresencesState {
  const PresencesInitial(super.presenceList);
}

final class PresencesLoading extends PresencesState {
  const PresencesLoading(super.presenceList);
}

final class PresencesLoaded extends PresencesState {
  const PresencesLoaded(super.presenceMap);
}

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:logging/logging.dart' as logging;

// Singleton Logger instance
late final Logger log;
bool isLogInitialized = false;

// Initialize the logger (call this in your main() function)
Future<void> initializeLogger() async {
  if (isLogInitialized) {
    return;
  }

  LogOutput output;

  output = ConsoleOutput(); // by default console output
  if (!kDebugMode) {
    /// Uncomment to write to file in Production mode
    /// - it will be written to the user's document directory.
    // final directory = await getApplicationDocumentsDirectory();
    // final file = File('${directory.path}/ddone_app_logs.txt');
    // output = FileOutput(file: file);
  }

  log = Logger(
    level: kDebugMode ? Level.trace : Level.trace, // More restrictive level in production
    filter: kDebugMode ? null : ProductionFilter(),
    printer: PrettyPrinter(
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
        colors: true,
        printEmojis: false,
        methodCount: 1,
        errorMethodCount: 50,
        lineLength: 100,
        excludeBox: {
          Level.trace: true,
        }),
    // printer: kDebugMode
    //     ? PrettyPrinter(
    //         dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
    //         colors: true,
    //         printEmojis: false,
    //         methodCount: 1,
    //         errorMethodCount: 50,
    //         lineLength: 100,
    //         excludeBox: {
    //             Level.trace: true,
    //           })
    //     : SimplePrinter(colors: false), // Simple printer for file output
    output: output,
  );
  isLogInitialized = true;

  // we got packages that use logging package, uncomment to listen to it and log it
  // logging.Logger.root.level = kDebugMode ? logging.Level.ALL : logging.Level.INFO;
  // logging.Logger.root.onRecord.listen((record) {
  //   debugPrint('EXTERNAL ${record.loggerName}: ${record.time} : ${record.level.name}: ${record.message}');
  // });
}

// Custom file output class
class FileOutput extends LogOutput {
  final File file;

  FileOutput({required this.file});

  @override
  void output(OutputEvent event) {
    try {
      final timestamp = DateTime.now().toIso8601String();
      final logLines = event.lines.map((line) => '[$timestamp] $line').join('\n');

      // Write to file (append mode) with error handling
      file.writeAsStringSync('$logLines\n', mode: FileMode.append);
    } catch (e) {
      // Fallback to console if file writing fails
      print('Logger file write failed: $e');
      for (final line in event.lines) {
        print(line);
      }
    }
  }
}

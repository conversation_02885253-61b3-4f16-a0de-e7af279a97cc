import 'dart:async';
import 'dart:collection';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

import 'logger_util.dart';
import 'package:ddone/services/app_tracking_transparency_service.dart';

class PermissionUtil {
  // Queue to handle sequential permission requests
  static final Queue<Future<bool> Function()> _permissionQueue = Queue<Future<bool> Function()>();
  static bool _isProcessingQueue = false;

  /// Add a permission request to the queue and process it sequentially
  static Future<bool> _queuePermissionRequest(
    Future<bool> Function() requestFunction,
  ) async {
    final completer = Completer<bool>();

    // Add the request to queue
    _permissionQueue.add(() async {
      try {
        final result = await requestFunction();
        completer.complete(result);
        return result;
      } catch (error) {
        completer.completeError(error);
        rethrow;
      }
    });

    // Start processing if not already processing
    if (!_isProcessingQueue) {
      _processQueueSequentially();
    }

    return completer.future;
  }

  /// Process queue items sequentially
  static Future<void> _processQueueSequentially() async {
    if (_isProcessingQueue) return;
    _isProcessingQueue = true;

    while (_permissionQueue.isNotEmpty) {
      final requestFunction = _permissionQueue.removeFirst();
      try {
        await requestFunction();
      } catch (e) {
        // Log error but continue processing queue
        log.e('Permission request error: $e');
      }
    }

    _isProcessingQueue = false;
  }

  /// A reusable function to handle permission requests with proper dialog handling
  /// Returns true if permission is granted, false otherwise
  static Future<bool> handlePermissionRequest(
    Permission permission,
    BuildContext context, {
    String? customTitle,
    String? customMessage,
  }) async {
    return _queuePermissionRequest(() async {
      log.t('Requesting for $permission');
      final status = await permission.request();

      if (status.isGranted || status.isLimited) {
        return true;
      }

      final permissionInfo = _getPermissionInfo(permission);
      final title = customTitle ?? permissionInfo['title'] as String;
      final baseMessage = customMessage ?? permissionInfo['message'] as String;

      String message = baseMessage;
      if (status.isPermanentlyDenied) {
        message = 'Permission has been permanently denied. Please enable it in Settings to continue.';
      } else if (status.isDenied) {
        message = '$baseMessage\n\nPlease enable it in Settings.';
      }

      if (!context.mounted) return false;

      final shouldOpenSettings = await _showPermissionDialog(
        context: context,
        title: title,
        message: message,
        showSettingsButton: status.isPermanentlyDenied || status.isDenied,
      );

      if (shouldOpenSettings) {
        await openAppSettings();
      }

      return false;
    });
  }

  /// Handle custom permission requests that don't use the standard Permission enum
  /// [requestFunction] should return a value that can be evaluated for permission status
  /// [checkFunction] optional function to check current permission status
  /// [openSettingsFunction] optional custom function to open settings (defaults to openAppSettings)
  /// [evaluateResult] function to determine if the result indicates permission was granted
  static Future<bool> handleCustomPermissionRequest<T>(
    Future<T> Function() requestFunction,
    BuildContext context, {
    required String title,
    required String message,
    Future<T> Function()? checkFunction,
    Future<void> Function()? openSettingsFunction,
    bool Function(T result)? evaluateResult,
    bool alwaysShowDialog = false,
  }) async {
    return _queuePermissionRequest(() async {
      // Default evaluation function - treats true, 'granted', 'authorized' as success
      bool defaultEvaluate(T result) {
        if (result is bool) return result;
        if (result is String) {
          return result.toLowerCase() == 'granted' ||
              result.toLowerCase() == 'authorized' ||
              result.toLowerCase() == 'true';
        }
        return result != null;
      }

      final evaluate = evaluateResult ?? defaultEvaluate;

      // Check current status if check function is provided
      if (checkFunction != null) {
        final currentStatus = await checkFunction();
        if (evaluate(currentStatus)) return true;
      }

      // Request permission
      final result = await requestFunction();

      if (evaluate(result)) {
        return true;
      }

      // Show dialog if permission denied or if alwaysShowDialog is true
      if (!context.mounted) return false;

      final shouldOpenSettings = await _showPermissionDialog(
        context: context,
        title: title,
        message: message,
        showSettingsButton: true,
      );

      if (shouldOpenSettings) {
        if (openSettingsFunction != null) {
          await openSettingsFunction();
        } else {
          await openAppSettings();
        }
      }

      return false;
    });
  }

  /// Convenience overload for boolean-returning functions
  static Future<bool> handleCustomBoolPermissionRequest(
    Future<bool> Function() requestFunction,
    BuildContext context, {
    required String title,
    required String message,
    Future<bool> Function()? checkFunction,
    Future<void> Function()? openSettingsFunction,
    bool alwaysShowDialog = false,
  }) {
    return handleCustomPermissionRequest<bool>(
      requestFunction,
      context,
      title: title,
      message: message,
      checkFunction: checkFunction,
      openSettingsFunction: openSettingsFunction,
      evaluateResult: (result) => result,
      alwaysShowDialog: alwaysShowDialog,
    );
  }

  /// Example of how to use custom permission with CallKit
  /// You can uncomment and modify this once you have the package imported
  /*
  static Future<bool> requestCallKitFullIntentPermission(BuildContext context) {
    return handleCustomPermissionRequest<dynamic>(
      () => FlutterCallkitIncoming.requestFullIntentPermission(),
      context,
      title: 'Full Intent Permission Required',
      message: 'This app needs permission to display incoming calls over other apps (Android 14+). This ensures you can receive calls even when the app is not active.',
      evaluateResult: (result) {
        // Customize this based on what the actual method returns
        if (result is bool) return result;
        if (result is String) return result.toLowerCase() == 'granted';
        return result != null;
      },
      // checkFunction: () => FlutterCallkitIncoming.hasFullIntentPermission(), // if available
    );
  }
  */

  /// Clear the permission queue (use with caution)
  static void clearPermissionQueue() {
    _permissionQueue.clear();
    _isProcessingQueue = false;
  }

  /// Get the current queue length (for debugging)
  static int get queueLength => _permissionQueue.length;

  /// Check if queue is currently being processed
  static bool get isProcessingQueue => _isProcessingQueue;

  /// Show permission dialog and return true if user wants to open settings
  static Future<bool> _showPermissionDialog({
    required BuildContext context,
    required String title,
    required String message,
    required bool showSettingsButton,
  }) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext dialogContext) {
            return AlertDialog(
              title: Text(title),
              content: Text(message),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(dialogContext).pop(false),
                  child: const Text('Cancel'),
                ),
                if (showSettingsButton)
                  TextButton(
                    onPressed: () => Navigator.of(dialogContext).pop(true),
                    child: const Text('Open Settings'),
                  ),
              ],
            );
          },
        ) ??
        false;
  }

  /// Get permission-specific title and message
  static Map<String, String> _getPermissionInfo(Permission permission) {
    switch (permission) {
      case Permission.camera:
        return {
          'title': 'Camera Access Required',
          'message': 'We need camera access to take photos and videos.',
        };

      case Permission.photos:
      case Permission.storage:
        return {
          'title': 'Photos Access Required',
          'message': 'We need access to your photos to select and save images.',
        };

      case Permission.microphone:
        return {
          'title': 'Microphone Access Required',
          'message': 'We need microphone access to pick up audio.',
        };

      case Permission.location:
      case Permission.locationWhenInUse:
        return {
          'title': 'Location Access Required',
          'message': 'We need location access to provide location-based features.',
        };

      case Permission.locationAlways:
        return {
          'title': 'Location Access Required',
          'message': 'We need location access at all times to provide continuous location-based features.',
        };

      case Permission.contacts:
        return {
          'title': 'Contacts Access Required',
          'message': 'We need access to your contacts to help you connect with friends.',
        };

      case Permission.calendar:
        return {
          'title': 'Calendar Access Required',
          'message': 'We need calendar access to schedule and manage events.',
        };

      case Permission.notification:
        return {
          'title': 'Notification Permission Required',
          'message': 'We need permission to send you important notifications.',
        };

      case Permission.phone:
        return {
          'title': 'Phone Access Required',
          'message': 'We need phone access to make calls directly from the app.',
        };

      case Permission.sms:
        return {
          'title': 'SMS Access Required',
          'message': 'We need SMS access to send text messages.',
        };

      case Permission.bluetoothScan:
      case Permission.bluetoothConnect:
      case Permission.bluetooth:
        return {
          'title': 'Bluetooth Access Required',
          'message': 'We need Bluetooth access to connect with nearby devices.',
        };

      case Permission.mediaLibrary:
        return {
          'title': 'Media Library Access Required',
          'message': 'We need access to your media library to manage your music and videos.',
        };

      case Permission.sensors:
        return {
          'title': 'Sensors Access Required',
          'message': 'We need access to device sensors for enhanced functionality.',
        };

      case Permission.speech:
        return {
          'title': 'Speech Recognition Required',
          'message': 'We need speech recognition access to process voice commands.',
        };

      case Permission.ignoreBatteryOptimizations:
        return {
          'title': 'Battery Optimization Required',
          'message': 'Please disable battery optimization to ensure the app works properly in the background.',
        };

      case Permission.criticalAlerts:
        return {
          'title': 'Critical Alerts Required',
          'message': 'We need permission to send critical alerts for important updates.',
        };

      case Permission.accessMediaLocation:
        return {
          'title': 'Media Location Access Required',
          'message': 'We need access to media location information to organize your photos.',
        };

      case Permission.activityRecognition:
        return {
          'title': 'Activity Recognition Required',
          'message': 'We need activity recognition to track your fitness activities.',
        };

      case Permission.manageExternalStorage:
        return {
          'title': 'Storage Management Required',
          'message': 'We need storage management permission to organize your files.',
        };

      case Permission.systemAlertWindow:
        return {
          'title': 'System Alert Window Required',
          'message': 'We need permission to display alerts over other apps.',
        };

      case Permission.requestInstallPackages:
        return {
          'title': 'Install Packages Required',
          'message': 'We need permission to install app updates.',
        };

      case Permission.accessNotificationPolicy:
        return {
          'title': 'Notification Policy Access Required',
          'message': 'We need access to notification policy to manage Do Not Disturb settings.',
        };

      default:
        return {
          'title': 'Permission Required',
          'message': 'This feature requires permission to function properly.',
        };
    }
  }

  /// Check if permission is granted without requesting
  static Future<bool> isPermissionGranted(Permission permission) async {
    final status = await permission.status;
    return status.isGranted || status.isLimited;
  }

  /// Check multiple permissions at once
  static Future<Map<Permission, bool>> checkMultiplePermissions(
    List<Permission> permissions,
  ) async {
    final result = <Permission, bool>{};
    for (final permission in permissions) {
      result[permission] = await isPermissionGranted(permission);
    }
    return result;
  }

  /// Request App Tracking Transparency permission (iOS only)
  /// This is required by Apple for iOS apps that collect data for tracking
  static Future<bool> requestAppTrackingTransparency(BuildContext context) async {
    return await AppTrackingTransparencyService.showPrePermissionDialog(context);
  }

  /// Check if App Tracking Transparency is authorized
  static Future<bool> isAppTrackingAuthorized() async {
    return await AppTrackingTransparencyService.isTrackingAuthorized();
  }
}

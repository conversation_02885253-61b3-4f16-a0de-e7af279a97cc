import 'package:ddone/constants/value_constants.dart';
import 'package:flutter/material.dart';
import 'package:responsive_framework/responsive_framework.dart';

extension DeviceMediaQuery on BuildContext {
  double deviceWidth([double ratio = 1]) {
    assert(ratio >= 0 && ratio <= 1, 'Ratio must be between 0 to 1');

    return MediaQuery.sizeOf(this).width * ratio;
  }

  double deviceHeight([double ratio = 1]) {
    assert(ratio >= 0 && ratio <= 1, 'Ratio must be between 0 to 1');

    return MediaQuery.sizeOf(this).height * ratio;
  }

  T responsiveSize<T>({
    required T moileSize,
    required T tabletSize,
    required T desktopSize,
    required T largeScreenSize,
  }) {
    if (isMobileSize) return moileSize;
    if (isTabletSize) return tabletSize;
    if (isDesktopSize) return desktopSize;
    return largeScreenSize;
  }

  //Responsive related
  bool get is4kSize => ResponsiveBreakpoints.of(this).breakpoint.name == largeScreen;
  bool get isDesktopSize => ResponsiveBreakpoints.of(this).isDesktop;
  bool get isTabletSize => ResponsiveBreakpoints.of(this).isTablet;
  bool get isMobileSize => ResponsiveBreakpoints.of(this).isMobile;

  bool get isLargerOrEqualTo4kSize => ResponsiveBreakpoints.of(this).largerOrEqualTo(largeScreen);
  bool get isLargerOrEqualToDesktopSize => ResponsiveBreakpoints.of(this).largerOrEqualTo(DESKTOP);
  bool get isLargerOrEqualToTabletSize => ResponsiveBreakpoints.of(this).largerOrEqualTo(TABLET);
  bool get isLargerOrEqualToMobileSize => ResponsiveBreakpoints.of(this).largerOrEqualTo(MOBILE);

  bool get isSmallerOrEqualTo4kSize => ResponsiveBreakpoints.of(this).smallerOrEqualTo(largeScreen);
  bool get isSmallerOrEqualToDesktopSize => ResponsiveBreakpoints.of(this).smallerOrEqualTo(DESKTOP);
  bool get isSmallerOrEqualToTabletSize => ResponsiveBreakpoints.of(this).smallerOrEqualTo(TABLET);
  bool get isSmallerOrEqualToMobileSize => ResponsiveBreakpoints.of(this).smallerOrEqualTo(MOBILE);

  bool get betweenDesktopAnd4k => ResponsiveBreakpoints.of(this).between(DESKTOP, largeScreen);
  bool get betweenTabletAndDesktop => ResponsiveBreakpoints.of(this).between(TABLET, DESKTOP);
  bool get betweenMobileAndTablet => ResponsiveBreakpoints.of(this).between(MOBILE, TABLET);
}

extension CustomTheme on BuildContext {
  TextTheme textStyle() {
    return Theme.of(this).textTheme;
  }

  ColorScheme colorTheme() {
    return Theme.of(this).colorScheme;
  }
}

# Run MSIX Locally

Our `pubspec.yaml` is configure to build msix bundle that is meant to upload to microsoft partner center to let Microsoft sign it. As a result, it is not possible to install the msix bundle locally.

To be able to run the msix bundle locally, you will need to do the following:

1. Comment out the `store: true` line in `pubspec.yaml`.
2. Go through the steps in msix-local-cert.ps1 script to create self-signed certificate.
2. Run `dart run msix:create` to build the msix bundle.
3. If `dart run msix:create` fail with `Error: SignerSign() failed.`, try manually sign the msix bundle using the `signtool.exe` as shown in the `msix-local-cert.ps1` script.
4. You should now be able to install the msix bundle locally.

# Register/Unregister

1. SipRegisteredEvent
2. SipUnRegisteredEvent

# Make a call

1. SipCallingEvent: This is the first event you'll receive after you send the call message to the Janus SIP plugin.

   What it means: Your client has sent the SIP INVITE message to the SIP server. The SIP protocol is now in the "calling" phase, waiting for the other party to respond. This is your cue that the call has been initiated.

2. SipProceedingEvent: This event comes next.

   What it means: The SIP server (or an intermediate proxy) has received your INVITE message and is processing it. It's a provisional response (a 1xx response in SIP, typically 100 Trying) that indicates the server is working on finding the destination. It's a positive acknowledgment that your request was received.

3. SipRingingEvent: This is a crucial event for the user experience.

   What it means: The SIP server has successfully located the called party, and their device (phone, softphone, etc.) is now ringing. The SIP server sends a 180 Ringing provisional response, which <PERSON><PERSON> relays to your client. This is the event that tells your client to play the "ringing" sound to the user.

4. SipAcceptedEvent: This is the call's "answer" event.

   What it means: The person you called has answered the phone. The called party's device sends a 200 OK response to the INVITE message. This event signals that the media session is about to be established.

5. SipProgressEvent: This event can sometimes appear in the flow, but it's not as common as the others.

   What it means: A "progress" event (a 183 Session Progress response) indicates that the call is in an intermediate state where early media (audio or video) may be flowing before the call is officially accepted. This can happen, for example, if the far end is playing a custom ringback tone or announcement. If this event occurs, it will typically come after SipProceedingEvent but before SipRingingEvent or SipAcceptedEvent.

6. SipHangupEvent: This event signifies the end of the call.

   What it means: One of the parties has hung up. This corresponds to a SIP BYE message. This event can happen at any point after the call has been initiated, whether it was hung up before ringing, while ringing, or after being answered. The event will contain a reason for the hangup (e.g., BYE, CANCEL, BUSY, UNAVAILABLE).

# Receive a call

1. SipIncomingCallEvent: This is the first and most important event you will receive.

   What it means: A SIP INVITE message has arrived at the Janus server, addressed to your registered SIP URI. This is the cue for your client to display an incoming call notification to the user. The event will contain information about the caller's URI, display name, and the SDP (Session Description Protocol) offer.

   Your client's action: Display a "new call" notification and the caller's details. You can also play a ringtone for the user.

2. SipProgressEvent or SipProceedingEvent: These events are not always received for an incoming call, but they can occur.

   What they mean: These provisional responses (e.g., 100 Trying or 183 Session Progress) are sent by your client's device to the SIP server. Janus may relay these back to you as an acknowledgment that your client is processing the INVITE. From a user's perspective, these events are not as critical as the SipIncomingCallEvent.

3. SipAcceptedEvent: The user has accepted the call.

   What it means: After the user clicks "answer" on your client's UI, your client sends an answer message to the Janus SIP plugin. Janus responds with a SipAcceptedEvent after the SIP 200 OK message has been successfully exchanged and the media session is ready. This is your cue to switch the UI from "ringing" to "active call" and start the media streams (audio/video).

   Your client's action: When the user clicks "answer," your client creates a new WebRTC PeerConnection, sets the remote SDP from the SipIncomingCallEvent, creates a local SDP answer, and sends it back to Janus in an answer message.

4. SipHangupEvent: The call is over.

   What it means: One of the parties has hung up the call. This corresponds to a SIP BYE message. This event can happen at any stage of the call.

   - Scenario 1: The caller hangs up before you answer. In this case, you will receive SipIncomingCallEvent followed by SipHangupEvent with a CANCEL reason.

   - Scenario 2: You hang up the call. Your client sends a hangup message to Janus, which relays a BYE message to the SIP server. You will then receive a SipHangupEvent from Janus as confirmation.

   - Scenario 3: The caller hangs up after you answer. You will receive a SipHangupEvent with a BYE reason.

   Your client's action: When you receive this event, you should tear down the WebRTC PeerConnection and switch the UI back to an idle state.
import 'package:ddone/utils/string_util.dart';
import 'package:event_bus_plus/event_bus_plus.dart';

class FirebaseEvent extends AppEvent {
  final String category;
  final Map<String, dynamic> data;
  const FirebaseEvent(this.category, this.data);

  @override
  String toString() => 'FirebaseEvent: $category - ${StringUtil.prettyPrint(data)}';

  @override
  List<Object?> get props => [category, data];
}

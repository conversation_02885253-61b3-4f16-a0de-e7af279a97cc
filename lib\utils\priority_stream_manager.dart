import 'dart:async';

import 'package:ddone/utils/logger_util.dart';

/// A manager for stream listeners that executes them based on a priority system.
///
/// This class allows you to subscribe to a stream and attach multiple listeners,
/// each with a specified priority. When the stream emits an event or an error,
/// the corresponding listeners are executed in order of their priority
/// (lower integer value means higher priority).
///
/// Callbacks at the same priority level are executed concurrently, and the manager
/// waits for all of them to complete before proceeding to the next priority level.
///
/// [T] is the type of the data in the stream.
class PriorityStreamManager<T> {
  final StreamController<T> _controller = StreamController.broadcast();
  final List<StreamSubscription> _subscriptions = [];

  /// Map to store data listeners with their priorities.
  final Map<int, List<FutureOr<void> Function(T)>> _listeners = {};

  /// Map to store error listeners with their priorities.
  final Map<int, List<FutureOr<void> Function(dynamic)>> _errorListeners = {};

  /// Adds a listener callback for a given priority.
  ///
  /// The [priority] is an integer where a lower value indicates a higher priority.
  /// The [onData] callback is executed on a stream event.
  /// The optional [onError] callback is executed on a stream error.
  void addListener(
    int priority,
    FutureOr<void> Function(T) onData, {
    FutureOr<void> Function(dynamic)? onError,
  }) {
    _listeners.putIfAbsent(priority, () => []).add(onData);
    if (onError != null) {
      _errorListeners.putIfAbsent(priority, () => []).add(onError);
    }
  }

  /// Removes a specific listener callback for a given priority.
  void removeListener(
    int priority,
    FutureOr<void> Function(T) onData, {
    FutureOr<void> Function(dynamic)? onError,
  }) {
    _listeners[priority]?.remove(onData);
    if (_listeners[priority]?.isEmpty == true) {
      _listeners.remove(priority);
    }

    if (onError != null) {
      _errorListeners[priority]?.remove(onError);
      if (_errorListeners[priority]?.isEmpty == true) {
        _errorListeners.remove(priority);
      }
    }
  }

  /// Subscribes to the [originalStream] and begins processing events and errors.
  void startListening(Stream<T>? originalStream) {
    if (originalStream == null) return;

    // Cancel existing subscriptions before creating new ones
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();

    _subscriptions.add(originalStream.listen(
      (event) async {
        final sortedPriorities = _listeners.keys.toList()..sort();
        for (final priority in sortedPriorities) {
          final callbacks = _listeners[priority];
          if (callbacks == null || callbacks.isEmpty) continue;

          final futures = callbacks.map((callback) async {
            try {
              await callback(event);
            } catch (e) {
              log.t('Error in priority $priority onData callback: $e');
            }
          });
          await Future.wait(futures);
        }
      },
      onError: (error) async {
        final sortedPriorities = _errorListeners.keys.toList()..sort();
        for (final priority in sortedPriorities) {
          final callbacks = _errorListeners[priority];
          if (callbacks == null || callbacks.isEmpty) continue;

          final futures = callbacks.map((callback) async {
            try {
              await callback(error);
            } catch (e) {
              log.t('Error in priority $priority onError callback: $e');
            }
          });
          await Future.wait(futures);
        }
      },
    ));
  }

  /// Cancels all stream subscriptions and clears all listeners.
  void dispose() {
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();
    _listeners.clear();
    _errorListeners.clear();
    _controller.close();
  }

  Stream<T> get stream => _controller.stream;

  /// Checks if there are any listeners registered for a specific [priority].
  bool hasListenersAtPriority(int priority) {
    return (_listeners.containsKey(priority) && _listeners[priority]!.isNotEmpty) ||
        (_errorListeners.containsKey(priority) && _errorListeners[priority]!.isNotEmpty);
  }

  /// Returns a sorted list of all priorities that have active listeners.
  List<int> get activePriorities {
    final priorities = <int>{..._listeners.keys, ..._errorListeners.keys};
    return priorities.toList()..sort();
  }
}

import 'package:flutter_test/flutter_test.dart';

// Helper function to test the attachment description logic
// This replicates the logic from NotificationService._getAttachmentDescription
String getAttachmentDescription(String messageType, String message) {
  if (messageType == 'Text') {
    // Check if the text message contains a URL that points to a file
    final String trimmedBody = message.trim();
    final Uri? fileUri = Uri.tryParse(trimmedBody);

    // Only process if it's a valid URL
    if (fileUri != null && fileUri.hasScheme && fileUri.pathSegments.isNotEmpty) {
      final RegExp imageExtensions = RegExp(r'\.(jpg|jpeg|png|gif|bmp|webp|svg|ico)$', caseSensitive: false);
      final RegExp videoExtensions = RegExp(r'\.(mp4|mkv|mov|avi|wmv|flv|webm|m4v)$', caseSensitive: false);
      final isVideo = videoExtensions.hasMatch(trimmedBody);
      final isImage = imageExtensions.hasMatch(trimmedBody);

      if (isImage) {
        return 'Image';
      } else if (isVideo) {
        return 'Video';
      } else {
        return 'Document';
      }
    }

    // If it's not a file URL, return the original message
    return message;
  }

  // For non-text message types, check the message content
  final String trimmedBody = message.trim();
  final Uri? fileUri = Uri.tryParse(trimmedBody);
  final RegExp imageExtensions = RegExp(r'\.(jpg|jpeg|png|gif|bmp|webp|svg|ico)$', caseSensitive: false);
  final RegExp videoExtensions = RegExp(r'\.(mp4|mkv|mov|avi|wmv|flv|webm|m4v)$', caseSensitive: false);
  final isVideo = videoExtensions.hasMatch(trimmedBody);
  final isImage = imageExtensions.hasMatch(trimmedBody);

  if (isImage) {
    return 'Image';
  } else if (isVideo) {
    return 'Video';
  } else if (fileUri != null && fileUri.hasScheme && fileUri.pathSegments.isNotEmpty) {
    return 'Document';
  }

  return messageType;
}

void main() {
  group('NotificationService Attachment Description', () {
    group('getAttachmentDescription', () {
      test('should return original message for regular text', () {
        const message = 'Hello, this is a regular text message';
        final result = getAttachmentDescription('Text', message);
        expect(result, equals(message));
      });

      test('should return "Image" for image URLs', () {
        const imageUrls = [
          'https://example.com/image.jpg',
          'https://example.com/photo.jpeg',
          'https://example.com/picture.png',
          'https://example.com/graphic.gif',
          'https://example.com/bitmap.bmp',
          'https://example.com/modern.webp',
          'https://example.com/vector.svg',
          'https://example.com/icon.ico',
        ];

        for (final url in imageUrls) {
          final result = getAttachmentDescription('Text', url);
          expect(result, equals('Image'), reason: 'Failed for URL: $url');
        }
      });

      test('should return "Video" for video URLs', () {
        const videoUrls = [
          'https://example.com/video.mp4',
          'https://example.com/movie.mkv',
          'https://example.com/clip.mov',
          'https://example.com/film.avi',
          'https://example.com/media.wmv',
          'https://example.com/flash.flv',
          'https://example.com/web.webm',
          'https://example.com/mobile.m4v',
        ];

        for (final url in videoUrls) {
          final result = getAttachmentDescription('Text', url);
          expect(result, equals('Video'), reason: 'Failed for URL: $url');
        }
      });

      test('should return "Document" for other file URLs', () {
        const documentUrls = [
          'https://example.com/document.pdf',
          'https://example.com/spreadsheet.xlsx',
          'https://example.com/presentation.pptx',
          'https://example.com/text.txt',
          'https://example.com/archive.zip',
        ];

        for (final url in documentUrls) {
          final result = getAttachmentDescription('Text', url);
          expect(result, equals('Document'), reason: 'Failed for URL: $url');
        }
      });

      test('should handle case insensitive extensions', () {
        const mixedCaseUrls = [
          'https://example.com/image.JPG',
          'https://example.com/video.MP4',
          'https://example.com/photo.PNG',
        ];

        final results = mixedCaseUrls.map((url) => getAttachmentDescription('Text', url)).toList();

        expect(results, equals(['Image', 'Video', 'Image']));
      });

      test('should return original message for non-URL text', () {
        const nonUrlTexts = [
          'Just a regular message',
          'This has a .jpg extension but is not a URL',
          'file.mp4 without protocol',
        ];

        for (final text in nonUrlTexts) {
          final result = getAttachmentDescription('Text', text);
          expect(result, equals(text), reason: 'Failed for text: $text');
        }
      });

      test('should handle non-Text message types', () {
        const imageUrl = 'https://example.com/image.jpg';
        const videoUrl = 'https://example.com/video.mp4';
        const docUrl = 'https://example.com/document.pdf';

        expect(getAttachmentDescription('Image', imageUrl), equals('Image'));
        expect(getAttachmentDescription('Video', videoUrl), equals('Video'));
        expect(getAttachmentDescription('Document', docUrl), equals('Document'));
        expect(getAttachmentDescription('Unknown', 'some content'), equals('Unknown'));
      });

      test('should handle URLs without file extensions', () {
        const urlWithoutExtension = 'https://example.com/somefile';
        final result = getAttachmentDescription('Text', urlWithoutExtension);
        expect(result, equals('Document'));
      });

      test('should handle malformed URLs', () {
        const malformedUrls = [
          'not-a-url',
          'http://',
          'https://',
          '',
          '   ',
        ];

        for (final url in malformedUrls) {
          final result = getAttachmentDescription('Text', url);
          expect(result, equals(url), reason: 'Failed for malformed URL: $url');
        }
      });
    });
  });
}

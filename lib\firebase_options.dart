// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBtcW-ivMqL9MPf-k_ditdO-Ek9mpazUzg',
    appId: '1:772434367346:android:2064928e964da3710bc3eb',
    messagingSenderId: '772434367346',
    projectId: 'ddone-e5328',
    storageBucket: 'ddone-e5328.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCQ3GnkAWYL0Tk4sbr2bveSTB9JgJRvCLM',
    appId: '1:772434367346:ios:3283f49270980c880bc3eb',
    messagingSenderId: '772434367346',
    projectId: 'ddone-e5328',
    storageBucket: 'ddone-e5328.appspot.com',
    iosBundleId: 'com.dotdash.ddone.dev',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCQ3GnkAWYL0Tk4sbr2bveSTB9JgJRvCLM',
    appId: '1:772434367346:ios:8824cb7af9ee35550bc3eb',
    messagingSenderId: '772434367346',
    projectId: 'ddone-e5328',
    storageBucket: 'ddone-e5328.appspot.com',
    iosBundleId: 'com.example.ddone',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyD9jowxRbUGa8kB671PmYqRyGtO6YPRch0',
    appId: '1:772434367346:web:d566f11f116316c60bc3eb',
    messagingSenderId: '772434367346',
    projectId: 'ddone-e5328',
    authDomain: 'ddone-e5328.firebaseapp.com',
    storageBucket: 'ddone-e5328.appspot.com',
    measurementId: 'G-8MQQRL4H80',
  );
}

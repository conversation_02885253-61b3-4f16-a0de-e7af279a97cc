import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/delete_bookmark/delete_bookmark_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/cubit/members_presence/members_presence_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/models/xmpp_model/mam_info.dart';
import 'package:ddone/models/xmpp_model/presence_info.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:flutter/foundation.dart';
import 'package:moxlib/moxlib.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';

import 'package:moxxmpp/moxxmpp.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

import 'package:synchronized/synchronized.dart';
import 'package:uuid/uuid.dart';

bool _xor(bool a, bool b) {
  return !a && b || a && !b;
}

typedef PendingQueryKey = (JID, String);

abstract class MAMError {}

class UnknownMAMError extends MAMError {}

class MAMData extends StanzaHandlerExtension {
  MAMData(this.queryId, this.delay);

  /// The id of the query.
  final String? queryId;

  /// The MAM-attached delayed delivery tag.
  final DelayedDeliveryData delay;
}

class XmppService extends XmppManagerBase {
  XmppService({
    required this.deleteBookmarkCubit,
    required this.loginCubit,
    required this.bookmarkListCubit,
    required this.groupUiCubit,
  }) : super(mamManager);

  final DeleteBookmarkCubit deleteBookmarkCubit;
  final LoginCubit loginCubit;
  final BookmarkListCubit bookmarkListCubit;
  final GroupUiCubit groupUiCubit;

  final Map<PendingQueryKey, int> _pendingQueries = {};

  final Lock _lock = Lock();

  @override
  Future<bool> isSupported() async => true;

  @override
  List<StanzaHandler> getIncomingStanzaHandlers() => [
        StanzaHandler(
          stanzaTag: 'message',
          tagName: 'result',
          tagXmlns: mamXmlns,
          callback: _onMAMMessage,
          priority: -98,
        ),
        StanzaHandler(
          stanzaTag: 'presence',
          callback: _onPresence,
          priority: -100,
        ),
      ];

  Future<StanzaHandlerData> _onMAMMessage(
    Stanza stanza,
    StanzaHandlerData data,
  ) async {
    // log.t('_onMAMMessage\n- stanza: ${stanza.toXml()}\n- mamXmlns: $mamXmlns');
    final updatedPendingQueries = <(JID, String), int>{};
    for (final entry in _pendingQueries.entries) {
      final lowercaseJid = JID.fromString(entry.key.$1.toString().toLowerCase());
      final newKey = (lowercaseJid, entry.key.$2);
      updatedPendingQueries[newKey] = entry.value;
    }

    // Replace _pendingQueries with the updated map
    _pendingQueries
      ..clear()
      ..addAll(updatedPendingQueries);

    final result = stanza.firstTag('result', xmlns: mamXmlns)!;

    final qid = result.attributes['queryid']! as String;
    final stanzaSplit = _pendingQueries.keys.first.$1.domain.contains('muc')
        ? (stanza.from!).toString().split('/')
        : (stanza.to!).toString().split('/');

    final jid = JID.fromString(stanzaSplit[0].toLowerCase());
    final key = (jid, qid);

    final isQuerying = await _lock.synchronized(() {
      final contains = _pendingQueries.containsKey(key);
      if (contains) {
        // Increment if the key exists.
        _pendingQueries[key] = _pendingQueries[key]! + 1;
      }
      return contains;
    });

    if (!isQuerying) {
      logger.warning('Received unexpected MAM result. Ignoring...');
      return StanzaHandlerData(true, true, stanza, data.extensions);
    }
    final forwarded = result.firstTag('forwarded', xmlns: forwardedXmlns)!;
    final message = forwarded.firstTag('message', xmlns: stanzaXmlns)!;
    final delay = forwarded.firstTag('delay', xmlns: delayedDeliveryXmlns)!;

    final prefs = sl.get<SharedPreferences>();

    String? sipNumber = prefs.getString(CacheKeys.sipNumber);
    String? sipDomain = prefs.getString(CacheKeys.sipDomain);
    var userAtDomain = '$sipNumber@$sipDomain';

    // Determine the correct key based on 'to' and 'from'
    final to = message.attributes['to'] ?? '';
    final toSplit = to.split('/');
    final from = message.attributes['from'] ?? '';
    final fromSplit = from.split('/');
    final body = message.firstTag('body')?.text;
    final messageId = message.attributes['id'] ?? '';
    final ids = stanza.attributes['id'] ?? '';
    final type = message.attributes['type'] ?? '';
    final delayStamp = delay.attributes['stamp'] ?? '' ?? DateTime.now().toUtc().toString();
    final msgBeforeId = result.attributes['id'] ?? '';
    final isMe = userAtDomain;
    final chatKeyId = (toSplit[0] == isMe) ? fromSplit[0] : toSplit[0];

    final keygrp = (to.contains('/') ? fromSplit[0] : toSplit[0]);

    final receivekeygrp = (!message.attributes.entries.contains('to') ? fromSplit[0] : toSplit[0]);
    bool isMessageHistory = prefs.getBool(CacheKeys.isMessageHistory) ?? false;

    // ............................................................................
    debugPrint('isMessageHistory $isMessageHistory');
    if (type == 'chat') {
      if (fromSplit[0] == isMe) {
        final mamInfo = MamInfo(
          toSplit[0],
          '',
          body ?? '',
          type,
          delayStamp,
          ids,
          msgBeforeId,
          '',
        );

        if (!MamListCubit.stanza.containsKey(chatKeyId)) {
          MamListCubit.stanza[chatKeyId] = [];
        }

        MamListCubit.stanza[chatKeyId]?.add(mamInfo);
      } else {
        final mamInfo = MamInfo(
          toSplit[0],
          fromSplit[0],
          body ?? '',
          type,
          delayStamp,
          ids,
          msgBeforeId,
          '',
        );

        if (!MamListCubit.stanza.containsKey(chatKeyId)) {
          MamListCubit.stanza[chatKeyId] = [];
        }

        MamListCubit.stanza[chatKeyId]?.add(mamInfo);
      }
    } else {
      if (message.attributes.keys.contains('to')) {
        if (fromSplit[1] == isMe) {
          final mamInfo = MamInfo(
            fromSplit[0],
            '',
            body ?? '',
            type,
            delayStamp,
            ids,
            msgBeforeId,
            fromSplit[1],
          );

          if (!MamListCubit.stanza.containsKey(keygrp)) {
            MamListCubit.stanza[keygrp] = [];
          }
          MamListCubit.stanza[keygrp]?.add(mamInfo);
        } else {
          final mamInfo = MamInfo(
            fromSplit[0],
            from,
            body ?? '',
            type,
            delayStamp,
            ids,
            msgBeforeId,
            fromSplit[1],
          );

          if (!MamListCubit.stanza.containsKey(keygrp)) {
            MamListCubit.stanza[keygrp] = [];
          }
          MamListCubit.stanza[keygrp]?.add(mamInfo);
        }
      } else {
        if (fromSplit[1] == isMe) {
          final mamInfo = MamInfo(
            fromSplit[0],
            '',
            body ?? '',
            type,
            delayStamp,
            ids,
            msgBeforeId,
            fromSplit[1],
          );

          if (!MamListCubit.test.containsKey(receivekeygrp)) {
            MamListCubit.test[receivekeygrp] = [];
          }

          MamListCubit.test[receivekeygrp]?.add(mamInfo);
        } else {
          final mamInfo = MamInfo(
            fromSplit[0],
            from,
            body ?? '',
            type,
            delayStamp,
            ids,
            msgBeforeId,
            fromSplit[1],
          );

          if (!MamListCubit.test.containsKey(receivekeygrp)) {
            MamListCubit.test[receivekeygrp] = [];
          }
          MamListCubit.test[receivekeygrp]?.add(mamInfo);
        }
      }
    }
    // ............................................................................

    return StanzaHandlerData(
      false,
      false,
      Stanza.fromXMLNode(message),
      data.extensions
        ..set(
          MAMData(
            qid,
            DelayedDeliveryData(
              jid,
              DateTime.parse(delay.attributes['stamp']! ?? ''),
            ),
          ),
        ),
    );
  }

  Future<StanzaHandlerData> _onPresence(
    Stanza presence,
    StanzaHandlerData state,
  ) async {
    final attrs = getAttributes();
    switch (presence.type) {
      case 'subscribe':
      case 'subscribed':
        {
          attrs.sendEvent(
            SubscriptionRequestReceivedEvent(
              from: JID.fromString(presence.from!),
            ),
          );
          return state..done = true;
        }
      default:
        break;
    }

    if (presence.from != null) {
      logger.finest("Received presence from '${presence.from}'");
      final xPresence = presence.findTags('x');
      final presenceSplit = presence.from?.split('/');
      final type = presence.type;
      debugPrint('typetype $type');
      if (type == null) {
        for (var x in xPresence) {
          debugPrint('type.isEmpty)type.isEmpty)');
          final presenceFrom = (presence.from!.contains('muc')) ? presenceSplit![0] : '';
          final item = Stanza.fromXMLNode(x);
          final items = item.firstTag('item')!;
          final itemJid = items.attributes['jid'].toString();
          final itemJidSplit = itemJid.split('/');
          final itemAffiliation = items.attributes['affiliation'].toString();
          final itemRole = items.attributes['role'].toString();
          final presenceInfo = PresenceInfo(
            itemJidSplit[0],
            itemAffiliation,
            itemRole,
            presenceSplit![1],
          );
          debugPrint('${presenceInfo.affiliation} -- ${presenceInfo.jid} -- ${presenceInfo.role}');
          PresencesCubit.presence.forEach((key, value) {
            // Convert List to Set to remove duplicates and back to List
            final uniqueList = value.toSet().toList();
            PresencesCubit.presence[key] = uniqueList;
          });
          if (!PresencesCubit.presence.containsKey(presenceFrom)) {
            PresencesCubit.presence[presenceFrom] = [];
          }
          PresencesCubit.presence[presenceFrom]?.add(presenceInfo);
          logger.finest('PresencesCubit.presence ${PresencesCubit.presence}');
          logger.finest(
              "Received presence item JID ++ DDONE ++: '$itemJid' Affiliation : '$itemAffiliation' Role : '$itemRole'");
        }
      } else {
        debugPrint('type.isNotEmpty)type.isNotEmpty)');
        if (type == 'unavailable') {
          debugPrint('type == unavailabletype == unavailable');
          for (var x in xPresence) {
            final presenceFrom = (presence.from!.contains('muc')) ? presenceSplit![0] : '';
            final item = Stanza.fromXMLNode(x);
            final items = item.firstTag('item')!;
            final status = item.findTags('status');
            final destroy = item.findTags('destroy');
            final itemJid = items.attributes['jid'].toString();
            final itemJidSplit = itemJid.split('/');
            final itemAffiliation = items.attributes['affiliation'].toString();
            final itemRole = items.attributes['role'].toString();
            if (status.isNotEmpty) {
              for (var statusCode in status) {
                final code = Stanza.fromXMLNode(statusCode);
                final kickCode = code.attributes['code'];
                if (kickCode.toString() == '321') {
                  final presenceInfo = PresenceInfo(
                    itemJidSplit[0],
                    itemAffiliation,
                    itemRole,
                    presenceSplit![1],
                  );
                  debugPrint('${presenceInfo.affiliation} -- ${presenceInfo.jid} -- ${presenceInfo.role}');
                  deleteBookmarkCubit.deleteBookmark(
                    loginCubit: loginCubit,
                    bookmarkListCubit: bookmarkListCubit,
                    groupUiCubit: groupUiCubit,
                    bookmarkName: presenceFrom,
                  );
                }
              }
            } else if (destroy.isNotEmpty) {
              for (var destroyGroup in destroy) {
                final destroyTag = Stanza.fromXMLNode(destroyGroup);
                final destroyJid = destroyTag.attributes['jid'];
                // if (destroyJid.toString() == '321') {
                final presenceInfo = PresenceInfo(
                  itemJidSplit[0],
                  itemAffiliation,
                  itemRole,
                  presenceSplit![1],
                );
                debugPrint('${presenceInfo.affiliation} -- ${presenceInfo.jid} -- ${presenceInfo.role}');
                deleteBookmarkCubit.deleteBookmark(
                  loginCubit: loginCubit,
                  bookmarkListCubit: bookmarkListCubit,
                  groupUiCubit: groupUiCubit,
                  bookmarkName: destroyJid,
                );
              }
              // }
            } else {
              debugPrint('type == availabletype == available');

              final presenceInfo = PresenceInfo(
                itemJidSplit[0],
                itemAffiliation,
                itemRole,
                presenceSplit![1],
              );
              debugPrint('${presenceInfo.affiliation} -- ${presenceInfo.jid} -- ${presenceInfo.role}');
              PresencesCubit.presence.forEach((key, value) {
                // Convert List to Set to remove duplicates and back to List
                final uniqueList = value.toSet().toList();
                PresencesCubit.presence[key] = uniqueList;
              });
              if (!PresencesCubit.presence.containsKey(presenceFrom)) {
                PresencesCubit.presence[presenceFrom] = [];
              }
              PresencesCubit.presence[presenceFrom]?.add(presenceInfo);
              logger.finest('PresencesCubit.presence ${PresencesCubit.presence}');
              logger.finest(
                  "Received presence item JID ++ DDONE ++: '$itemJid' Affiliation : '$itemAffiliation' Role : '$itemRole'");
            }
          }
        }
      }

      return state..done = true;
    }

    return state;
  }

  Future<void> getField() async {
    getAttributes().sendStanza(
      StanzaDetails(
        Stanza.iq(type: 'get', children: [
          XMLNode.xmlns(
            tag: 'query',
            xmlns: mamXmlns,
          ),
        ]),
      ),
    );
  }

  Future<Result<MAMError, int>> requestMessage(
    JID jid,
    // {
    // JID requestJid,
    // DateTime? fromDate,
    // DateTime? endDate,
    // int? pageSize,
    // }
  ) async {
    final uuid = sl.get<Uuid>().v4();
    final key = (jid, uuid);
    await _lock.synchronized(() {
      _pendingQueries[key] = 0;
    });
    final request = Stanza.iq(
      type: 'set',
      to: jid.toString(),
      children: [
        XMLNode.xmlns(
          tag: 'query',
          xmlns: mamXmlns,
          attributes: {
            'queryid': uuid,
          },
          children: [
            XMLNode.xmlns(
              tag: 'x',
              xmlns: dataFormsXmlns,
              attributes: {
                'type': 'submit',
              },
              children: [
                XMLNode(
                  tag: 'field',
                  attributes: {
                    'var': 'FORM_TYPE',
                    'type': 'hidden',
                  },
                  children: [
                    XMLNode(
                      tag: 'value',
                      text: mamXmlns,
                    ),
                  ],
                ),
              ],
            ),
            XMLNode.xmlns(
              tag: 'set',
              xmlns: 'http://jabber.org/protocol/rsm',
              children: [
                XMLNode(
                  tag: 'before',
                ),
              ],
            ),
          ],
        ),
      ],
    );

    debugPrint('');

    final result = await getAttributes().sendStanza(
      StanzaDetails(
        request,
      ),
    );
    final messageCount = await _lock.synchronized(() => _pendingQueries.remove(key));

    // Check if the query finished successfully
    if (result!.attributes['type'] != 'result') {
      return Result(UnknownMAMError());
    }
    // return null;
    return Result(messageCount);
  }

  Future<Result<MAMError, int>> requestMsg(
    JID jid, {
    JID? requestJid,
    DateTime? fromDate,
    DateTime? endDate,
    int? pageSize,
  }) async {
    final uuid = sl.get<Uuid>().v4();
    final key = (jid, uuid);
    await _lock.synchronized(() {
      _pendingQueries[key] = 0;
    });
    final request = Stanza.iq(
      type: 'set',
      to: jid.toString(),
      children: [
        XMLNode.xmlns(
          tag: 'query',
          xmlns: mamXmlns,
          attributes: {
            'queryid': uuid,
          },
          children: [
            XMLNode.xmlns(
              tag: 'x',
              xmlns: dataFormsXmlns,
              attributes: {
                'type': 'submit',
              },
              children: [
                XMLNode(
                  tag: 'field',
                  attributes: {
                    'var': 'FORM_TYPE',
                    'type': 'hidden',
                  },
                  children: [
                    XMLNode(
                      tag: 'value',
                      text: mamXmlns,
                    ),
                  ],
                ),
                XMLNode(
                  tag: 'field',
                  attributes: {
                    'var': 'with',
                  },
                  children: [
                    XMLNode(
                      tag: 'value',
                      text: requestJid.toString(),
                    ),
                  ],
                ),
              ],
            ),
            XMLNode.xmlns(
              tag: 'set',
              xmlns: 'http://jabber.org/protocol/rsm',
              children: [
                XMLNode(
                  tag: 'max',
                  text: pageSize.toString(),
                ),
                XMLNode(
                  tag: 'before',
                ),
              ],
            ),
          ],
        ),
      ],
    );

    debugPrint('');

    final result = await getAttributes().sendStanza(
      StanzaDetails(
        request,
      ),
    );
    final messageCount = await _lock.synchronized(() => _pendingQueries.remove(key));

    // Check if the query finished successfully
    if (result!.attributes['type'] != 'result') {
      return Result(UnknownMAMError());
    }
    // return null;
    return Result(messageCount);
  }

  Future<Result<MAMError, int>> requestMoreMsg(
    JID jid, {
    JID? requestJid,
    required String beforeId,
    int? pageSize,
  }) async {
    final uuid = sl.get<Uuid>().v4();
    final key = (jid, uuid);
    await _lock.synchronized(() {
      _pendingQueries[key] = 0;
    });
    final request = Stanza.iq(
      type: 'set',
      to: jid.toString(),
      children: [
        XMLNode.xmlns(
          tag: 'query',
          xmlns: mamXmlns,
          attributes: {
            'queryid': uuid,
          },
          children: [
            XMLNode.xmlns(
              tag: 'x',
              xmlns: dataFormsXmlns,
              attributes: {
                'type': 'submit',
              },
              children: [
                XMLNode(
                  tag: 'field',
                  attributes: {
                    'var': 'FORM_TYPE',
                    'type': 'hidden',
                  },
                  children: [
                    XMLNode(
                      tag: 'value',
                      text: mamXmlns,
                    ),
                  ],
                ),
                XMLNode(
                  tag: 'field',
                  attributes: {
                    'var': 'with',
                  },
                  children: [
                    XMLNode(
                      tag: 'value',
                      text: requestJid.toString(),
                    ),
                  ],
                ),
              ],
            ),
            XMLNode.xmlns(
              tag: 'set',
              xmlns: 'http://jabber.org/protocol/rsm',
              children: [
                XMLNode(
                  tag: 'max',
                  text: pageSize.toString(),
                ),
                XMLNode(
                  tag: 'before',
                  text: beforeId,
                ),
              ],
            ),
          ],
        ),
      ],
    );

    debugPrint('');

    final result = await getAttributes().sendStanza(
      StanzaDetails(
        request,
      ),
    );
    final messageCount = await _lock.synchronized(() => _pendingQueries.remove(key));

    // Check if the query finished successfully
    if (result!.attributes['type'] != 'result') {
      return Result(UnknownMAMError());
    }
    // return null;
    return Result(messageCount);
  }

  Future<Result<MAMError, int>> requestGroupMessage(
    JID groupJid,
    // {
    // DateTime? fromDate,
    // DateTime? endDate,
    // int? pageSize,
    // }
  ) async {
    final uuid = sl.get<Uuid>().v4();
    final key = (groupJid, uuid);
    await _lock.synchronized(() {
      _pendingQueries[key] = 0;
    });
    final request = Stanza.iq(
      type: 'set',
      to: groupJid.toString(),
      children: [
        XMLNode.xmlns(
          tag: 'query',
          xmlns: mamXmlns,
          attributes: {
            'queryid': uuid,
          },
          children: [
            XMLNode.xmlns(
              tag: 'x',
              xmlns: dataFormsXmlns,
              attributes: {
                'type': 'submit',
              },
              children: [
                XMLNode(
                  tag: 'field',
                  attributes: {
                    'var': 'FORM_TYPE',
                    'type': 'hidden',
                  },
                  children: [
                    XMLNode(
                      tag: 'value',
                      text: mamXmlns,
                    ),
                  ],
                ),
              ],
            ),
            XMLNode.xmlns(
              tag: 'set',
              xmlns: 'http://jabber.org/protocol/rsm',
              children: [
                XMLNode(
                  tag: 'before',
                ),
              ],
            ),
          ],
        ),
      ],
    );

    debugPrint('');

    final result = await getAttributes().sendStanza(
      StanzaDetails(
        request,
      ),
    );
    final messageCount = await _lock.synchronized(() => _pendingQueries.remove(key));

    // Check if the query finished successfully
    if (result!.attributes['type'] != 'result') {
      return Result(UnknownMAMError());
    }
    // return null;
    return Result(messageCount);
  }

  Future<Result<MAMError, int>> requestGroupMsg(
    JID requestJid,
    int pageSize,
  ) async {
    final uuid = sl.get<Uuid>().v4();
    final key = (requestJid, uuid);
    await _lock.synchronized(() {
      _pendingQueries[key] = 0;
    });
    final request = Stanza.iq(
      type: 'set',
      to: requestJid.toString(),
      children: [
        XMLNode.xmlns(
          tag: 'query',
          xmlns: mamXmlns,
          attributes: {
            'queryid': uuid,
          },
          children: [
            XMLNode.xmlns(
              tag: 'x',
              xmlns: dataFormsXmlns,
              attributes: {
                'type': 'submit',
              },
              children: [
                XMLNode(
                  tag: 'field',
                  attributes: {
                    'var': 'FORM_TYPE',
                    'type': 'hidden',
                  },
                  children: [
                    XMLNode(
                      tag: 'value',
                      text: mamXmlns,
                    ),
                  ],
                ),
              ],
            ),
            XMLNode.xmlns(
              tag: 'set',
              xmlns: 'http://jabber.org/protocol/rsm',
              children: [
                XMLNode(
                  tag: 'max',
                  text: pageSize.toString(),
                ),
                XMLNode(
                  tag: 'before',
                ),
              ],
            ),
          ],
        ),
      ],
    );

    debugPrint('');

    final result = await getAttributes().sendStanza(
      StanzaDetails(
        request,
      ),
    );
    final messageCount = await _lock.synchronized(() => _pendingQueries.remove(key));

    // Check if the query finished successfully
    if (result!.attributes['type'] != 'result') {
      return Result(UnknownMAMError());
    }
    // return null;
    return Result(messageCount);
  }

  Future<Result<MAMError, int>> requestMoreGroupMsg(
    JID requestJid,
    int pageSize,
    String beforeId,
  ) async {
    final uuid = sl.get<Uuid>().v4();
    final key = (requestJid, uuid);
    await _lock.synchronized(() {
      _pendingQueries[key] = 0;
    });
    final request = Stanza.iq(
      type: 'set',
      to: requestJid.toString(),
      children: [
        XMLNode.xmlns(
          tag: 'query',
          xmlns: mamXmlns,
          attributes: {
            'queryid': uuid,
          },
          children: [
            XMLNode.xmlns(
              tag: 'x',
              xmlns: dataFormsXmlns,
              attributes: {
                'type': 'submit',
              },
              children: [
                XMLNode(
                  tag: 'field',
                  attributes: {
                    'var': 'FORM_TYPE',
                    'type': 'hidden',
                  },
                  children: [
                    XMLNode(
                      tag: 'value',
                      text: mamXmlns,
                    ),
                  ],
                ),
              ],
            ),
            XMLNode.xmlns(
              tag: 'set',
              xmlns: 'http://jabber.org/protocol/rsm',
              children: [
                XMLNode(
                  tag: 'max',
                  text: pageSize.toString(),
                ),
                XMLNode(
                  tag: 'before',
                  text: beforeId,
                ),
              ],
            ),
          ],
        ),
      ],
    );

    debugPrint('');

    final result = await getAttributes().sendStanza(
      StanzaDetails(
        request,
      ),
    );
    final messageCount = await _lock.synchronized(() => _pendingQueries.remove(key));

    // Check if the query finished successfully
    if (result!.attributes['type'] != 'result') {
      return Result(UnknownMAMError());
    }
    // return null;
    return Result(messageCount);
  }

  Future<Result<MAMError, int>> requestMg(
    JID jid,
  ) async {
    final uuid = sl.get<Uuid>().v4();
    final key = (jid, uuid);
    await _lock.synchronized(() {
      _pendingQueries[key] = 0;
    });
    final requested = Stanza.iq(
      type: 'set',
      to: jid.toString(),
      children: [
        XMLNode.xmlns(
          tag: 'query',
          xmlns: mamXmlns,
          attributes: {
            'queryid': uuid,
          },
        ),
      ],
    );
    final result = await getAttributes().sendStanza(
      StanzaDetails(
        requested,
      ),
    );
    final messageCount = await _lock.synchronized(() => _pendingQueries.remove(key));

    // Check if the query finished successfully
    if (result!.attributes['type'] != 'result') {
      return Result(UnknownMAMError());
    }
    // return null;
    return Result(messageCount);
  }

  Future<Result<MAMError, int>> requestMessages(
    JID archive, {
    String? beforeId,
    String? afterId,
    List<String>? ids,
    int? pageSize,
  }) async {
    assert(
      _xor(
        beforeId != null || afterId != null,
        ids != null,
      ),
      'beforeId/afterId cannot be specified with ids',
    );

    final uuid = const Uuid().v4();
    final key = (archive, uuid);
    await _lock.synchronized(() {
      _pendingQueries[key] = 0;
    });
    // DataForm? dataForm;
    if (beforeId != null || afterId != null || ids != null) {}

    final request = Stanza.iq(
      type: 'set',
      // to: archive.toString(),
      children: [
        XMLNode.xmlns(
          tag: 'query',
          xmlns: mamXmlns,
          attributes: {
            'queryid': uuid,
          },
          children: [
            if (pageSize != null)
              XMLNode.xmlns(
                tag: 'set',
                xmlns: rsmXmlns,
                children: [
                  XMLNode(
                    tag: 'max',
                    text: pageSize.toString(),
                  ),
                  XMLNode(
                    tag: 'before',
                    text: beforeId,
                  ),
                ],
              ),
            // if (dataForm != null) dataForm.toXml(),
          ],
        ),
      ],
    );
    final result = await getAttributes().sendStanza(
      StanzaDetails(
        request,
        responseBypassesQueue: false,
      ),
    );

    // Remove the pending query key.
    final messageCount = await _lock.synchronized(() => _pendingQueries.remove(key));

    // Check if the query finished successfully
    if (result!.attributes['type'] != 'result') {
      return Result(UnknownMAMError());
    }
    // return null;
    return Result(messageCount);
  }
}

// import 'package:ddone/utils/pop_out_util.dart';
import 'package:ddone/service_locator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';

import 'package:ddone/components/list_tile/card_list_tile_with_title.dart';
import 'package:ddone/constants/regex_constants.dart';
// import 'package:ddone/cubit/chat_category/chat_category_cubit.dart';
// import 'package:ddone/cubit/delete_bookmark/delete_bookmark_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
// import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/cubit/update/info_cubit.dart';
import 'package:ddone/screens/chat.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/string_ext.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:logger/logger.dart';

// --- Constants for Indices (Optional but recommended) ---
const int kRecentChatsIndex = 0;
const int kDirectMessagesIndex = 1;
const int kGroupChatsIndex = 2;

class ChatCategory extends StatefulWidget {
  final int index;

  const ChatCategory({
    required this.index,
    super.key,
  });

  @override
  State<ChatCategory> createState() => _ChatCategoryState();
}

class _ChatCategoryState extends State<ChatCategory> {
  // --- Cubits ---
  late LoginCubit _loginCubit;
  late InfoCubit _infoCubit;
  late MamListCubit _mamListCubit;
  // late GroupUiCubit _groupUiCubit; // Needed for onItemSelectedGroup
  // late BookmarkListCubit _bookmarkListCubit; // Needed for onItemSelectedGroup
  // late ChatCategoryCubit _chatCategoryCubit; // Needed for onItemSelected
  // late DeleteBookmarkCubit _deleteBookmarkCubit; // Needed for onItemSelectedGroup

  // --- Serivces ---
  late Logger log;

  @override
  void initState() {
    super.initState();

    _loginCubit = BlocProvider.of<LoginCubit>(context);
    _infoCubit = BlocProvider.of<InfoCubit>(context);
    _mamListCubit = BlocProvider.of<MamListCubit>(context);
    // _groupUiCubit = BlocProvider.of<GroupUiCubit>(context);
    // _bookmarkListCubit = BlocProvider.of<BookmarkListCubit>(context);
    // _chatCategoryCubit = BlocProvider.of<ChatCategoryCubit>(context);
    // _deleteBookmarkCubit = BlocProvider.of<DeleteBookmarkCubit>(context);

    log = sl.get<Logger>();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      margin: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
      color: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      shadowColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        side: const BorderSide(width: 0.8, color: Colors.transparent),
        borderRadius: BorderRadius.circular(10),
      ),
      child: BlocBuilder<LoginCubit, LoginState>(
        // Build based on authentication state first
        builder: (context, loginState) {
          if (loginState is LoginAuthenticated && loginState.connection.isAuthenticated) {
            // Use BlocBuilder for InfoState as it's needed by all list types for selection highlight
            return BlocBuilder<InfoCubit, InfoState>(builder: (context, infoState) {
              // Use BlocBuilder for MamListState as it provides the data for lists
              return BlocBuilder<MamListCubit, MamListState>(builder: (context, mamListState) {
                // Determine which list builder to use
                switch (widget.index) {
                  case kRecentChatsIndex:
                    return _buildAllChatsList(context, infoState, mamListState);
                  case kDirectMessagesIndex:
                    return _buildDirectMessagesList(context, infoState, mamListState);
                  case kGroupChatsIndex:
                    return _buildGroupChatsList(context, infoState, mamListState);
                  default:
                    return const Center(child: Text('Invalid Category'));
                }
              });
            });
          } else {
            return _buildNotAuthenticated(context);
          }
        },
      ),
    );
  }

  Widget _buildNotAuthenticated(BuildContext context) {
    return const Center(
      child: Text(
        'Not Authenticated',
        style: TextStyle(
          color: Colors.red, // Consider using theme error color
        ),
      ),
    );
  }

  // --- Helper: Empty List State ---
  Widget _buildEmptyList(BuildContext context, String message) {
    return Center(
      child: Text(
        message,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey, // Use theme color
            ),
      ),
    );
  }

  // --- Helper: Build "All" Chats List (Index 0) ---
  Widget _buildAllChatsList(BuildContext context, InfoState infoState, MamListState mamListState) {
    if (mamListState is! MamListUpdated) {
      // Could return a loading indicator here if MamList has loading states
      return _buildEmptyList(context, 'Loading recent chats');
    }

    final mamFullList = mamListState.filteredMamFullList;

    if (mamFullList.isEmpty) {
      return _buildEmptyList(context, 'No recent chats');
    }

    return ListView.builder(
      itemCount: mamFullList.length,
      itemBuilder: (context, index) {
        final mamItem = mamFullList.elementAt(index);
        final displayName = mamItem['displayName']?.toString() ?? '';
        final jid = mamItem['jid']?.toString() ?? '';

        if (jid.isEmpty) return const SizedBox.shrink(); // Skip invalid items

        // Process display name (extract actual name from potential MUC JID format)
        final String groupnameReplaced = displayName.contains('@muc.') ? displayName.mucIDFilter() : displayName;
        final groupNameFilter = groupNameRegex.firstMatch(groupnameReplaced);
        final String? groupIdentifier = groupNameFilter?.group(0); // UUID part
        final String actualName = groupnameReplaced.replaceAll(groupIdentifier ?? '', '').trim();
        final displayTitle =
            actualName.isNotEmpty ? actualName : jid; // Fallback to JID if name is empty after filtering

        return CardListTileWithTitle(
          title: '', // No category title needed here
          listTileTitle: displayTitle,
          leadingIcon: Icon(
            // Determine icon based on JID type
            jid.contains('@muc.') ? Icons.group : Icons.person,
            color: Colors.white, // Use theme color
          ),
          showTitle: false,
          onTap: () => _handleItemTap(jid, isGroup: jid.contains('@muc.')),
          isTileSelected: jid == infoState.receiver,
          // Add onLongPress or trailing icon for delete/pin actions if needed
          // Example: trailing: _buildItemActions(context, jid, isGroup: jid.contains('@muc.')),
        );
      },
    );
  }

  // --- Helper: Build Direct Messages List (Index 1) ---
  Widget _buildDirectMessagesList(BuildContext context, InfoState infoState, MamListState mamListState) {
    if (mamListState is! MamListUpdated) {
      return _buildEmptyList(context, 'Loading direct messages');
    }
    // Use the combined list specifically for contacts
    final contactList = mamListState.filteredContactModelList;

    if (contactList.isEmpty) {
      return _buildEmptyList(context, 'No contacts for direct messages');
    }

    return ListView.builder(
      itemCount: contactList.length,
      itemBuilder: (context, index) {
        final contact = contactList[index];
        final bool isLocal = contact.isLocalContact ?? false; // Default to false if null

        // Skip local contacts if that's the intention for this view
        if (isLocal) {
          return const SizedBox.shrink();
        }

        // Ensure there's a valid URI to tap on
        final String? contactUri = contact.contactEntries.isNotEmpty
            ? contact.contactEntries[0].uri
            : null; // Or handle finding the right URI if multiple exist

        if (contactUri == null) {
          return const SizedBox.shrink(); // Skip if no URI
        }

        return CardListTileWithTitle(
          title: '',
          listTileTitle: contact.displayName,
          leadingIcon: const Icon(
            Icons.person,
            color: Colors.white, // Use theme color
          ),
          showTitle: false,
          onTap: () => _handleItemTap(contactUri, isGroup: false),
          isTileSelected: contactUri == infoState.receiver,
          // Example: trailing: _buildItemActions(context, contactUri, isGroup: false),
        );
      },
    );
  }

  // --- Helper: Build Group Chats List (Index 2) ---
  Widget _buildGroupChatsList(BuildContext context, InfoState infoState, MamListState mamListState) {
    if (mamListState is! MamListUpdated) {
      return _buildEmptyList(context, 'Loading groups');
    }

    // Use the list specifically for bookmarks/groups from MamListCubit
    final groupList = mamListState.filterBookmarkModelList; // Assumes this holds ConferenceInfo or similar

    if (groupList.isEmpty) {
      return _buildEmptyList(context, 'No groups joined or created');
    }

    // Optional: Check ContactsState as well if needed for additional group info/filtering
    // final contactsState = context.watch<ContactsCubit>().state;
    // if (contactsState is! ContactsLoaded) { // Or relevant loaded state
    //    return _buildEmptyList(context, 'Loading contact info...');
    // }
    // final contactsGroupList = contactsState.filterBookmarkGroupList; // If needed

    return ListView.builder(
      itemCount: groupList.length,
      itemBuilder: (context, index) {
        final group = groupList[index]; // Assuming this is ConferenceInfo or similar structure

        // Process group name
        final groupNameReplaced = group.name.mucIDFilter();
        final groupNameFilter = groupNameRegex.firstMatch(groupNameReplaced);
        final String? groupIdentifier = groupNameFilter?.group(0);
        final String groupNameOnly = groupNameReplaced.replaceAll(groupIdentifier ?? '', '').trim();
        final displayTitle = groupNameOnly.isNotEmpty ? groupNameOnly : group.jid; // Fallback to JID

        return CardListTileWithTitle(
          onTap: () => _handleItemTap(group.jid, isGroup: true, nick: group.nick, name: group.name),
          title: '',
          listTileTitle: displayTitle,
          leadingIcon: const Icon(
            Icons.group,
            color: Colors.white, // Use theme color
          ),
          showTitle: false,
          isTileSelected: group.jid == infoState.receiver,
          // Example: trailing: _buildItemActions(context, group.jid, isGroup: true, nick: group.nick),
        );
      },
    );
  }

  void _handleItemTap(String jid, {required bool isGroup, String? nick, String? name}) {
    if (isGroup) {
      _infoCubit.getGroupChatHistory(
        receiver: jid,
        nick: nick ?? '', // Provide nick if available
        name: name ?? jid, // Provide name if available, fallback to jid
        loginCubit: _loginCubit,
        mamListCubit: _mamListCubit,
      );
    } else {
      _infoCubit.getChatHistory(
        receiver: jid,
        loginCubit: _loginCubit,
        mamListCubit: _mamListCubit,
      );
    }

    if (isMobile) {
      pushNamed(ChatPage.routeName);
    }
  }

  // --- Optional Helper: Build Action Menu (Example) ---
  // Widget _buildItemActions(BuildContext context, String jid, {required bool isGroup, String? nick}) {
  //   return PopupMenuButton<String>(
  //     icon: const Icon(Icons.more_vert, color: Colors.grey), // Use theme color
  //     onSelected: (String value) {
  //       if (isGroup) {
  //         onItemSelectedGroup(context, value, jid, nick ?? '');
  //       } else {
  //         onItemSelected(context, value, jid);
  //       }
  //     },
  //     itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
  //       const PopupMenuItem<String>(
  //         value: 'Delete',
  //         child: Text('Delete Chat'),
  //       ),
  //       // const PopupMenuItem<String>( // Add Pin later if needed
  //       //   value: 'Pin',
  //       //   child: Text('Pin Chat'),
  //       // ),
  //     ],
  //   );
  // }

  // --- Action Handlers (Keep as is for now) ---
  // void onItemSelected(BuildContext context, dynamic value, String jid) {
  //   if (value == null) return;
  //   ScaffoldMessenger.maybeOf(context)?.clearSnackBars();
  //   // Consider using theme for SnackBar
  //   ScaffoldMessenger.of(context).showSnackBar(
  //     SnackBar(content: Text('[ $value ] Selected ($jid)')),
  //   );

  //   switch (value) {
  //     case 'Delete':
  //       showConfirmDialog(
  //         context: context,
  //         title: 'Confirm delete chat?',
  //         rightButtonText: 'Yes',
  //         onPressedRightButton: () {
  //           // Use the cubit accessed in initState
  //           _chatCategoryCubit.removeRoster(jid, _loginCubit);
  //           _infoCubit.removeInfo(); // Clear info pane if this chat was selected
  //           Navigator.of(context).pop(); // Close confirmation dialog
  //         },
  //         leftButtonText: 'No',
  //         onPressedLeftButton: () => Navigator.of(context).pop(), // Close confirmation dialog
  //       );
  //       break;
  //     case 'Pin':
  //       // Implement Pin logic
  //       break;
  //     default:
  //   }
  // }

  // void onItemSelectedGroup(BuildContext context, dynamic value, String jid, String nick) {
  //   if (value == null) return;
  //   ScaffoldMessenger.maybeOf(context)?.clearSnackBars();
  //   ScaffoldMessenger.of(context).showSnackBar(
  //     SnackBar(content: Text('[ $value ] Selected ($jid)')),
  //   );
  //   switch (value) {
  //     case 'Delete':
  //       showConfirmDialog(
  //         context: context,
  //         title: 'Confirm delete group chat?', // More specific title
  //         rightButtonText: 'Yes',
  //         onPressedRightButton: () {
  //           // Use the cubits accessed in initState
  //           _chatCategoryCubit.removeGroup(
  //             jid,
  //             nick,
  //             _loginCubit,
  //             _deleteBookmarkCubit,
  //             _groupUiCubit,
  //             _bookmarkListCubit,
  //           );
  //           _infoCubit.removeInfo(); // Clear info pane if this group was selected
  //           Navigator.of(context).pop(); // Close confirmation dialog
  //         },
  //         leftButtonText: 'No',
  //         onPressedLeftButton: () => Navigator.of(context).pop(), // Close confirmation dialog
  //       );
  //       break;
  //     case 'Pin':
  //       // Implement Pin logic
  //       break;
  //     default:
  //   }
  // }
}

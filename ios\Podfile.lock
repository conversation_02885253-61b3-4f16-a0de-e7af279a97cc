PODS:
  - app_tracking_transparency (0.0.1):
    - Flutter
  - audio_session (0.0.1):
    - Flutter
  - background_downloader (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - CryptoSwift (1.8.3)
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - file_selector_ios (0.0.1):
    - Flutter
  - Firebase/CoreOnly (11.4.0):
    - FirebaseCore (= 11.4.0)
  - Firebase/Messaging (11.4.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.4.0)
  - firebase_core (3.8.0):
    - Firebase/CoreOnly (= 11.4.0)
    - Flutter
  - firebase_messaging (15.1.5):
    - Firebase/Messaging (= 11.4.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.4.0):
    - FirebaseCoreInternal (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.5.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseInstallations (11.4.0):
    - FirebaseCore (~> 11.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.4.0):
    - FirebaseCore (~> 11.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Reachability (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_background_service_ios (0.0.3):
    - Flutter
  - flutter_callkit_incoming (0.0.1):
    - CryptoSwift
    - Flutter
  - flutter_contacts (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - flutter_volume_controller (0.0.1):
    - Flutter
  - flutter_webrtc (0.9.36):
    - Flutter
    - WebRTC-SDK (= 114.5735.08)
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - heif_converter (1.0.0):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - media_kit_libs_ios_video (1.0.4):
    - Flutter
  - media_kit_native_event_loop (1.0.0):
    - Flutter
  - media_kit_video (0.0.1):
    - Flutter
  - MTBBarcodeScanner (5.0.11)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - network_info_plus (0.0.1):
    - Flutter
  - open_filex (0.0.2):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - phone_state (1.0.4):
    - Flutter
  - PromisesObjC (2.4.0)
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - screen_brightness_ios (0.1.0):
    - Flutter
  - SDWebImage (5.20.0):
    - SDWebImage/Core (= 5.20.0)
  - SDWebImage/Core (5.20.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - volume_controller (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - WebRTC-SDK (114.5735.08)

DEPENDENCIES:
  - app_tracking_transparency (from `.symlinks/plugins/app_tracking_transparency/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - background_downloader (from `.symlinks/plugins/background_downloader/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/darwin`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - file_selector_ios (from `.symlinks/plugins/file_selector_ios/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_background_service_ios (from `.symlinks/plugins/flutter_background_service_ios/ios`)
  - flutter_callkit_incoming (from `.symlinks/plugins/flutter_callkit_incoming/ios`)
  - flutter_contacts (from `.symlinks/plugins/flutter_contacts/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - flutter_volume_controller (from `.symlinks/plugins/flutter_volume_controller/ios`)
  - flutter_webrtc (from `.symlinks/plugins/flutter_webrtc/ios`)
  - heif_converter (from `.symlinks/plugins/heif_converter/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - media_kit_libs_ios_video (from `.symlinks/plugins/media_kit_libs_ios_video/ios`)
  - media_kit_native_event_loop (from `.symlinks/plugins/media_kit_native_event_loop/ios`)
  - media_kit_video (from `.symlinks/plugins/media_kit_video/ios`)
  - network_info_plus (from `.symlinks/plugins/network_info_plus/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - phone_state (from `.symlinks/plugins/phone_state/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - screen_brightness_ios (from `.symlinks/plugins/screen_brightness_ios/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - volume_controller (from `.symlinks/plugins/volume_controller/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  trunk:
    - CryptoSwift
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - MTBBarcodeScanner
    - nanopb
    - PromisesObjC
    - SDWebImage
    - SwiftyGif
    - WebRTC-SDK

EXTERNAL SOURCES:
  app_tracking_transparency:
    :path: ".symlinks/plugins/app_tracking_transparency/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  background_downloader:
    :path: ".symlinks/plugins/background_downloader/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/darwin"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  file_selector_ios:
    :path: ".symlinks/plugins/file_selector_ios/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_background_service_ios:
    :path: ".symlinks/plugins/flutter_background_service_ios/ios"
  flutter_callkit_incoming:
    :path: ".symlinks/plugins/flutter_callkit_incoming/ios"
  flutter_contacts:
    :path: ".symlinks/plugins/flutter_contacts/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  flutter_volume_controller:
    :path: ".symlinks/plugins/flutter_volume_controller/ios"
  flutter_webrtc:
    :path: ".symlinks/plugins/flutter_webrtc/ios"
  heif_converter:
    :path: ".symlinks/plugins/heif_converter/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  media_kit_libs_ios_video:
    :path: ".symlinks/plugins/media_kit_libs_ios_video/ios"
  media_kit_native_event_loop:
    :path: ".symlinks/plugins/media_kit_native_event_loop/ios"
  media_kit_video:
    :path: ".symlinks/plugins/media_kit_video/ios"
  network_info_plus:
    :path: ".symlinks/plugins/network_info_plus/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  phone_state:
    :path: ".symlinks/plugins/phone_state/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  screen_brightness_ios:
    :path: ".symlinks/plugins/screen_brightness_ios/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  volume_controller:
    :path: ".symlinks/plugins/volume_controller/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  app_tracking_transparency: ****************************************
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  background_downloader: b0572309a68d929c35941f0484e8ad7c65052228
  connectivity_plus: b21496ab28d1324eb59885d888a4d83b98531f01
  CryptoSwift: 967f37cea5a3294d9cce358f78861652155be483
  device_info_plus: 335f3ce08d2e174b9fdc3db3db0f4e3b1f66bd89
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: 9b3292d7c8bc68c8a7bf8eb78f730e49c8efc517
  file_selector_ios: f92e583d43608aebc2e4a18daac30b8902845502
  Firebase: cf1b19f21410b029b6786a54e9764a0cacad3c99
  firebase_core: 84a16d041be8bc166b6e00350f89849e06daf9d1
  firebase_messaging: 48ce5cf82b70ac47ed9cb9277f97bb77d1d01a38
  FirebaseCore: e0510f1523bc0eb21653cac00792e1e2bd6f1771
  FirebaseCoreInternal: f47dd28ae7782e6a4738aad3106071a8fe0af604
  FirebaseInstallations: 6ef4a1c7eb2a61ee1f74727d7f6ce2e72acf1414
  FirebaseMessaging: f8a160d99c2c2e5babbbcc90c4a3e15db036aee2
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_background_service_ios: 00d31bdff7b4bfe06d32375df358abe0329cf87e
  flutter_callkit_incoming: cb8138af67cda6dd981f7101a5d709003af21502
  flutter_contacts: 5383945387e7ca37cf963d4be57c21f2fc15ca9f
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  flutter_volume_controller: c2be490cb0487e8b88d0d9fc2b7e1c139a4ebccb
  flutter_webrtc: f5711277e7c7c49949e62c2b24afc4965cd48028
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  heif_converter: c99800312529d736276a6a75a2115b8f43a0bad1
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  media_kit_libs_ios_video: 5a18affdb97d1f5d466dc79988b13eff6c5e2854
  media_kit_native_event_loop: 5fba1a849a6c87a34985f1e178a0de5bd444a0cf
  media_kit_video: 1746e198cb697d1ffb734b1d05ec429d1fcd1474
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  network_info_plus: cf61925ab5205dce05a4f0895989afdb6aade5fc
  open_filex: 432f3cd11432da3e39f47fcc0df2b1603854eff1
  package_info_plus: 566e1b7a2f3900e4b0020914ad3fc051dcc95596
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  phone_state: 09524710fc44aabcf0b392ac5cbe51ae72c76352
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  qr_code_scanner: d77f94ecc9abf96d9b9b8fc04ef13f611e5a147a
  screen_brightness_ios: 5ed898fa50fa82a26171c086ca5e28228f932576
  SDWebImage: 73c6079366fea25fa4bb9640d5fb58f0893facd8
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite: c35dad70033b8862124f8337cc994a809fcd9fa3
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  volume_controller: ca1cde542ee70fad77d388f82e9616488110942b
  wakelock_plus: 8c239121a007daa1d6759c6acdc507860273dd2f
  WebRTC-SDK: c24d2a6c9f571f2ed42297cb8ffba9557093142b

PODFILE CHECKSUM: 1fac58de45a9a3c23da5ef0e08c771ac42a7bf07

COCOAPODS: 1.16.2

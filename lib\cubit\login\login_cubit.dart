import 'dart:async';
import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/chat_recent/chat_recent_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/delete_bookmark/delete_bookmark_cubit.dart';
import 'package:ddone/cubit/messaging/receive_message_cubit.dart';
import 'package:ddone/cubit/update/chat_ui_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/cubit/invitation/invitation_cubit.dart';
import 'package:ddone/cubit/messaging/messages_cubit.dart';
import 'package:ddone/environments/env.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/firebase_service.dart';
import 'package:ddone/services/xmpp/connectivity_manager.dart';
import 'package:ddone/services/xmpp/fixed_resource_binding_negotiator.dart';
import 'package:ddone/services/xmpp/self_signed_tcp_socket_wrapper.dart';
import 'package:ddone/services/xmpp_service.dart';
import 'package:ddone/utils/auth_util.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/cubit/members_presence/members_presence_cubit.dart';
import 'package:moxlib/moxlib.dart';
import 'package:moxxmpp/model/conference_info.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:moxxmpp_socket_tcp/moxxmpp_socket_tcp.dart';

part 'login_state.dart';

@Deprecated('Use XmppConnectionCubit')
class LoginCubit extends Cubit<LoginState> with PrefsAware {
  StreamSubscription? connectionStreamSubscription;
  static String? uri;
  static String? username;
  static String? jid;
  late XmppConnection connection;
  final rosterStateManager = TestingRosterStateManager('', []);
  final FirebaseService firebaseService;

  LoginCubit._()
      : firebaseService = sl.get<FirebaseService>(),
        super(LoginInitial());

  factory LoginCubit.initial({LoginState? state}) {
    return LoginCubit._();
  }

  String generateHash(String ext, String domain, String authPassword) {
    // Concatenate the values with ':' in between
    String input = '$ext:$domain:$authPassword';

    // Convert the input to bytes
    List<int> bytes = utf8.encode(input);

    // Hash the input using MD5
    Digest md5Hash = md5.convert(bytes);

    // Return the hash as a hexadecimal string
    return md5Hash.toString();
  }

  Future<void> authenticateUser({
    required InvitationCubit invitationCubit,
    required MessagesCubit messagesCubit,
    required MamListCubit mamListCubit,
    required BookmarkListCubit bookmarkListCubit,
    required PresencesCubit presencesCubit,
    required ChatRecentCubit chatRecentCubit,
    required ContactsCubit contactsCubit,
    required ChatUiCubit chatUiCubit,
    required LoginCubit loginCubit,
    required DeleteBookmarkCubit deleteBookmarkCubit,
    required GroupUiCubit groupUiCubit,
    required ReceiveMessageCubit receiveMessageCubit,
  }) async {
    emit(LoginLoading());

    String? sipNumber = prefs.getString(CacheKeys.sipNumber);
    String? sipDomain = prefs.getString(CacheKeys.sipDomain);
    String? sipPwd = prefs.getString(CacheKeys.sipPwd);

    if (sipDomain == null) {
      return;
    }
    String hash = AuthUtil.hashSecret('$sipNumber:$sipDomain:$sipPwd');

    String userAtDomain = '$sipNumber@$sipDomain';

    // This generate resouceId part in JID once and only once unless user clear app data or uninstall the app
    // - because it includes sipNumber+sipDomain+datetime, so by right the chance for 1 user to have the same resourceId will be
    //   very very slim. After we got the resouceId, we don't need to care about regenerating it anymore. Just slap the
    //   same resourceId even if the user changed the sipNumber and sipDomain.
    String? jidResourceId = prefs.getString(CacheKeys.jidResourceId);
    jidResourceId ??= AuthUtil.generateUniqueIdFromString('$sipNumber:$sipDomain:${DateTime.now().toIso8601String()}');
    prefs.setString(CacheKeys.jidResourceId, jidResourceId);

    try {
      final XmppConnection connection = XmppConnection(
        RandomBackoffReconnectionPolicy(1, 60),
        ServerAvailabilityConnectivityManager(env!.mongooseimXmppHost),
        ClientToServerNegotiator(),
        env!.flavor.name == BuildFlavor.production.name ? TCPSocketWrapper(false) : SelfSignedTcpSocketWrapper(),
        bookmarkListCubit: bookmarkListCubit,
      );
      connection
        ..registerManagers([
          StreamManagementManager(),
          DiscoManager([]),
          PingManager(
            const Duration(minutes: 3),
          ),
          MessageManager(),
          PresenceManager(),
          ChatStateManager(),
          CarbonsManager(),
          MessageDeliveryReceiptManager(),
          MUCManager(),
          StableIdManager(),
          HttpFileUploadManager(),
          RosterManager(rosterStateManager),
          MessageRetractionManager(),
          XmppService(
            deleteBookmarkCubit: deleteBookmarkCubit,
            loginCubit: loginCubit,
            bookmarkListCubit: bookmarkListCubit,
            groupUiCubit: groupUiCubit,
          ),
        ])
        ..registerFeatureNegotiators([
          FixedResourceBindingNegotiator(jidResourceId),
          RosterFeatureNegotiator(),
          StartTlsNegotiator(),
          StreamManagementNegotiator(),
          CSINegotiator(),
          RosterFeatureNegotiator(),
          SaslPlainNegotiator(),
          SaslScramNegotiator(10, '', '', ScramHashType.sha512),
          SaslScramNegotiator(9, '', '', ScramHashType.sha256),
          SaslScramNegotiator(8, '', '', ScramHashType.sha1),
        ]);

      log.t('xmpp connection - host:${env!.mongooseimXmppHost} port:${int.tryParse(env!.mongooseimXmppPort)}');
      connection.connectionSettings = ConnectionSettings(
        jid: JID.fromString(userAtDomain),
        password: hash,
        host: env!.mongooseimXmppHost,
        port: int.tryParse(env!.mongooseimXmppPort),
      );

      connection.asBroadcastStream().listen((event) async {
        log.t('xmpp stream [<-- $event]');
        if (event is MessageEvent) {
          final body = event.extensions.get<MessageBodyData>()?.body;
          final from = event.from.toString();
          final fromSplit = from.split('/');
          final to = event.to.toString();
          final toSplit = to.split('/');
          final id = event.id;
          final idData = event.extensions.get<PresenceManager>()?.id;
          final stableID = event.extensions.get<StableIdData>()?.stanzaIds;
          final ad = event.extensions.get<OccupantIdData>()?.id;
          final groupId = event.groupFrom.toString();
          // final groupJoin = event.groupReceived;
          // final reasonJoin = event.reasonReceived;

          log.t(
              'xmpp connection listen - from:$from, to:$to, id:$id, body:$body, groupId:$groupId, idData:$idData, stableID:$stableID, ad:$ad');

          if (!event.extensions.keys.toString().contains('MAMData')) {
            if (body != null && body.isNotEmpty) {
              if (fromSplit[0].contains('muc') || toSplit[0].contains('muc')) {
                messagesCubit.receiveMessage(
                  loginCubit: loginCubit,
                  mamListCubit: mamListCubit,
                  chatUiCubit: chatUiCubit,
                  receiveMessageCubit: receiveMessageCubit,
                  text: body,
                  senderJid: from,
                  id: id.toString(),
                  grpJid: fromSplit[1].toString(),
                );
              } else {
                messagesCubit.receiveMessage(
                  loginCubit: loginCubit,
                  mamListCubit: mamListCubit,
                  chatUiCubit: chatUiCubit,
                  receiveMessageCubit: receiveMessageCubit,
                  text: body,
                  senderJid: fromSplit[0],
                  id: id.toString(),
                  grpJid: '',
                );
              }
              mamListCubit.getCombinedContactList(contactsCubit: contactsCubit);
              mamListCubit.getCombinedFullList(contactsCubit: contactsCubit);
            }
          }
        } else if (event is ConnectionStateChangedEvent) {
          log.t('xmpp stream [=== xmppConnectionState:${event.state}]');
        } else if (event is StreamNegotiationsDoneEvent) {
          log.t('xmpp stream [=== resumed:${event.resumed}]');
        } else if (event is ResourceBoundEvent) {
          log.t('xmpp stream [=== resource:${event.resource}]');
        } else if (event is StreamManagementEnabledEvent) {
          log.t('xmpp stream [=== resource:${event.resource} id:${event.id} location:${event.location}]');
        } else if (event is InviteEvent) {
          final from = event.from;
          final to = event.to ?? '';
          final id = event.id ?? '';
          final roomJid = event.jid ?? '';
          final reason = event.reason ?? '';

          debugPrint('From : $from >>> To : $to >>> Id : $id >>> RoomJid : $roomJid >>> Reason : $reason');
          debugPrint('The Not Message Event Keys: ${event.extensions.keys}');

          if (roomJid.isNotEmpty) {
            if (!from.toString().contains('muc')) {
              invitationCubit.receiveInvitation(
                loginCubit: loginCubit,
                to: to.toString(),
                from: from.toString(),
                roomJid: roomJid,
                reason: reason,
              );
            }
          }
        } else if (event is MemberJoinedEvent) {
          final room = event.roomJid;
          final member = event.member;
          debugPrint('RoomJid: $room >> Member: $member');
        }
      });

      Result<bool, XmppError> connect = await connection.connect(
        shouldReconnect: true,
        waitForConnection: true,
        waitUntilLogin: true,
      );

      log.t('authenticateUser - Connecting');
      if (connect.isType<XmppError>()) {
        XmppError error = connect.get<XmppError>();
        log.e('XMPP Connection error:$error isRecoverable: ${error.isRecoverable()}');
      }
      if (!connect.isType<bool>()) {
        log.e('Authentication Failed');
        return;
      }

      log.t('authenticateUser - Testing connection : ${connection.isAuthenticated}');
      emit(LoginAuthenticated(connection: connection));
      log.t('authenticateUser - Connected');

      final muc = connection.getManagerById<MUCManager>(mucManager)!;
      muc.getBookMarks();

      // get groups
      await bookmarkListCubit.getbookmarkList();
      contactsCubit.setBookmarkGroupList(bookmarkListCubit: bookmarkListCubit);

      // join groups (need to join then can only get group messages)
      List<Future> joinGroupFutures = [];
      for (int i = 0; i < bookmarkListCubit.state.bookmarkList.length; i++) {
        final bookmarks = bookmarkListCubit.state.bookmarkList[i];
        joinGroupFutures.add(joinGroup(muc, bookmarks, userAtDomain));
      }
      await Future.wait(joinGroupFutures);

      // get messages
      final mam = connection.getManagerById<XmppService>(mamManager)!;
      List<Future> chatFutures = [
        requestDirectMessages(mam, userAtDomain), // get direct messages
      ];

      // Roster is used for friend request thing, but now we don't use it. Comment out first.
      // final roster = connection.getManagerById<RosterManager>(rosterManager)!;
      // final Result<RosterRequestResult, RosterError> result = await roster.requestRoster();
      // final rosterList = result.get<RosterRequestResult>();
      // debugPrint('rosterList.items ${rosterList.items}');

      for (int i = 0; i < bookmarkListCubit.state.bookmarkList.length; i++) {
        final bookmarks = bookmarkListCubit.state.bookmarkList[i];
        chatFutures.add(requestGroupMessages(mam, bookmarks)); // get group messages
      }
      await Future.wait(chatFutures);
      mamListCubit.getMamList();

      presencesCubit.getGroupPresences();
    } catch (e) {
      log.e('AuthenticateUser xmpp connection error', error: e);
      emit(
        LoginAuthenticationFailed(
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> requestDirectMessages(XmppService mam, String user) async {
    final response = await mam.requestMessage(
      JID.fromString(user),
    );
    log.t('xmpp requestDirectMessages - requestMessage response:$response');
    await mam.getField();
  }

  Future<void> requestGroupMessages(XmppService mam, ConferenceInfo bookmark) async {
    final response = await mam.requestGroupMessage(
      JID.fromString(bookmark.jid),
    );
    log.t('xmpp requestGroupMessages - requestGroupMessage response:$response');
  }

  Future<void> joinGroup(MUCManager muc, ConferenceInfo bookmark, String user) async {
    log.t('xmpp joinGroup - bookmark:$bookmark, user:$user');
    final response = await muc.joinRoom(
      JID.fromString(bookmark.jid),
      bookmark.nick.toString(),
    );
    log.t(
        'xmpp joinGroup - response:$response, response<bool>:${response.get<bool>()}, response<MUCError>${response.get<MUCError>()}');
    await muc.getRoomNick(
      JID.fromString(bookmark.jid),
      user,
    );
  }

  void logout() {
    emit(LoginInitial());
  }
}

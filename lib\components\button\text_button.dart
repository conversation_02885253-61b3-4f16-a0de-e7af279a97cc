import 'package:flutter/material.dart';

class TextButtonWidget extends StatelessWidget {
  final VoidCallback onPressed;
  final Widget contentWidget;
  final ButtonStyle? buttonStyle;
  const TextButtonWidget({
    required this.onPressed,
    required this.contentWidget,
    this.buttonStyle,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onPressed,
      style: buttonStyle ??
          const ButtonStyle(
            backgroundColor: WidgetStatePropertyAll(
              Colors.black45,
            ),
          ),
      // ButtonStyle(
      //   backgroundColor: MaterialStatePropertyAll(
      //     color ?? Colors.black45,
      //   ),
      // ),
      child: contentWidget,
    );
  }
}

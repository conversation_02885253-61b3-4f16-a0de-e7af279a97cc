part of 'focus_cubit.dart';

class FocusState extends Equatable {
  final bool isContactFocused;
  final bool isDialpadFocused;
  final bool isTransferContactFocused;
  final bool isTransferDialpadFocused;

  const FocusState({
    this.isContactFocused = false,
    this.isDialpadFocused = false,
    this.isTransferContactFocused = false,
    this.isTransferDialpadFocused = false,
  });

  FocusState copyWith({
    bool? isContactFocused,
    bool? isDialpadFocused,
    bool? isTransferContactFocused,
    bool? isTransferDialpadFocused,
  }) {
    return FocusState(
      isContactFocused: isContactFocused ?? this.isContactFocused,
      isDialpadFocused: isDialpadFocused ?? this.isDialpadFocused,
      isTransferContactFocused: isTransferContactFocused ?? this.isTransferContactFocused,
      isTransferDialpadFocused: isTransferDialpadFocused ?? this.isTransferDialpadFocused,
    );
  }

  @override
  List<Object> get props => [
        isContactFocused,
        isDialpadFocused,
        isTransferContactFocused,
        isTransferDialpadFocused,
      ];
}

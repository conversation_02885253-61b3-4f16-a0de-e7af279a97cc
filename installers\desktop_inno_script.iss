; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!

#define MyAppName "DDone"
#define MyAppVersion "0.0.8"
#define MyAppPublisher "DotDash Technologies Sdn Bhd"
#define MyAppURL "https://dotdashtech.com/"
#define MyAppExeName "DDOne.exe"

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
; (To generate a new GUID, click Tools | Generate GUID inside the IDE.)
AppId={{EE3D7CD1-27C3-4709-BE0C-DF6D8659520A}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
;AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\{#MyAppName}
DisableProgramGroupPage=yes
; Uncomment the following line to run in non administrative install mode (install for current user only.)
;PrivilegesRequired=lowest
OutputDir=C:\Users\<USER>\OneDrive\Desktop\WorkSpace\flutter\flutter-ddone\ddone\installers
OutputBaseFilename=DDOne
SetupIconFile=C:\Users\<USER>\OneDrive\Desktop\logo\ddadmin.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "C:\Users\<USER>\OneDrive\Desktop\WorkSpace\flutter\flutter-ddone\ddone\build\windows\runner\Release\{#MyAppExeName}"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Users\<USER>\OneDrive\Desktop\WorkSpace\flutter\flutter-ddone\ddone\build\windows\runner\Release\audioplayers_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Users\<USER>\OneDrive\Desktop\WorkSpace\flutter\flutter-ddone\ddone\build\windows\runner\Release\file_selector_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Users\<USER>\OneDrive\Desktop\WorkSpace\flutter\flutter-ddone\ddone\build\windows\runner\Release\flutter_webrtc_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Users\<USER>\OneDrive\Desktop\WorkSpace\flutter\flutter-ddone\ddone\build\windows\runner\Release\flutter_windows.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Users\<USER>\OneDrive\Desktop\WorkSpace\flutter\flutter-ddone\ddone\build\windows\runner\Release\just_audio_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Users\<USER>\OneDrive\Desktop\WorkSpace\flutter\flutter-ddone\ddone\build\windows\runner\Release\libwebrtc.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Users\<USER>\OneDrive\Desktop\WorkSpace\flutter\flutter-ddone\ddone\build\windows\runner\Release\permission_handler_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Users\<USER>\OneDrive\Desktop\WorkSpace\flutter\flutter-ddone\ddone\build\windows\runner\Release\url_launcher_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "C:\Users\<USER>\OneDrive\Desktop\WorkSpace\flutter\flutter-ddone\ddone\build\windows\runner\Release\data\*"; DestDir: "{app}\data"; Flags: ignoreversion recursesubdirs createallsubdirs
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent


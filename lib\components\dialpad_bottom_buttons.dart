import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DialpadBottomButtons extends StatelessWidget {
  final GestureTapCallback? onLeftButtonClick,
      onMiddleButtonClick,
      onRightButtonClick,
      onLeftButtonLongPress,
      onMiddleButtonLongPress,
      onRightButtonLongPress;
  final IconData leftIcon, middleIcon, rightIcon;

  const DialpadBottomButtons({
    required this.leftIcon,
    required this.middleIcon,
    required this.rightIcon,
    this.onLeftButtonClick,
    this.onLeftButtonLongPress,
    this.onMiddleButtonClick,
    this.onMiddleButtonLongPress,
    this.onRightButtonClick,
    this.onRightButtonLongPress,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;

        // const size = heightLarge;

        return Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            RoundShapeInkWell(
              onTap: () {
                if (onLeftButtonClick != null) {
                  onLeftButtonClick!();
                }
              },
              onLongPress: () {
                if (onLeftButtonLongPress != null) {
                  onLeftButtonLongPress!();
                }
              },
              // size: size,
              contentWidget: Icon(
                leftIcon,
                color: colorTheme.roundShapeInkWellColor,
                size: iconSizeLarge,
              ),
              color: colorTheme.primaryColor,
            ),
            RoundShapeInkWell(
              onTap: () {
                if (onMiddleButtonClick != null) {
                  onMiddleButtonClick!();
                }
              },
              onLongPress: () {
                if (onMiddleButtonLongPress != null) {
                  onMiddleButtonLongPress!();
                }
              },
              // size: size,
              contentWidget: Icon(
                middleIcon,
                color: colorTheme.roundShapeInkWellColor,
                size: iconSizeLarge,
              ),
              color: colorTheme.primaryColor,
            ),
            RoundShapeInkWell(
              onTap: () {
                if (onRightButtonClick != null) {
                  onRightButtonClick!();
                }
              },
              onLongPress: () {
                if (onRightButtonLongPress != null) {
                  onRightButtonLongPress!();
                }
              },
              // size: size,
              contentWidget: Icon(
                rightIcon,
                color: colorTheme.roundShapeInkWellColor,
                size: iconSizeLarge,
              ),
              color: colorTheme.primaryColor,
            ),
          ],
        );
      },
    );
  }
}

part of 'call_history_cubit.dart';

abstract class CallHistoryState extends Equatable {
  final List<CallRecords> callRecordList;

  const CallHistoryState({this.callRecordList = const []});

  @override
  List<Object?> get props => [callRecordList];
}

class CallHistoryInitial extends CallH<PERSON>oryState {
  const CallHistoryInitial() : super(callRecordList: const []);

  @override
  List<Object?> get props => super.props..addAll([]);
}

class CallHistoryLoaded extends CallHistoryState {
  const CallHistoryLoaded({List<CallRecords> callRecordList = const []}) : super(callRecordList: callRecordList);

  @override
  List<Object?> get props => super.props..addAll([]);
}

class CallHistoryLoadFail extends CallHistoryState {
  const CallHistoryLoadFail() : super();

  @override
  List<Object?> get props => super.props..addAll([]);
}

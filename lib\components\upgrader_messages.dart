// import 'package:upgrader/upgrader.dart';

// // class CustomUpgraderMessages extends UpgraderMessages {
// //   CustomUpgraderMessages({required String code}) : super(code: code);

// //   @override
// //   String getMessage(UpgraderMessage message) {
// //     switch (message) {
// //       case UpgraderMessage.body:
// //         return 'A new version is available! Please update to version {appStoreVersion}.';
// //       case UpgraderMessage.buttonTitleUpdate:
// //         return 'Update Now';
// //       default:
// //         return super.getMessage(message);
// //     }
// //   }
// // }

// class MySpanishMessages extends UpgraderMessages {
//   /// Override the message function to provide custom language localization.
//   @override
//   String? message(UpgraderMessage messageKey) {
//     if (languageCode == 'es') {
//       switch (messageKey) {
//         case UpgraderMessage.body:
//           return 'es A new version of {{appName}} is available!';
//         case UpgraderMessage.buttonTitleIgnore:
//           return 'es Ignore';
//         case UpgraderMessage.buttonTitleLater:
//           return 'es Later';
//         case UpgraderMessage.buttonTitleUpdate:
//           return 'es Update Now';
//         case UpgraderMessage.prompt:
//           return 'es Want to update?';
//         case UpgraderMessage.releaseNotes:
//           return 'es Release Notes';
//         case UpgraderMessage.title:
//           return 'es Update App?';
//       }
//     }
//     // Messages that are not provided above can still use the default values.
//     return super.message(messageKey);
//   }
// }

import 'dart:ui';
import 'package:upgrader/upgrader.dart';

class UpgradeMsg extends UpgraderMessages {
  @override
  String get body => 'A new version of YourApp is available. Please Update.';
}

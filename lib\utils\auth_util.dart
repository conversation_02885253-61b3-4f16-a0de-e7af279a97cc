import 'dart:convert';

import 'package:crypto/crypto.dart';

class AuthUtil {
  static String hashSecret(String secret) {
    // Convert the secret to bytes
    List<int> bytes = utf8.encode(secret);
    // Hash the input using MD5
    Digest md5Hash = md5.convert(bytes);
    // Return the hash as a hexadecimal string
    return md5Hash.toString();
  }

  /// Generates a unique ID (SHA-256 hash) from a given string.
  ///
  /// This function takes an input string, encodes it as UTF-8 bytes,
  /// and then computes its SHA-256 hash. The hash is returned as a
  /// hexadecimal string. SHA-256 produces a 64-character hexadecimal string.
  ///
  /// Example:
  ///   String uniqueId = generateUniqueIdFromString('<EMAIL>');
  ///   print(uniqueId); // e.g., "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8"
  static String generateUniqueIdFromString(String inputString) {
    // 1. Encode the input string to UTF-8 bytes
    final bytes = utf8.encode(inputString);
    // 2. Compute the SHA-256 hash of the bytes
    final digest = sha256.convert(bytes);
    // 3. Return the hash as a hexadecimal string
    return digest.toString();
  }
}

import 'package:intl/intl.dart';

extension DateTimeExtension on DateTime {
  DateTime formatDate(String format) {
    return DateTime.parse(DateFormat(format).format(this));
  }

  DateTime yyyyMMdd() {
    return DateTime.parse(DateFormat('yyyy-MM-dd').format(this));
  }

  DateTime yyyyMMddHHmm() {
    return DateTime.parse(DateFormat('yyyy-MM-dd HH-mm').format(this));
  }

  // ignore: non_constant_identifier_names
  DateTime EEEEddMMMyyyy() {
    return DateTime.parse(DateFormat('EEEEE dd MMM yyyy').format(this));
  }

  // ignore: non_constant_identifier_names
  String EEEEddMMMyyyyString() {
    return DateFormat('EEE dd MMM yyyy').format(this);
  }

  bool isToday() {
    DateTime now = DateTime.now();

    return year == now.year && month == now.month && day == now.day;
  }

  bool isYesterday() {
    DateTime now = DateTime.now();

    DateTime yesterday = DateTime(now.year, now.month, now.day - 1);

    return year == yesterday.year && month == yesterday.month && day == yesterday.day;
  }

  bool areSameDate(DateTime date2) {
    return year == date2.year && month == date2.month && day == date2.day;
  }

  bool areNotSameDate(DateTime date2) {
    return year != date2.year || month != date2.month || day != date2.day;
  }
}

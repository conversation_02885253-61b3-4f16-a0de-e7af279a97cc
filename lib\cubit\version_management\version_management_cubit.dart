import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/environments/env.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/models/endpointmgt_models.dart';
import 'package:ddone/repositories/endpointmgt_repository.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:pub_semver/pub_semver.dart';

part 'version_management_state.dart';

class VersionManagementCubit extends Cubit<VersionManagementState> with PrefsAware {
  late final EndpointmgtRepository _endpointmgtRepository;

  // Completer to track checkForUpdates completion
  Completer<void>? _checkForUpdatesCompleter;
  bool _isCheckForUpdatesRunning = false;
  bool _hasCheckForUpdatesCompleted = false;

  VersionManagementCubit._({
    VersionManagementState? state,
  })  : _endpointmgtRepository = sl.get<EndpointmgtRepository>(),
        super(state ?? VersionInitial());

  factory VersionManagementCubit.initial({VersionManagementState? state}) {
    return sl.isRegistered<VersionManagementCubit>()
        ? sl.get<VersionManagementCubit>()
        : VersionManagementCubit._(state: state);
  }

  /// Waits for checkForUpdates to complete with a 30-second timeout
  Future<void> _waitForCheckForUpdates() async {
    if (_hasCheckForUpdatesCompleted) {
      return; // Already completed, no need to wait
    }

    if (_isCheckForUpdatesRunning && _checkForUpdatesCompleter != null) {
      try {
        await _checkForUpdatesCompleter!.future.timeout(
          const Duration(seconds: 30),
          onTimeout: () {
            log.w('Timeout waiting for checkForUpdates to complete');
            throw TimeoutException('checkForUpdates did not complete within 30 seconds', const Duration(seconds: 30));
          },
        );
      } catch (e) {
        log.e('Error waiting for checkForUpdates: $e');
        rethrow;
      }
    }
  }

  Future<void> checkForUpdates({
    String? screenWidth,
    String? screenHeight,
    String? pixelRatio,
  }) async {
    // If already running, wait for completion
    if (_isCheckForUpdatesRunning && _checkForUpdatesCompleter != null) {
      await _checkForUpdatesCompleter!.future;
      return;
    }

    // Set up the completer for this execution
    _checkForUpdatesCompleter = Completer<void>();
    _isCheckForUpdatesRunning = true;
    _hasCheckForUpdatesCompleted = false;

    try {
      emit(VersionChecking());

      String? endpointmgtUuid = prefs.getString(CacheKeys.endpointmgtUuid);

      DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
      String? deviceId;
      String? osType;
      String? osVersion;
      Map<String, String> miscellaneousInfo = {
        'locale': Platform.localeName,
        'screenWidth': '$screenWidth',
        'screenHeight': '$screenHeight',
        'pixelRatio': '$pixelRatio',
      };
      try {
        if (isAndroid) {
          AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
          deviceId = androidInfo.id;
          osType = 'Android';
          osVersion = androidInfo.version.release;
          miscellaneousInfo['model'] = androidInfo.model;
          miscellaneousInfo['manufacturer'] = androidInfo.manufacturer;
          miscellaneousInfo['sdkint'] = '${androidInfo.version.sdkInt}';
          miscellaneousInfo['cpuarch'] = androidInfo.supportedAbis.first;
        } else if (isIOS) {
          IosDeviceInfo iosInfo = await deviceInfoPlugin.iosInfo;
          deviceId = iosInfo.identifierForVendor;
          osType = iosInfo.systemName;
          osVersion = iosInfo.systemVersion;
          miscellaneousInfo['model'] = iosInfo.model;
          miscellaneousInfo['cpuarch'] = iosInfo.utsname.machine;
        } else if (isWindows) {
          WindowsDeviceInfo windowsInfo = await deviceInfoPlugin.windowsInfo;
          deviceId = windowsInfo.deviceId;
          osType = 'Windows';
          osVersion = 'buildlab:${windowsInfo.buildLab},releaseId:${windowsInfo.releaseId}';
          miscellaneousInfo['model'] = windowsInfo.computerName;
        } else if (isMacOS) {
          MacOsDeviceInfo macOsInfo = await deviceInfoPlugin.macOsInfo;
          deviceId = macOsInfo.systemGUID;
          osType = 'macOS';
          osVersion = macOsInfo.osRelease;
          miscellaneousInfo['model'] = macOsInfo.model;
          miscellaneousInfo['cpuarch'] = macOsInfo.arch;
        } else {
          deviceId = 'Unknown';
          osType = 'Unknown';
          osVersion = 'Unknown';
        }
      } catch (e) {
        log.e('Failed to get device info: $e');
        deviceId ??= 'Error';
        osType ??= 'Error';
        osVersion ??= 'Error';
      }

      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      final request = CheckUpdateRequest(
        device: Device(
          endpointId: endpointmgtUuid,
          deviceId: deviceId!,
          operatingSystem: osType,
          osVersion: osVersion,
          miscellaneousInfo: miscellaneousInfo,
        ),
        app: App(
          appId: packageInfo.appName, // name in pubspec.yaml
          appName: env!.appName, // name that we define ourselves in env
          currentAppVersion: packageInfo.version,
        ),
      );

      log.t(
          'checkAppUpdate url:${env!.endpointmgtUrl}${env!.checkAppUpdatePath} request:${jsonEncode(request.toJson())}');
      final response = await _endpointmgtRepository.checkAppUpdate(env!.checkAppUpdatePath, request);
      log.t('checkAppUpdate response:${jsonEncode(response.toJson())}');

      if (response.latestVersion != null) {
        final Version currentVersion = Version.parse(packageInfo.version);
        final Version latestVersion = Version.parse(response.latestVersion!);
        final bool isUpdateRequired = currentVersion < latestVersion;
        if (isUpdateRequired) {
          emit(VersionOutdated(
            forceUpdate: response.requiresUpdate ?? false,
            curerntVersion: packageInfo.version,
            latestVersion: response.latestVersion!,
            updateUrl: '${response.updateUrl}',
          ));
        }
      } else {
        emit(VersionUpToDate());
      }

      if (response.endpointId != null) {
        prefs.setString(CacheKeys.endpointmgtUuid, response.endpointId!);
      }

      // Mark as completed successfully
      _hasCheckForUpdatesCompleted = true;
      _checkForUpdatesCompleter?.complete();
    } catch (e) {
      log.e('Failed to check for updates.', error: e);
      emit(VersionCheckFailed(error: e.toString()));

      // Complete with error
      _checkForUpdatesCompleter?.completeError(e);
      rethrow;
    } finally {
      _isCheckForUpdatesRunning = false;
    }
  }

  Future<void> updateUser() async {
    try {
      // Wait for checkForUpdates to complete before proceeding
      await _waitForCheckForUpdates();
    } catch (e) {
      log.e('updateUser aborted due to checkForUpdates timeout or error: $e');
      return; // Exit early if checkForUpdates hasn't completed
    }

    String? userName = prefs.getString(CacheKeys.sipName);
    String? domain = prefs.getString(CacheKeys.sipDomain);
    String? extensionNumber = prefs.getString(CacheKeys.sipNumber);
    String? endpointmgtUuid = prefs.getString(CacheKeys.endpointmgtUuid);

    final request = UpdateUserRequest(
      user: User(
        name: userName,
        domain: domain,
        extensionNumber: extensionNumber,
      ),
      device: Device(
        endpointId: endpointmgtUuid,
      ),
    );
    try {
      log.t('updateUser url:${env!.endpointmgtUrl}${env!.updateUserPath} request:${jsonEncode(request.toJson())}');
      final response = await _endpointmgtRepository.updateUser(env!.updateUserPath, request);
      log.t('updateUser response:$response');
    } catch (e) {
      log.e('Failed to updateUser.', error: e);
    }
  }

  // Helper method to manually reset the state if needed
  void resetCheckForUpdatesState() {
    _isCheckForUpdatesRunning = false;
    _hasCheckForUpdatesCompleted = false;
    _checkForUpdatesCompleter = null;
  }

  @override
  Future<void> close() {
    // Clean up completer if it exists
    if (_checkForUpdatesCompleter != null && !_checkForUpdatesCompleter!.isCompleted) {
      _checkForUpdatesCompleter!.complete();
    }
    return super.close();
  }
}

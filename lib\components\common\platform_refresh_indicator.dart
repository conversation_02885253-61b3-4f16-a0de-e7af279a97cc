import 'package:flutter/material.dart';

class PlatformRefreshIndicator extends StatelessWidget {
  final Widget customWidget;
  final bool ableToRefresh;
  final Future<void> Function()? onRefresh;

  const PlatformRefreshIndicator({
    required this.customWidget,
    required this.ableToRefresh,
    this.onRefresh,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ableToRefresh
        ? RefreshIndicator(
            notificationPredicate: (_) => onRefresh != null,
            onRefresh: onRefresh ??
                () {
                  return Future.value();
                },
            child: customWidget,
          )
        : customWidget;
  }
}

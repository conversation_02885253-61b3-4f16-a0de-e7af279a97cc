part of 'add_bookmark_cubit.dart';

abstract class AddBookmarkState extends Equatable {
  final List<ConferenceInfo> pendingbookmark;
  const AddBookmarkState(this.pendingbookmark);

  @override
  List<Object> get props => [pendingbookmark];
}

class AddBookmarkInitial extends AddBookmarkState {
  AddBookmarkInitial(super.pendingbookmark);
}

class AddBookmarkPending extends AddBookmarkState {
  AddBookmarkPending(super.pendingbookmark);
}

class AddBookmarkDone extends AddBookmarkState {
  // final List<ConferenceInfo> lastestBookmarks;
  AddBookmarkDone(super.pendingbookmark,);
}

class AddBookmarkFailed extends AddBookmarkState {
  final String error;

  AddBookmarkFailed(super.pendingbookmark, {
    required this.error,
  });
}

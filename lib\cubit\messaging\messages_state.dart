part of 'messages_cubit.dart';

class MessagesState extends Equatable {
  static Map<String, List<MessageModel>> conversations = <String, List<MessageModel>>{};

  const MessagesState._();

  MessagesState({required Map<String, List<MessageModel>> conversation}) : super() {
    MessagesState.conversations = conversation;
  }

  @override
  List<Object> get props => [conversations];

  MessagesState copywith({
    Map<String, List<MessageModel>>? conversations,
  }) {
    return MessagesState(
      conversation: conversations ?? MessagesState.conversations,
    );
  }
}

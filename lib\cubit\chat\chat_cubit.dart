import 'package:bloc/bloc.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/add_bookmark/add_bookmark_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/delete_bookmark/delete_bookmark_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/cubit/messaging/messages_cubit.dart';
import 'package:ddone/cubit/update/chat_ui_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:equatable/equatable.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:flutter/material.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'chat_state.dart';

class ChatCubit extends Cubit<ChatState> {
  final SharedPreferences _sharedPreferences;
  ChatCubit._()
      : _sharedPreferences = sl.get<SharedPreferences>(),
        super(ChatInitial());

  factory ChatCubit.initial() {
    return ChatCubit._();
  }

  Future<void> joinChatRoom(
    String group,
    String nickname,
    LoginCubit loginCubit,
    AddBookmarkCubit addBookmarkCubit,
    BookmarkListCubit bookmarkListCubit,
    GroupUiCubit groupUiCubit,
  ) async {
    try {
      // if (context.read<BookmarkListCubit>().state is BookmarkListUpdated) {
      if (loginCubit.state is LoginAuthenticated) {
        LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;

        String? sipNumber = _sharedPreferences.getString(CacheKeys.sipNumber);
        String? sipDomain = _sharedPreferences.getString(CacheKeys.sipDomain);
        var userAtDomain = "${sipNumber!}@${sipDomain!}";

        final mucName = JID.fromString(group);
        final nickName = JID.fromString(userAtDomain);

        final muc = currentLoginState.connection.getManagerById<MUCManager>(mucManager)!;

        if (mucName.toString().isNotEmpty && nickName.toString().isNotEmpty) {
          addBookmarkCubit.addPendingBookmark(
            mucName.toString(),
            'true',
            mucName.toString(),
            nickName.toString(),
          );
          await addBookmarkCubit.addBookmark(
            loginCubit: loginCubit,
            bookmarkListCubit: bookmarkListCubit,
            groupUiCubit: groupUiCubit,
          );

          await muc.joinRoom(
            mucName,
            nickName.toString(),
            maxHistoryStanzas: 0,
          );
        }

        // await muc.configure(
        //   mucName,
        //   nickName.toString(),
        // );

        await muc.setConfigure(
          mucName,
          nickName.toString(),
        );
        try {
          await muc.registerUser(
            mucName,
            nickName.toString(),
            userAtDomain,
          );
        } catch (e) {
          debugPrint("Error registering user: $e");
        }
      }
    } catch (e) {
      debugPrint('Error creating chat room: $e');
    }
  }

  Future<void> leaveRoom(
    LoginCubit loginCubit,
    DeleteBookmarkCubit deleteBookmarkCubit,
    GroupUiCubit groupUiCubit,
    BookmarkListCubit bookmarkListCubit,
    String receiver,
    String grpNick,
  ) async {
    try {
      if (loginCubit.state is LoginAuthenticated) {
        LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;

        debugPrint("Leave : Group : $receiver");

        final mucName = JID.fromString(receiver);
        final nickName = grpNick;

        final muc = currentLoginState.connection.getManagerById<MUCManager>(mucManager)!;
        await deleteBookmarkCubit.deleteBookmark(
          loginCubit: loginCubit,
          bookmarkListCubit: bookmarkListCubit,
          groupUiCubit: groupUiCubit,
          bookmarkName: receiver,
        );
        await muc.leaveRoom(
          mucName,
          nickName,
        );

        debugPrint("MUC Query : ${muc.queryRoomInformation(JID.fromString(receiver))}");

        // final state = (await muc.getRoomState(mucName))!;
        // final manager = currentLoginState.connection
        //     .getManagerById<MessageManager>(messageManager)!;

        // await manager.sendMessage(mucName, TypedMap<StanzaHandlerExtension>.fromList([
        //   MessageBodyData(body),
        // ]));
      }
    } catch (e) {
      debugPrint('Error leaving chat room: $e');
    }
  }

  Future<void> removeGroup(
    LoginCubit loginCubit,
    DeleteBookmarkCubit deleteBookmarkCubit,
    GroupUiCubit groupUiCubit,
    BookmarkListCubit bookmarkListCubit,
    String receiver,
    String grpNick,
    String destroyreasonEditingController,
  ) async {
    try {
      if (loginCubit.state is LoginAuthenticated) {
        LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;

        String? sipNumber = _sharedPreferences.getString(CacheKeys.sipNumber);
        String? sipDomain = _sharedPreferences.getString(CacheKeys.sipDomain);
        var userAtDomain = "${sipNumber!}@${sipDomain!}";

        final mucName = JID.fromString(receiver);
        final nickName = JID.fromString(grpNick);
        final userAccount = JID.fromString(userAtDomain);
        final reason = destroyreasonEditingController;

        final muc = currentLoginState.connection.getManagerById<MUCManager>(mucManager)!;

        if (mucName.toString().isNotEmpty && nickName.toString().isNotEmpty && reason.toString().isNotEmpty) {
          await muc.removeRoom(
            mucName,
            userAccount,
            nickName.toString(),
            reason.toString(),
          );

          await deleteBookmarkCubit.deleteBookmark(
            loginCubit: loginCubit,
            bookmarkListCubit: bookmarkListCubit,
            groupUiCubit: groupUiCubit,
            bookmarkName: receiver,
          );
        }
      }
    } catch (e) {
      debugPrint('Error removing room: $e');
    }
  }

  Future<void> inviteUser({
    required LoginCubit loginCubit,
    required MessagesCubit messagesCubit,
    required MamListCubit mamListCubit,
    required ChatUiCubit chatUiCubit,
    required ContactsCubit contactsCubit,
    required String receiver,
    required String userEditingController,
    required String inviteReasonEditingController,
  }) async {
    try {
      if (loginCubit.state is LoginAuthenticated) {
        LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;

        final mucName = JID.fromString(receiver);
        final userName = JID.fromString(userEditingController);
        final reason = inviteReasonEditingController;
        String? sipNumber = _sharedPreferences.getString(CacheKeys.sipNumber);
        String? sipDomain = _sharedPreferences.getString(CacheKeys.sipDomain);
        var userAtDomain = "${sipNumber!}@${sipDomain!}";

        final muc = currentLoginState.connection.getManagerById<MUCManager>(mucManager)!;

        if (mucName.toString().isNotEmpty) {
          // await deleteBookmarkCubit.deleteBookmark(
          //   deleteContext: context,
          //   bookmarkName: ChatPage.receiver.toString(),
          try {
            var invResult = await muc.inviteUser(
              mucName,
              userName,
              // nickName.toString(),
              reason,
              userAtDomain,
            );
            debugPrint(
                'Invite Result: Group: ${invResult.group}, From: ${invResult.from}, SendTo: ${invResult.sendTo}, Reason: ${invResult.reason}');

            messagesCubit.sendMessage(
              receiverJid: invResult.sendTo,
              loginCubit: loginCubit,
              mamListCubit: mamListCubit,
              chatUiCubit: chatUiCubit,
              contactsCubit: contactsCubit,
              text: "[~Invitation~] You have been invited to ${invResult.group}",
              // isSender: true,
              // sender: 'You',
            );
          } catch (e) {
            debugPrint('Error Sending Invite: $e');
          }
          // );

          // debugPrint('Invite Result: ${invResult}');
        }
      }
    } catch (e) {
      debugPrint('Error removing room: $e');
    }
  }
}

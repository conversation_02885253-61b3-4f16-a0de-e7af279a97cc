import 'package:ddone/components/button/network_indicator.dart';
import 'package:ddone/components/common/common_text_field.dart';
import 'package:ddone/components/dialpad_bottom_buttons.dart';
import 'package:ddone/components/numpad.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/auth/auth_cubit.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/focus/focus_cubit.dart';
import 'package:ddone/cubit/selected_contact/selected_contact_cubit.dart';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/environments/env.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/models/enums/voip_sip_event_enum.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive/hive.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class Dialpad extends StatefulWidget {
  static const routeName = '/dialpad';

  const Dialpad({super.key});

  @override
  State<Dialpad> createState() => _DialpadState();
}

class _DialpadState extends State<Dialpad> with AutomaticKeepAliveClientMixin<Dialpad>, PrefsAware {
  @override
  // keep page alive
  bool get wantKeepAlive => true;

  late HomeCubit _homeCubit;
  late VoipCubit _voipCubit;
  late ContactsCubit _contactsCubit;
  late SelectedContactCubit _selectedContactCubit;
  late FocusCubit _focusCubit;

  late TextEditingController _textController;

  late ScrollController _scrollController;

  late final BoxCollection cdrBox;

  void _handleBackSpace([bool deleteAll = false]) {
    var text = _textController.text;
    if (text.isNotEmpty) {
      setState(() {
        text = deleteAll ? '' : text.substring(0, text.length - 1);
        _textController.text = text;
      });
    }
  }

  bool _onKey(KeyEvent event) {
    final key = event.logicalKey.keyLabel;

    final homeState = _homeCubit.state;
    final voipState = _voipCubit.state;
    final selectedContactState = _selectedContactCubit.state;
    final focusNodeState = _focusCubit.state;

    final navSelectedIndex = homeState.navSelectedIndex;

    bool checkScreen = (isDesktop && navSelectedIndex == 1) || (isMobile && navSelectedIndex == 2);
    bool checkFocus = (!focusNodeState.isContactFocused && !focusNodeState.isDialpadFocused);
    bool checkVoip = (voipState.statusMessage.isEmpty ||
        voipState.statusMessage == VoipSipEvent.hangup.statusMessage() ||
        voipState is VoipSipCalling ||
        voipState is VoipSipTransferCall);

    if (checkScreen && checkVoip && checkFocus && selectedContactState is SelectedContactInitial) {
      if (event is KeyUpEvent) {
        if (key == 'Backspace') {
          _handleBackSpace();
        } else if (key == 'Enter') {
          if (_textController.text.isNotEmpty) {
            var dest = _textController.text;

            _homeCubit.makeCall(
              extNum: dest,
              contactsCubit: _contactsCubit,
              context: context,
            );

            _textController.clear();
          }
        } else if (key.length == 1) {
          // _handleNum(key);
        }
      }
      if (event is KeyRepeatEvent) {
        if (key == 'Backspace') {
          _handleBackSpace(true);
        }
      }
    }

    return false;
  }

  @override
  initState() {
    super.initState();

    _homeCubit = BlocProvider.of<HomeCubit>(context);
    _voipCubit = BlocProvider.of<VoipCubit>(context);
    _contactsCubit = BlocProvider.of<ContactsCubit>(context);
    _selectedContactCubit = BlocProvider.of<SelectedContactCubit>(context);
    _focusCubit = BlocProvider.of<FocusCubit>(context);

    if (!isMobile) {
      _focusCubit.focusNode(_focusCubit.dialpadNode);
    }

    ServicesBinding.instance.keyboard.addHandler(_onKey);

    _textController = TextEditingController(text: prefs.getString('dest') ?? '');

    _scrollController = ScrollController();

    _textController.addListener(() {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    });
  }

  @override
  void dispose() async {
    // await stopWatchTimer.dispose();  // Need to call dispose function.
    ServicesBinding.instance.keyboard.removeHandler(_onKey);

    _textController.dispose();

    _scrollController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return BlocListener<AuthCubit, AuthState>(
      listener: (context, authState) {
        if (authState is AuthSuccess) {
              _contactsCubit.getContactList();
        }
      },
      child: BlocListener<HomeCubit, HomeState>(
      listener: (context, homeState) {
        if (homeState.prefilledDailpad != null) {
          setState(() {
            _textController.text = homeState.prefilledDailpad!;
          });
          _homeCubit.clearPrefilledDailpad();
        }
      },
      child: GestureDetector(
        onTap: () {
          _focusCubit.focusNode(_focusCubit.dialpadNode);
        },
        child: BlocBuilder<ThemeCubit, ThemeState>(
          builder: (context, themeState) {
            final colorTheme = themeState.colorTheme;

            return Scaffold(
              appBar: AppBar(
                centerTitle: true,
                backgroundColor: colorTheme.surfaceColor,
                title: const Text('Dial'),
                leading: isMobile ? const NetworkIndicatorButton() : null,
              ),
              body: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: maxWidthForDesktop),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CommonTextField(
                        readOnly: isMobile,
                        onFocusChange: (value) {
                          if (value) {
                            _focusCubit.focusDialpadField();
                          } else {
                            _focusCubit.unfocusDialpadField();
                          }
                        },
                        focusNode: _focusCubit.dialpadNode,
                        textEditingController: _textController,
                        scrollController: _scrollController,
                        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                        // textFieldHeight: heightMedium,
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(
                        height: context.responsiveSize<double>(
                          moileSize: spacingMedium,
                          tabletSize: spacingLarge,
                          desktopSize: spacingLarge,
                          largeScreenSize: spacingLarge,
                        ),
                      ),
                      Column(
                        children: [
                          Numpad(
                            callBack: (value) {
                              setState(() {
                                _textController.text += value;
                              });
                            },
                          ),
                          SizedBox(
                            height: context.responsiveSize<double>(
                              moileSize: 0,
                              tabletSize: spacingLarge,
                              desktopSize: spacingLarge,
                              largeScreenSize: spacingLarge,
                            ),
                          ),
                          DialpadBottomButtons(
                            onLeftButtonClick: () {
                              EasyLoadingService().showInfoWithText('${env!.appName} v${env!.appVersion}');
                            },
                            onMiddleButtonClick: () {
                              if (_textController.text.isNotEmpty) {
                                final dialedExt = _textController.text;
                                prefs.setString(CacheKeys.lastDialed, dialedExt);
                                _homeCubit.makeCall(
                                  extNum: dialedExt,
                                  contactsCubit: _contactsCubit,
                                  context: context,
                                );
                                _textController.clear();
                              } else {
                                final lastDialed = prefs.getString(CacheKeys.lastDialed);
                                if (lastDialed != null && lastDialed.isNotEmpty) {
                                  setState(() {
                                    _textController.text = lastDialed;
                                  });
                                }
                              }
                            },
                            onRightButtonClick: () {
                              _handleBackSpace();
                            },
                            onRightButtonLongPress: () {
                              _handleBackSpace(true);
                            },
                            leftIcon: Icons.info,
                            middleIcon: Icons.call,
                            rightIcon: Icons.backspace_outlined,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    ),
    );
  }
}

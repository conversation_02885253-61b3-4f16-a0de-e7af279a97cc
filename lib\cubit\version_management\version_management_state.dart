part of 'version_management_cubit.dart';

abstract class VersionManagementState extends Equatable {
  const VersionManagementState();

  @override
  List<Object> get props => [];
}

class VersionInitial extends VersionManagementState {}

class VersionChecking extends VersionManagementState {}

class VersionOutdated extends VersionManagementState {
  final bool forceUpdate;
  final String curerntVersion;
  final String latestVersion;
  final String updateUrl;

  const VersionOutdated(
      {required this.forceUpdate, required this.curerntVersion, required this.latestVersion, required this.updateUrl});

  @override
  List<Object> get props => [forceUpdate, curerntVersion, latestVersion];
}

class VersionUpToDate extends VersionManagementState {}

class VersionCheckFailed extends VersionManagementState {
  final String error;

  const VersionCheckFailed({required this.error});

  @override
  List<Object> get props => [error];
}

// import 'package:flutter/material.dart';
// import 'package:flutter/foundation.dart' show kIsWeb;

// class MyApp extends StatelessWidget {
//   final Uri dynamicUrl;
//   const MyApp(this.dynamicUrl, {super.key});

//   @override
//   Widget build(BuildContext context) {
//     return MaterialApp(
//       title: 'My App',
//       home: FutureBuilder(
//         future: _loadWidgetTree(),
//         builder: (BuildContext context, AsyncSnapshot<Widget> snapshot) {
//           if (snapshot.hasData) {
//             return snapshot.data!;
//           } else {
//             return Container(
//               color: Colors.white,
//               child: const Center(
//                 child: CircularProgressIndicator(),
//               ),
//             );
//           }
//         },
//       ),
//     );
//   }

//   Future<Widget> _loadWidgetTree() async {
//     if (kIsWeb) {
//       // Load the widget tree using dynamic imports for web
//       final module = await require(dynamicUrl.toString());
//       final Widget widget = module.default();
//       return widget;
//     } else {
//       // Load the widget tree normally for mobile
//       final Widget widget = await rootBundle.loadString('assets/app.dart')
//           .then((value) => runZoned(() => eval(value)));
//       return widget;
//     }
//   }
// }
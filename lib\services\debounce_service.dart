import 'dart:async';

import 'package:flutter/material.dart';

/// Used to delay an action according to the time given
class Debouncer {
  int? milliseconds;
  VoidCallback? action;
  Timer? _timer;

  Debouncer({
    this.milliseconds = 500,
  });

  run(VoidCallback action) {
    if (_timer != null) {
      _timer!.cancel();
    }
    _timer = Timer(
      Duration(
        milliseconds: milliseconds!,
      ),
      action,
    );
  }
}

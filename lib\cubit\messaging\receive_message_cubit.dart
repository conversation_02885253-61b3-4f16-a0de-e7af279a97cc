import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'receive_message_state.dart';

class ReceiveMessageCubit extends Cubit<ReceiveMessageState> {
  ReceiveMessageCubit._() : super(const ReceiveMessageInitial());

  factory ReceiveMessageCubit.initial() {
    return ReceiveMessageCubit._();
  }

  void receiveMessage({
    required String jid,
    required String name,
    required String message,
    required bool isGroup,
  }) {
    emit(
      ReceiveMessageUpdated(
        jid: jid,
        name: name,
        message: message,
        isGroup: isGroup,
      ),
    );
  }
}

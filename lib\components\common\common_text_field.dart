import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CommonTextField extends StatelessWidget {
  final TextEditingController textEditingController;
  final ScrollController? scrollController;
  final Function(bool)? onFocusChange;
  final Function(String)? onValueChanged;
  final VoidCallback? onTap;
  final String? hintText;
  final Icon? prefixIcon;
  final bool readOnly;
  final Color? prefixIconColor;
  final FocusNode? focusNode;
  final TextInputType? textInputType;
  final double? textFieldHeight, textFieldWidth;
  final TextAlign? textAlign;
  final TextStyle? textStyle;
  final List<TextInputFormatter>? inputFormatters;

  const CommonTextField({
    required this.textEditingController,
    this.scrollController,
    this.onFocusChange,
    this.onValueChanged,
    this.onTap,
    this.hintText,
    this.prefixIcon,
    this.prefixIconColor,
    this.readOnly = false,
    this.focusNode,
    this.textInputType,
    this.textFieldHeight,
    this.textFieldWidth,
    this.textAlign,
    this.textStyle,
    this.inputFormatters,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        final defaultTextFieldWidth = context.responsiveSize<double>(
          moileSize: dialpadFieldWidth,
          tabletSize: dialpadFieldWidth,
          desktopSize: dialpadFieldWidth,
          largeScreenSize: dialpadFieldWidth,
        );

        final defaultTextFieldHeight = context.responsiveSize<double>(
          moileSize: context.deviceHeight(0.08),
          tabletSize: heightMedium,
          desktopSize: heightMedium,
          largeScreenSize: heightMedium,
        );

        final defaultTextStyle = context.responsiveSize<TextStyle>(
          moileSize: textTheme.titleMedium!,
          tabletSize: textTheme.displaySmall!,
          desktopSize: textTheme.displaySmall!,
          largeScreenSize: textTheme.displaySmall!,
        );

        final defaultContentPadding = context.responsiveSize<EdgeInsets>(
          moileSize: const EdgeInsets.symmetric(
            horizontal: 5,
            vertical: 10,
          ),
          tabletSize: const EdgeInsets.symmetric(
            horizontal: 5,
            vertical: 15,
          ),
          desktopSize: const EdgeInsets.symmetric(
            horizontal: 5,
            vertical: 15,
          ),
          largeScreenSize: const EdgeInsets.symmetric(
            horizontal: 5,
            vertical: 15,
          ),
        );

        return Container(
          width: textFieldWidth ?? defaultTextFieldWidth,
          height: textFieldHeight ?? defaultTextFieldHeight,
          decoration: const BoxDecoration(
            color: Color.fromARGB(255, 60, 60, 60),
            borderRadius: BorderRadius.all(
              Radius.circular(radiusLarge),
            ),
          ),
          child: Focus(
            onFocusChange: onFocusChange,
            child: Center(
              child: TextField(
                focusNode: focusNode,
                key: key,
                keyboardType: textInputType ?? TextInputType.text,
                cursorColor: colorTheme.primaryColor,
                textAlign: textAlign ?? TextAlign.left,
                textAlignVertical: TextAlignVertical.center,
                style: textStyle ?? defaultTextStyle.copyWith(color: colorTheme.onPrimaryColor),
                inputFormatters: inputFormatters,
                decoration: InputDecoration(
                  filled: false,
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  disabledBorder: InputBorder.none,
                  errorBorder: InputBorder.none,
                  focusedErrorBorder: InputBorder.none,
                  contentPadding: defaultContentPadding,
                  hintText: hintText ?? '',
                  hintMaxLines: 1,
                  hintStyle: defaultTextStyle.copyWith(color: colorTheme.primaryColor.withOpacity(opacityMedium)),
                  prefixIcon: prefixIcon,
                  prefixIconColor: prefixIconColor ?? colorTheme.primaryColor.withOpacity(opacityMedium),
                ),
                controller: textEditingController,
                scrollController: scrollController,
                onChanged: onValueChanged,
                onTap: onTap,
                readOnly: readOnly,
                maxLines: 1,
              ),
            ),
          ),
        );
      },
    );
  }
}

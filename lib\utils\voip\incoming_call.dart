import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:ddone/constants/regex_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/voip/voip_cubit.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/models/enums/hive/call_type_enum.dart';
import 'package:ddone/models/hive/call_records.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/voip/accepted_call.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class IncomingCallDialog extends StatefulWidget {
  final String? callerID, caller;
  final VoidCallback onAccept, onDecline;

  const IncomingCallDialog({
    required this.callerID,
    required this.caller,
    required this.onAccept,
    required this.onDecline,
    super.key,
  });

  @override
  State<IncomingCallDialog> createState() => _IncomingCallDialogState();
}

class _IncomingCallDialogState extends State<IncomingCallDialog> with WidgetsBindingObserver, PrefsAware {
  late VoipCubit _voipCubit;
  late HiveService _hiveService;

  void removeWhenNotCalling() {
    if (_voipCubit.state is! VoipSipIncomingCall && mounted) {
      pop();
    }
  }

  void removeWhenInCall() {
    if (_voipCubit.state is VoipSipAccepted && mounted) {
      pop();
    }
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);

    _voipCubit = BlocProvider.of<VoipCubit>(context);
    _hiveService = sl.get<HiveService>();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      removeWhenNotCalling();
      removeWhenInCall();
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {}
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<VoipCubit, VoipState>(
      listener: (context, voipState) {
        if (voipState is VoipSipHangup) {
          popUntilInitial();

          _hiveService.addData<CallRecords>(
            data: CallRecords(
              contactName: widget.callerID ?? 'Unknown',
              did: widget.caller ?? 'Unknown',
              duration: '0:00',
              type: CallType.missed,
              datetime: DateTime.now(),
            ),
          );
        } else if (voipState is VoipSipAccepted) {
          // Close the incoming call dialog since the accepted call screen
          // will be handled centrally in home.dart
          popUntilInitial();
        } else if (voipState is VoipSipAcceptedError) {
          popUntilInitial();
        }
      },
      child: BlocBuilder<ThemeCubit, ThemeState>(
        builder: (context, themeState) {
          final colorTheme = themeState.colorTheme;
          final textTheme = themeState.themeData.textTheme;

          return AlertDialog(
            title: Text(
              'Incoming call',
              style: TextStyle(
                color: Colors.orange,
                fontSize: context.deviceWidth(0.03),
              ),
            ),
            insetPadding: const EdgeInsets.all(0),
            backgroundColor: const Color.fromARGB(255, 54, 54, 54),
            content: SizedBox(
              width: context.deviceWidth(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Divider(
                    indent: 8.0,
                    endIndent: 8.0,
                  ),
                  SizedBox(height: context.deviceWidth(0.03)),
                  Text(
                    widget.callerID ?? 'Unknown',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: context.deviceWidth(0.03),
                    ),
                  ),
                  SizedBox(height: context.deviceWidth(0.03)),
                  Text(
                    widget.caller ?? 'Unknown',
                    style: textTheme.displaySmall!.copyWith(color: colorTheme.onBackgroundColor),
                  ),
                  SizedBox(height: context.deviceWidth(0.03)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      RoundShapeInkWell(
                        color: colorTheme.connectedColor,
                        contentWidget: Icon(
                          Icons.call,
                          color: colorTheme.onPrimaryColor,
                          size: context.deviceWidth(0.04),
                        ),
                        onTap: widget.onAccept,
                      ),
                      SizedBox(
                        width: context.deviceWidth(0.1),
                      ),
                      RoundShapeInkWell(
                        checkNetwork: false,
                        color: colorTheme.errorColor,
                        contentWidget: Icon(
                          Icons.close,
                          color: colorTheme.onPrimaryColor,
                          size: context.deviceWidth(0.04),
                        ),
                        onTap: () {
                          popUntilInitial();

                          widget.onDecline();
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

void showAcceptedCallDialog(
  BuildContext context, {
  required HomeCubit homeCubit,
  required String caller,
  required String callerID,
}) async {
  final HiveService hiveService = sl.get<HiveService>();

  if (caller.startsWith('sip:')) {
    Match? match = sipNumberRegex.firstMatch(caller);
    if (match != null) {
      String extractedNumber = match.group(1) ?? '';
      caller = extractedNumber;
    }
  } else if (callerID.startsWith('"') && callerID.length > 1) {
    callerID = callerID.substring(1, callerID.length - 1);
  }

  DateTime timeCalled = DateTime.now();

  homeCubit.startStopWatch();

  showDialog(
    barrierDismissible: false,
    context: context,
    builder: (BuildContext context) {
      return AcceptedCallDialog(
        caller: caller,
        callerID: callerID,
        onDecline: (displayTime) {
          hiveService.addData<CallRecords>(
            data: CallRecords(
              contactName: callerID,
              did: caller,
              duration: displayTime,
              type: CallType.answered,
              datetime: timeCalled,
            ),
          );
        },
      );
    },
  );
}

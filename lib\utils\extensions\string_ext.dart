import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/service_locator.dart';
import 'package:shared_preferences/shared_preferences.dart';

extension StringExtension on String {
  String mucIDFilter() {
    final sharedPreferences = sl.get<SharedPreferences>();
    final sipDomain = sharedPreferences.getString(CacheKeys.sipDomain);
    return replaceAll('@muc.$sipDomain', '');
  }

  String userIDFilter() {
    final sharedPreferences = sl.get<SharedPreferences>();
    final sipDomain = sharedPreferences.getString(CacheKeys.sipDomain);
    return replaceAll('@$sipDomain', '');
  }
}

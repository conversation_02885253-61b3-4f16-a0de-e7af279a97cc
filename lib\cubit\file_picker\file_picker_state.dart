part of 'file_picker_cubit.dart';

sealed class FilePickerState extends Equatable {
  const FilePickerState();

  @override
  List<Object> get props => [];
}

final class FilePickerInitial extends FilePickerState {}

final class FilePickerSelected extends FilePickerState {
  final FilePickerResult selectedFile;
  const FilePickerSelected({
    required this.selectedFile,
  });

  @override
  List<Object> get props => super.props..addAll([selectedFile]);
}

final class FilePickerUploading extends FilePickerState {}

final class FilePickerUploaded extends FilePickerState {}

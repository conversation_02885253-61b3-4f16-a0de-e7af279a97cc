import 'package:ddone/screens/about_me.dart';
import 'package:ddone/screens/call_history.dart';
import 'package:ddone/screens/chat_recent.dart';
import 'package:ddone/screens/contacts.dart';
import 'package:ddone/screens/dd_meet.dart';
import 'package:ddone/screens/dialpad.dart';
import 'package:ddone/screens/dialscreen_windows.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter/material.dart';

typedef PageViewDef = List<({PageViewNameEnum name, Widget widget})>;

enum PageViewNameEnum {
  // PageView in home
  callHistory,
  dialScreenWindows,
  contactList,
  dailpad,
  recentChat,
  ddMeet,
  aboutMe,

  // PageView in chatRecent
  chatAll,
  chatDirectMessage,
  chatGroupMessage;
}

final PageViewDef homePageViewDefinition = isDesktop
    ? [
        (name: PageViewNameEnum.callHistory, widget: const CallHistory()),
        (name: PageViewNameEnum.dialScreenWindows, widget: const DialScreenWindows()),
        (name: PageViewNameEnum.recentChat, widget: const RecentChat()),
        (name: PageViewNameEnum.ddMeet, widget: const DDMeet()),
        (name: PageViewNameEnum.aboutMe, widget: const AboutMe()),
      ]
    : [
        (name: PageViewNameEnum.callHistory, widget: const CallHistory()),
        (name: PageViewNameEnum.contactList, widget: const ContactList()),
        (name: PageViewNameEnum.dailpad, widget: const Dialpad()),
        (name: PageViewNameEnum.recentChat, widget: const RecentChat()),
        (name: PageViewNameEnum.ddMeet, widget: const DDMeet()),
        (name: PageViewNameEnum.aboutMe, widget: const AboutMe()),
      ];

int getPageViewIndex(PageViewDef definitions, PageViewNameEnum name) {
  for (int i = 0; i < definitions.length; i++) {
    if (definitions[i].name == name) {
      return i;
    }
  }
  throw ArgumentError('No such widget. PageViewNameEnum=$name');
}

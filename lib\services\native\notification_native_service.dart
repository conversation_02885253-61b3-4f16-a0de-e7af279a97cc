import 'dart:io';

import 'package:flutter/services.dart';

class NotificationNativeService {
  static const platform = MethodChannel('notificationPlatform');

  Future<bool> requestNotificationPermissionMacOS() async {
    if (!Platform.isMacOS) return false;

    return await platform.invokeMethod<bool>('requestPermission') ?? false;
  }

  void openNotificationCenterMacOS() {
    if (!Platform.isMacOS) return;

    platform.invokeMethod<bool>('openNotificationCenter');
  }

  Future<bool> checkNotificationPermissionMacOS() async {
    if (!Platform.isMacOS) return false;

    return await platform.invokeMethod<bool>('checkNotificationPermission') ?? false;
  }
}

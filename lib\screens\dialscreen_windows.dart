import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/selected_contact/selected_contact_cubit.dart';
import 'package:ddone/cubit/selected_group/selected_group_cubit.dart';
import 'package:ddone/screens/contacts.dart';
import 'package:ddone/screens/dialpad.dart';
import 'package:ddone/screens/selected_contact.dart';
import 'package:ddone/screens/selected_group.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DialScreenWindows extends StatefulWidget {
  const DialScreenWindows({super.key});

  @override
  State<DialScreenWindows> createState() => _DialScreenWindowsState();
}

class _DialScreenWindowsState extends State<DialScreenWindows> {
  // bool _isContainerVisible = false;

  // void _toggleContainer(bool isOpen) {
  //   setState(() {
  //     if (isOpen) {
  //       _isContainerVisible = true;
  //     } else {
  //       _isContainerVisible = false;
  //     }
  //   });
  // }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Expanded(child: ContactList()),
        SizedBox(
          width: widthTiny,
          height: double.infinity,
          child: Container(
            color: Colors.black,
          ),
        ),
        Expanded(
          child: BlocBuilder<SelectedGroupCubit, SelectedGroupState>(
            builder: (context, selectedGroupState) {
              return BlocBuilder<SelectedContactCubit, SelectedContactState>(
                builder: (context, selectedContactstate) {
                  return Stack(
                    children: [
                      const Dialpad(),
                      if (selectedContactstate is SelectedContactLoaded) const SelectedContact(),
                      if (selectedGroupState is SelectedGroupLoaded) const SelectedGroup(),
                    ],
                  );
                },
              );
            },
          ),
        )
      ],
    );
  }
}

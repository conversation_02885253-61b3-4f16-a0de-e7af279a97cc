import 'package:bloc/bloc.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:equatable/equatable.dart';

part 'selected_group_state.dart';

class SelectedGroupCubit extends Cubit<SelectedGroupState> {
  HiveService hiveService;

  SelectedGroupCubit._({SelectedGroupState? state})
      : hiveService = sl.get<HiveService>(),
        super(state ?? const SelectedGroupInitial());

  factory SelectedGroupCubit.initial({SelectedGroupState? state}) {
    //the case of is register only happen when you mock the cubit in test case
    if (sl.isRegistered<SelectedGroupCubit>()) {
      return sl.get<SelectedGroupCubit>();
    }

    return SelectedGroupCubit._(state: state);
  }

  void setGroupInfo({
    required String groupName,
    required String groupJid,
    required String autoJoin,
    required String nick,
  }) {
    emit(
      SelectedGroupLoaded(
        groupName: groupName,
        groupJid: groupJid,
        autoJoin: autoJoin,
        nick: nick,
      ),
    );
  }

  void removeSelectedGroupInfo() => emit(const SelectedGroupInitial());

  // void addOrRemoveFavourite({bool? removeOnly = false}) {
  //   final List<FavouriteContact> favouriteContactList = hiveService.getAllData<FavouriteContact>();

  //   for (int i = 0; i < favouriteContactList.length; i++) {
  //     final favouriteContact = favouriteContactList[i];

  //     if (favouriteContact.contactNumber == state.contactId && favouriteContact.displayName == state.displayName) {
  //       hiveService.deleteDataByIndex<FavouriteContact>(index: i);

  //       emit(
  //         SelectedGroupLoaded(
  //           displayName: state.displayName,
  //           contactId: state.contactId,
  //           sipAddress: state.sipAddress,
  //         ),
  //       );

  //       return;
  //     }
  //   }

  //   if (removeOnly == true) {
  //     return;
  //   }

  //   if (favouriteContactList.length == 10) {
  //     EasyLoadingService().showInfoWithText('You have reached maximum of 10 favourite contacts.');

  //     return;
  //   }

  //   hiveService.addData<FavouriteContact>(
  //     data: FavouriteContact(
  //       contactNumber: state.contactId,
  //       isLocalContact: state.isLocalContact,
  //       uri: state.sipAddress,
  //       displayName: state.displayName,
  //     ),
  //   );

  //   emit(
  //     SelectedGroupLoaded(
  //       displayName: state.displayName,
  //       contactId: state.contactId,
  //       sipAddress: state.sipAddress,
  //       isLocalContact: state.isLocalContact,
  //       isFavouriteContact: true,
  //     ),
  //   );
  // }
}

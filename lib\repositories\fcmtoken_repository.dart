import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'fcmtoken_repository.g.dart';

@RestApi()
abstract class FcmtokenRepository {
  factory FcmtokenRepository(Dio dio, {String baseUrl}) = _FcmtokenRepository;

  @POST('ddoneToken')
  Future<HttpResponse<void>> storeFcmtoken(@Body() dynamic params);

  @POST('ddoneLogout')
  Future<HttpResponse<void>> deleteFcmtoken(@Body() dynamic params);

  @POST('ddoneCheckRegistered')
  Future<HttpResponse<String>> checkRegistered(@Body() dynamic params);
}

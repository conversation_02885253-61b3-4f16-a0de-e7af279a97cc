import 'dart:async';

import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/models/download_result_model.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/download_file_service.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';

class DisplayVideo extends StatefulWidget {
  final String videoUrl;
  const DisplayVideo({
    required this.videoUrl,
    super.key,
  });

  @override
  State<DisplayVideo> createState() => _DisplayVideoState();
}

class _DisplayVideoState extends State<DisplayVideo> {
  final DownloadFileService _downloadService = sl.get<DownloadFileService>();

  late final Player _player;
  late final VideoController _videoController;
  StreamSubscription<Duration>? _positionSubscription;
  StreamSubscription<Duration>? _durationSubscription;
  StreamSubscription<bool>? _playingSubscription;

  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;

  bool _showControls = false;
  bool showPlayer = false;
  bool isPlaying = true;
  bool _isDownloading = false;

  @override
  void initState() {
    super.initState();
    _player = Player();
    _videoController = VideoController(_player);

    setVideo();

    // Listen to player state changes
    _positionSubscription = _player.stream.position.listen((position) {
      if (mounted) {
        setState(() {
          _currentPosition = position;
        });
      }
    });

    _durationSubscription = _player.stream.duration.listen((duration) {
      if (mounted) {
        setState(() {
          _totalDuration = duration;
        });
      }
    });

    _playingSubscription = _player.stream.playing.listen((playing) {
      if (mounted) {
        setState(() {
          isPlaying = playing;
        });
      }
    });
  }

  @override
  void dispose() {
    _positionSubscription?.cancel();
    _durationSubscription?.cancel();
    _playingSubscription?.cancel();
    _player.dispose();
    super.dispose();
  }

  Future<void> setVideo() async {
    try {
      await _player.open(Media(widget.videoUrl));
      await _player.setVolume(10.0); // Media Kit uses 0-100 scale

      setState(() {
        showPlayer = true;
        isPlaying = true;
      });
    } catch (e) {
      debugPrint('Error initializing video: $e');

      setState(() {
        showPlayer = false;
      });
    }
  }

  void togglePlayPause() {
    _player.playOrPause();
  }

  void _onSeek(double value) {
    _player.seek(Duration(seconds: value.toInt()));
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return [if (duration.inHours > 0) hours, minutes, seconds].join(':');
  }

  Future<void> _saveVideo() async {
    if (_isDownloading) return;

    setState(() {
      _isDownloading = true;
    });
    EasyLoadingService().showLoadingWithText('Downloading...');

    final result = await _downloadService.saveFileToDownloads(widget.videoUrl);

    setState(() {
      _isDownloading = false;
    });

    EasyLoadingService().dismissLoading();
    switch (result.status) {
      case DownloadResultStatus.successful:
        String successText = 'Saved!';
        if (isIOS) {
          successText = 'Saved to Files!';
        } else if (isAndroid) {
          successText = 'Saved to Downloads!';
        } else if (isWindows) {
          successText = 'Saved to Downloads!';
        }
        EasyLoadingService().showSuccessWithText(successText);
        break;
      case DownloadResultStatus.failed:
      case DownloadResultStatus.fileNotAvailable:
        EasyLoadingService().showErrorWithText(result.message ?? 'Download failed');
        break;
      case DownloadResultStatus.canceled:
      case DownloadResultStatus.paused:
        EasyLoadingService().showInfoWithText(result.message ?? 'Download interrupted');
        break;
      case DownloadResultStatus.fileAlreadyExists:
        EasyLoadingService().showInfoWithText('File already exists.');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;
        return GestureDetector(
          onTap: pop,
          child: Scaffold(
            appBar: AppBar(
              backgroundColor: colorTheme.backgroundColor,
              elevation: 0,
              actions: [
                IconButton(
                  onPressed: _isDownloading ? null : _saveVideo,
                  icon: _isDownloading
                      ? SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2.0,
                            color: colorTheme.primaryColor,
                          ),
                        )
                      : const Icon(
                          Icons.download,
                          size: iconSizeLarge,
                        ),
                  tooltip: _isDownloading ? 'Downloading...' : 'Download Image',
                ),
              ],
            ),
            body: GestureDetector(
              onTap: togglePlayPause,
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: MouseRegion(
                    onEnter: (_) => setState(() {
                      _showControls = true;
                    }),
                    onExit: (_) => setState(() {
                      _showControls = false;
                    }),
                    child: SizedBox(
                      // height: context.deviceHeight(),
                      width: context.deviceWidth(0.7),
                      child: AspectRatio(
                        aspectRatio: 16 / 9,
                        child: showPlayer
                            ? Stack(
                                children: [
                                  Video(controller: _videoController),
                                  _showControls
                                      ? Positioned(
                                          bottom: 0,
                                          left: 0,
                                          right: 0,
                                          child: Row(
                                            children: [
                                              IconButton(
                                                onPressed: togglePlayPause,
                                                icon: Icon(
                                                  isPlaying ? Icons.pause_rounded : Icons.play_arrow_rounded,
                                                ),
                                              ),
                                              Text(
                                                _formatDuration(_currentPosition),
                                                style: textTheme.bodyLarge?.copyWith(color: Colors.white),
                                              ),
                                              Expanded(
                                                child: SizedBox(
                                                  height: 30,
                                                  child: Slider(
                                                    activeColor: colorTheme.primaryColor.withOpacity(0.8),
                                                    // secondaryTrackValue: 0.5,
                                                    value: _currentPosition.inSeconds.toDouble(),
                                                    max: _totalDuration.inSeconds.toDouble(),
                                                    onChanged: (_) {},
                                                    onChangeEnd: _onSeek,
                                                  ),
                                                ),
                                              ),
                                              Text(
                                                _formatDuration(_totalDuration),
                                                style: textTheme.bodyLarge?.copyWith(color: Colors.white),
                                              ),
                                            ],
                                          ),
                                        )
                                      : const SizedBox(),
                                ],
                              )
                            : const Center(
                                child: CircularProgressIndicator(),
                              ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

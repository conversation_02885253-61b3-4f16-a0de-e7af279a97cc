import 'dart:async';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:collection/collection.dart';
import 'package:ddone/components/chat_bubble/document_chat_bubble.dart';
import 'package:ddone/components/chat_bubble/video_chat_bubble.dart';
import 'package:ddone/constants/chat_emoji_constants.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/constants/regex_constants.dart';
import 'package:ddone/constants/value_constants.dart';
import 'package:ddone/cubit/chat/chat_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/members_presence/members_presence_cubit.dart';
import 'package:ddone/environments/env.dart';
import 'package:ddone/cubit/add_bookmark/add_bookmark_cubit.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/delete_bookmark/delete_bookmark_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/messaging/messages_cubit.dart';
import 'package:ddone/cubit/update/chat_ui_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/cubit/update/info_cubit.dart';

import 'package:ddone/service_locator.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/services/notification_service.dart';
import 'package:ddone/services/shared_preferences_service.dart';
import 'package:ddone/services/xmpp_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/extensions/string_ext.dart';
import 'package:ddone/utils/pop_out_util.dart';
import 'package:ddone/components/chat_bubble/decline_chat_bubble.dart';
import 'package:ddone/components/chat_bubble/invitation_chat_bubble.dart';
import 'package:ddone/components/file_picker.dart';
import 'package:ddone/components/member_dropdown.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:logger/logger.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/models/xmpp_model/mam_info.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:shimmer/shimmer.dart';

class ChatPage extends StatefulWidget {
  static const String routeName = '/chatPage';
  // final bool isContainerVisible;
  // final Function toggleContainer;
  const ChatPage({
    super.key,
    // required this.isContainerVisible,
    // required this.toggleContainer,
  });
  // static String? receiver;
  // static String? grpNick;

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> with WidgetsBindingObserver {
  final TextEditingController _textEditingController = TextEditingController();
  final TextEditingController _destroyreasonEditingController = TextEditingController();
  final TextEditingController _userEditingController = TextEditingController();
  final TextEditingController _invitereasonEditingController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  late final SharedPreferencesService prefs;
  late final Logger log;
  late final NotificationService notificationService;

  String defaultMuc = '@x001.dotdashtech.com';
  final FocusNode _focusNode = FocusNode();
  String receivedMessages = '';
  bool _showError = false;

  late DeleteBookmarkCubit _deleteBookmarkCubit;
  late AddBookmarkCubit _addBookmarkCubit;
  late MessagesCubit _messagesCubit;
  late LoginCubit _loginCubit;
  late MamListCubit _mamListCubit;
  late ChatUiCubit _chatUiCubit;
  late GroupUiCubit _groupUiCubit;
  late BookmarkListCubit _bookmarkListCubit;
  late ChatCubit _chatCubit;
  // late FilePickerCubit _filePickerCubit;
  late InfoCubit _infoCubit;
  late ContactsCubit _contactsCubit;
  // late SelectedContactCubit _selectedContactCubit;
  // late SelectedGroupCubit _selectedGroupCubit;
  // late MemberCubit _memberCubit;

  bool _isConnected = false;
  bool _isRefreshing = false;

  Timer? _refreshTimer;
  static const Duration refreshInterval = Duration(seconds: 5);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    _focusNode.requestFocus();
    _messagesCubit = BlocProvider.of<MessagesCubit>(context);
    _loginCubit = BlocProvider.of<LoginCubit>(context);
    _mamListCubit = BlocProvider.of<MamListCubit>(context);
    _chatUiCubit = BlocProvider.of<ChatUiCubit>(context);
    _groupUiCubit = BlocProvider.of<GroupUiCubit>(context);
    _bookmarkListCubit = BlocProvider.of<BookmarkListCubit>(context);
    _infoCubit = BlocProvider.of<InfoCubit>(context);
    _contactsCubit = BlocProvider.of<ContactsCubit>(context);
    // _selectedContactCubit = BlocProvider.of<SelectedContactCubit>(context);
    // _selectedGroupCubit = BlocProvider.of<SelectedGroupCubit>(context);
    // _memberCubit = BlocProvider.of<MemberCubit>(context);

    _chatCubit = ChatCubit.initial();
    _addBookmarkCubit = AddBookmarkCubit.initial();
    _deleteBookmarkCubit = DeleteBookmarkCubit.initial();
    // _filePickerCubit = FilePickerCubit.initial();

    if (_loginCubit.state is LoginAuthenticated) {
      final LoginAuthenticated loginState = _loginCubit.state as LoginAuthenticated;

      if (loginState.connection.isAuthenticated) {
        setState(() {
          _isConnected = true;
        });
      } else {
        setState(() {
          _isConnected = false;
        });
      }
    }

    prefs = sl.get<SharedPreferencesService>();
    log = sl.get<Logger>();
    notificationService = sl.get<NotificationService>();

    _scrollController.addListener(_onScroll);

    startRefreshTimer();
  }

  @override
  void dispose() {
    notificationService.removeActiveChat(); // reset it when Chat screen is removed.
    WidgetsBinding.instance.removeObserver(this);
    stopRefreshTimer();
    _scrollController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      startRefreshTimer();
    } else if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.inactive ||
        state == AppLifecycleState.detached) {
      stopRefreshTimer();
    }
  }

  Future<void> refreshContent() async {
    if (_infoCubit.state.receiver.isNotEmpty && _loginCubit.state is LoginAuthenticated) {
      log.t('Chat - refreshContent');
      String? sipNumber = prefs.getString(CacheKeys.sipNumber);
      String? sipDomain = prefs.getString(CacheKeys.sipDomain);
      final loginAuthenticatedState = _loginCubit.state as LoginAuthenticated;
      final mam = loginAuthenticatedState.connection.getManagerById<XmppService>(mamManager)!;
      if (_infoCubit.state.isGroupInfo()) {
        log.t('Chat - refreshContent - group');
        MamListCubit.test.clear();
        await mam.requestGroupMessage(
          JID.fromString(_infoCubit.state.name),
        );
      } else {
        log.t('Chat - refreshContent - direct message');
        MamListCubit.stanza.clear();
        await mam.requestMessage(
          JID.fromString('$sipNumber@$sipDomain'),
        );
      }
      _mamListCubit.getMamList();
    }
  }

  void startRefreshTimer() {
    log.t('Chat - startRefreshTimer');
    _refreshTimer?.cancel();
    refreshContent(); // initial refresh
    _refreshTimer = Timer.periodic(refreshInterval, (_) => refreshContent());
  }

  void stopRefreshTimer() {
    log.t('Chat - stopRefreshTimer');
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  void _onScroll() {
    // if (_scrollController.position.pixels == 0 && !_isRefreshing && !_infoCubit.state.receiver.contains('muc')) {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent &&
        !_isRefreshing &&
        !_infoCubit.state.isGroupInfo()) {
      // onChatRefresh();
    }

    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent &&
        !_isRefreshing &&
        _infoCubit.state.isGroupInfo()) {
      // onGroupRefresh();
    }
  }

  Future<void> onChatRefresh() async {
    setState(() {
      _isRefreshing = true;
    });

    setState(() {
      _infoCubit.getLoadChatHistory(
          receiver: _infoCubit.state.receiver,
          loginCubit: _loginCubit,
          mamListCubit: _mamListCubit,
          beforeId: _mamListCubit.state.mamList[_infoCubit.state.receiver]!.first.beforeId
          // isContainerVisible: widget.isContainerVisible,
          // toggleContainer: widget.toggleContainer,
          );
      _isRefreshing = false;
    });
  }

  Future<void> onGroupRefresh() async {
    setState(() {
      _isRefreshing = true;
    });

    setState(() {
      _infoCubit.getLoadGroupChatHistory(
          receiver: _infoCubit.state.receiver,
          nick: _infoCubit.state.nick,
          name: _infoCubit.state.name,
          loginCubit: _loginCubit,
          mamListCubit: _mamListCubit,
          // memberCubit: _memberCubit,
          beforeId: _mamListCubit.state.mamList[_infoCubit.state.receiver]!.first.beforeId
          // isContainerVisible: widget.isContainerVisible,
          // toggleContainer: widget.toggleContainer,
          );
      _isRefreshing = false;
    });
  }

  void _openFilePicker(BuildContext context) async {
    await showModalBottomSheet<FilePickerResult?>(
        backgroundColor: context.colorTheme().surface,
        context: context,
        isDismissible: false,
        // showDragHandle: true,
        enableDrag: false,
        builder: (BuildContext context) {
          return Container(
            height: 700,
            width: 500,
            decoration: BoxDecoration(
              color: context.colorTheme().surface,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: const Padding(
              padding: EdgeInsets.only(top: 8.0),
              child: FilePickerDialog(),
            ),
          );
        });
  }

  final List<Map<String, dynamic>> dataList = [
    {
      'icon': Icons.image_outlined,
      'text': 'Photos & Videos',
      'dialog': 'Select Image/Video',
      'type': FileType.custom,
      'path': "${Platform.environment['USERPROFILE']}${Platform.pathSeparator}Pictures",
      'allowedEx': ['jpg', 'jpeg', 'png', 'mp4'],
    },
    {
      'icon': Icons.file_present,
      'text': 'Documents',
      'dialog': 'Select File',
      'type': FileType.any,
      'path': "${Platform.environment['USERPROFILE']}${Platform.pathSeparator}Documents",
      'allowedEx': null,
    },
    // {'icon': Icons.file_present, 'text': 'Documents'},
  ];

  bool isFileExplorerOpen = false;
  // late bool isMeOwner;

  int i = 0;

  @override
  Widget build(BuildContext context) {
    final sipNum = prefs.getString(CacheKeys.sipNumber);
    final sipDomain = prefs.getString(CacheKeys.sipDomain);
    final sipAccount = '$sipNum@$sipDomain';

    return BlocListener<InfoCubit, InfoState>(
      listener: (context, infoState) {
        if (infoState is InfoLoaded) {
          WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
            if (i == 0 && _mamListCubit.state.mamList.isNotEmpty && _scrollController.hasClients) {
              _scrollController.jumpTo(_scrollController.position.minScrollExtent);
              i++;
            }
          });
        }
      },
      child: BlocBuilder<InfoCubit, InfoState>(
        builder: (context, infoState) {
          notificationService.setActiveChat(infoState.receiver);

          return BlocBuilder<MamListCubit, MamListState>(builder: (context, mamListState) {
            return Scaffold(
              backgroundColor: context.colorTheme().surface,
              appBar: ChatAppBar(
                infoState: infoState,
                mamListState: mamListState,
                sipAccount: sipAccount,
                onLeaveGroup: () => _showLeaveGroupDialog(context, infoState),
                onDestroyGroup: () => _showDestroyGroupDialog(context, infoState),
                onInviteUser: () => _showInviteUserDialog(context, infoState, sipNum!, sipDomain!),
              ),
              body: BlocBuilder<ChatUiCubit, ChatUiState>(
                builder: (context, state) {
                  return infoState.receiver.isNotEmpty
                      ? Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Expanded(
                              child: GestureDetector(
                                onTap: () => _focusNode.unfocus(),
                                child: Container(
                                  padding: const EdgeInsets.only(top: 10),
                                  color: context.colorTheme().surface,
                                  child: ChatMessagesList(
                                    scrollController: _scrollController,
                                    sipAccount: sipAccount,
                                    mamListState: mamListState,
                                    messagesCubit: _messagesCubit,
                                    loginCubit: _loginCubit,
                                    mamListCubit: _mamListCubit,
                                    chatUiCubit: _chatUiCubit,
                                    contactsCubit: _contactsCubit,
                                    chatCubit: _chatCubit,
                                    addBookmarkCubit: _addBookmarkCubit,
                                    bookmarkListCubit: _bookmarkListCubit,
                                    groupUiCubit: _groupUiCubit,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(height: 10),
                            Divider(height: 2, color: Colors.grey[800]),
                            ChatInputField(
                              textEditingController: _textEditingController,
                              focusNode: _focusNode,
                              isConnected: _isConnected,
                              infoState: infoState,
                              messagesCubit: _messagesCubit,
                              loginCubit: _loginCubit,
                              mamListCubit: _mamListCubit,
                              chatUiCubit: _chatUiCubit,
                              contactsCubit: _contactsCubit,
                              onAttachPressed: () => _openFilePicker(context),
                            ),
                          ],
                        )
                      : Container(
                          alignment: Alignment.center,
                          child: Text(
                            '${env!.appName} Chat',
                            style: context.textStyle().displaySmall!.copyWith(color: Colors.white54),
                          ),
                        );
                },
              ),
            );
          });
        },
      ),
    );
  }

  // Dialog helper methods
  void _showLeaveGroupDialog(BuildContext context, InfoState infoState) {
    ChatDialogs.showLeaveGroupDialog(
      context: context,
      infoState: infoState,
      chatCubit: _chatCubit,
      loginCubit: _loginCubit,
      deleteBookmarkCubit: _deleteBookmarkCubit,
      groupUiCubit: _groupUiCubit,
      bookmarkListCubit: _bookmarkListCubit,
      infoCubit: _infoCubit,
    );
  }

  void _showDestroyGroupDialog(BuildContext context, InfoState infoState) {
    ChatDialogs.showDestroyGroupDialog(
      context: context,
      infoState: infoState,
      chatCubit: _chatCubit,
      loginCubit: _loginCubit,
      deleteBookmarkCubit: _deleteBookmarkCubit,
      groupUiCubit: _groupUiCubit,
      bookmarkListCubit: _bookmarkListCubit,
      infoCubit: _infoCubit,
      mamListCubit: _mamListCubit,
      contactsCubit: _contactsCubit,
      destroyReasonController: _destroyreasonEditingController,
      onShowError: () => setState(() => _showError = true),
      onHideError: () => setState(() => _showError = false),
      showError: _showError,
    );
  }

  void _showInviteUserDialog(BuildContext context, InfoState infoState, String sipNumber, String sipDomain) {
    ChatDialogs.showInviteUserDialog(
      context: context,
      infoState: infoState,
      chatCubit: _chatCubit,
      loginCubit: _loginCubit,
      messagesCubit: _messagesCubit,
      mamListCubit: _mamListCubit,
      chatUiCubit: _chatUiCubit,
      contactsCubit: _contactsCubit,
      userEditingController: _userEditingController,
      inviteReasonController: _invitereasonEditingController,
      sipNumber: sipNumber,
      sipDomain: sipDomain,
    );
  }
}

class ChatAppBar extends StatelessWidget implements PreferredSizeWidget {
  final InfoState infoState;
  final MamListState mamListState;
  final String sipAccount;
  final VoidCallback onLeaveGroup;
  final VoidCallback onDestroyGroup;
  final VoidCallback onInviteUser;

  const ChatAppBar({
    super.key,
    required this.infoState,
    required this.mamListState,
    required this.sipAccount,
    required this.onLeaveGroup,
    required this.onDestroyGroup,
    required this.onInviteUser,
  });

  @override
  Widget build(BuildContext context) {
    if (infoState is! InfoLoaded) {
      return AppBar(
        backgroundColor: context.colorTheme().surface,
        title: Text(
          '',
          style: TextStyle(
            color: context.colorTheme().primary,
          ),
        ),
        centerTitle: true,
      );
    }

    final groupNameReplaced = infoState.receiver.mucIDFilter();
    final groupNameFilter = groupNameRegex.firstMatch(groupNameReplaced);
    final groupNameOnly = groupNameFilter?.group(0);
    final groupNameOnlyReplaced = groupNameReplaced.replaceAll(groupNameOnly ?? '', '');

    // find receiver name
    final contactList = mamListState.filteredContactModelList;
    final matchingContact = contactList.firstWhereOrNull(
        (contact) => contact.contactEntries.isNotEmpty && contact.contactEntries[0].uri == infoState.receiver);
    final receiverName = matchingContact?.displayName;
    final extNum = matchingContact?.contactEntries.firstOrNull?.uri.split('@').firstOrNull;

    List<Widget> appBarActions = [];
    if (infoState.isGroupInfo()) {
      appBarActions.addAll(_buildGroupActions(context));
    } else {
      if (extNum != null && extNum.isNotEmpty) {
        appBarActions.addAll(_buildDirectCallActions(context, extNum));
      }
    }

    return AppBar(
      backgroundColor: context.colorTheme().surface,
      title: Text(
        infoState.isGroupInfo()
            ? groupNameOnlyReplaced.toLowerCase().toString()
            : (receiverName ?? infoState.receiver.toLowerCase()),
        style: TextStyle(
          color: context.colorTheme().primary,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      centerTitle: true,
      actions: appBarActions,
    );
  }

  List<Widget> _buildGroupActions(BuildContext context) {
    return [
      const Tooltip(
        message: 'Members',
        child: MembersDropdown(),
      ),
      BlocBuilder<PresencesCubit, PresencesState>(
        builder: (context, presencesState) {
          final mem = presencesState.presenceList[infoState.receiver.toLowerCase()];
          bool isMeOwner = false;
          if (mem != null) {
            isMeOwner = mem.any((member) => member.affiliation == 'owner' && member.jid == sipAccount);
          }

          return PopupMenuButton(
            iconColor: context.colorTheme().primaryContainer,
            color: context.colorTheme().secondary,
            onSelected: (value) {
              if (value == 'Leave Group') {
                onLeaveGroup();
              } else if (value == 'Remove Group') {
                onDestroyGroup();
              } else if (value == 'Invite User') {
                onInviteUser();
              }
            },
            itemBuilder: (BuildContext context) {
              return [
                const PopupMenuItem(
                  value: 'Leave Group',
                  child: Row(
                    children: [
                      Icon(Icons.group_off_outlined),
                      SizedBox(width: 10),
                      Text('Leave Group'),
                    ],
                  ),
                ),
                if (isMeOwner)
                  const PopupMenuItem(
                    value: 'Remove Group',
                    child: Row(
                      children: [
                        Icon(Icons.group_remove_outlined),
                        SizedBox(width: 10),
                        Text('Remove Group'),
                      ],
                    ),
                  ),
                const PopupMenuItem(
                  value: 'Invite User',
                  child: Row(
                    children: [
                      Icon(Icons.person_add_alt_outlined),
                      SizedBox(width: 10),
                      Text('Invite User'),
                    ],
                  ),
                ),
              ];
            },
          );
        },
      ),
    ];
  }

  List<Widget> _buildDirectCallActions(BuildContext context, String extNum) {
    final homeCubit = context.read<HomeCubit>();
    return [
      IconButton(
        icon: Icon(Icons.call_outlined, color: context.colorTheme().primaryContainer),
        tooltip: 'Call',
        onPressed: () {
          homeCubit.initiateCallFromChat(extNum);
        },
      ),
    ];
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class ChatBubble extends StatelessWidget {
  final MamInfo message;
  final int index;
  final List<MamInfo> reverseMessage;
  final String sipAccount;
  final MessagesCubit messagesCubit;
  final LoginCubit loginCubit;
  final MamListCubit mamListCubit;
  final ChatUiCubit chatUiCubit;
  final ContactsCubit contactsCubit;
  final ChatCubit chatCubit;
  final AddBookmarkCubit addBookmarkCubit;
  final BookmarkListCubit bookmarkListCubit;
  final GroupUiCubit groupUiCubit;
  final InfoState infoState;

  const ChatBubble({
    super.key,
    required this.message,
    required this.index,
    required this.reverseMessage,
    required this.sipAccount,
    required this.messagesCubit,
    required this.loginCubit,
    required this.mamListCubit,
    required this.chatUiCubit,
    required this.contactsCubit,
    required this.chatCubit,
    required this.addBookmarkCubit,
    required this.bookmarkListCubit,
    required this.groupUiCubit,
    required this.infoState,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        final currentMessage = reverseMessage[index];
        final currentDateTime = DateTime.parse(currentMessage.time).toUtc().toLocal();
        final String currentDate = DateFormat('yyyy-MM-dd').format(currentDateTime);
        final String currentMonthDay = DateFormat('MM-dd').format(currentDateTime);
        final String currentDay = DateFormat('EEE').format(currentDateTime);

        String? previousDate;

        if (index + 1 < reverseMessage.length) {
          final previousMessage = reverseMessage[index + 1];
          final previousDateTime = DateTime.parse(previousMessage.time).toUtc().toLocal();
          previousDate = DateFormat('yyyy-MM-dd').format(previousDateTime);
        }

        bool shouldShowDate = false;

        if (previousDate == null || currentDate != previousDate) {
          shouldShowDate = true;
          previousDate = currentDate;
        }

        final int currentYear = DateTime.now().year;
        final chatDate = (currentDateTime.year) == currentYear ? currentMonthDay : currentDate;

        return Column(
          children: [
            if (shouldShowDate) _buildDateHeader(chatDate, currentDay, colorTheme, textTheme),
            _buildMessageBubble(context, colorTheme),
          ],
        );
      },
    );
  }

  Widget _buildDateHeader(String chatDate, String currentDay, dynamic colorTheme, TextTheme textTheme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            color: colorTheme.roundShapeInkWellColor,
          ),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              '$chatDate ($currentDay)',
              style: textTheme.bodySmall!.copyWith(
                fontWeight: FontWeight.bold,
                color: colorTheme.onPrimaryColor.withOpacity(0.8),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMessageBubble(BuildContext context, dynamic colorTheme) {
    final bool isMe = message.sender == '';
    final String grpSender = message.grpSender;
    final List<String> senderSplit = grpSender.split('@');

    final String parsedTime = DateTime.parse(message.time).toUtc().toString();
    final DateTime localTime = DateTime.parse(parsedTime).toLocal();
    final String formattedTime = DateFormat('hh:mm a').format(localTime);

    final String trimmedBody = message.body.trim();
    final Uri? fileUri = Uri.tryParse(message.body);
    final RegExp imageExtensions = RegExp(r'\.(jpg|jpeg|png|gif|bmp|webp)$', caseSensitive: true);
    final RegExp videoExtensions = RegExp(r'\.(mp4|mkv|mov)$', caseSensitive: true);
    final isVideo = videoExtensions.hasMatch(trimmedBody);
    final isImage = imageExtensions.hasMatch(trimmedBody);

    final fileName = (fileUri != null && fileUri.pathSegments.isNotEmpty) ? fileUri.pathSegments.last : '';
    final RegExp groupRegex = RegExp(r'(.+)\s(\S+@muc\.x001\.dotdashtech\.com)', caseSensitive: true);
    final groupMatch = groupRegex.firstMatch(message.body);
    final inviteText = groupMatch?.group(1);
    final inviteSplit = inviteText?.split(invitationRegex);
    final groupOnly = groupMatch?.group(2);
    final groupName = groupOnly?.mucIDFilter();

    return Padding(
      padding: EdgeInsets.only(
        top: 3.0,
        right: isMe ? 10.0 : 0.0,
        left: isMe ? 0.0 : 10.0,
      ),
      child: !message.body.contains(invitationDeclineRegex)
          ? _buildRegularMessage(context, isMe, senderSplit, formattedTime, trimmedBody, isImage, isVideo, fileName,
              fileUri, colorTheme, inviteText, inviteSplit, groupOnly, groupName, localTime)
          : _buildDeclineMessage(context, groupOnly, groupName, isMe, localTime, formattedTime),
    );
  }

  Widget _buildRegularMessage(
      BuildContext context,
      bool isMe,
      List<String> senderSplit,
      String formattedTime,
      String trimmedBody,
      bool isImage,
      bool isVideo,
      String? fileName,
      Uri? fileUri,
      dynamic colorTheme,
      String? inviteText,
      List<String>? inviteSplit,
      String? groupOnly,
      String? groupName,
      DateTime localTime) {
    return Column(
      crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        if (isMe && !message.to.contains('muc') || !message.sender.contains('muc'))
          Container()
        else
          Row(
            mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
            children: [
              Text(
                senderSplit[0],
                style: const TextStyle(color: Colors.white54),
              )
            ],
          ),
        ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: width8XLarge),
          child: IntrinsicWidth(
            child: Container(
              padding: !isImage
                  ? const EdgeInsets.only(left: 10, right: 3, bottom: 7, top: 7)
                  : const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
              decoration: BoxDecoration(
                color: isMe ? Colors.amber : Colors.black54,
                borderRadius: isMe
                    ? const BorderRadius.only(
                        topLeft: Radius.circular(7),
                        bottomLeft: Radius.circular(7),
                        topRight: Radius.circular(7),
                      )
                    : const BorderRadius.only(
                        topLeft: Radius.circular(7),
                        bottomRight: Radius.circular(7),
                        topRight: Radius.circular(7),
                      ),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Expanded(
                        child: _buildMessageContent(context, isMe, trimmedBody, isImage, isVideo, fileName, fileUri,
                            colorTheme, inviteText, inviteSplit, groupOnly, groupName, localTime, formattedTime),
                      ),
                      if (!message.body.contains(invitationRegex) && !message.body.contains(env!.minioUrl) && !isImage)
                        const Column(children: [SizedBox(width: 20)]),
                    ],
                  ),
                  if (!message.body.contains(invitationRegex) && !message.body.contains(env!.minioUrl) && !isImage)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        SizedBox(width: !isImage ? 8.0 : 0),
                        Text(
                          !isImage ? formattedTime : '',
                          style: TextStyle(
                            color: isMe ? Colors.black : Colors.white70,
                            fontSize: 10,
                          ),
                        ),
                        if (!message.body.contains('[~Invitation~]')) SizedBox(width: !isImage ? 4.0 : 0),
                      ],
                    ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDeclineMessage(
      BuildContext context, String? groupOnly, String? groupName, bool isMe, DateTime localTime, String formattedTime) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (!message.body.contains(env!.minioUrl) && message.body.contains(invitationDeclineRegex))
          BlocBuilder<BookmarkListCubit, BookmarkListState>(
            builder: (context, state) {
              return DeclineChatBubble(
                groupOnly: groupOnly ?? '',
                isMe: isMe,
                groupName: groupName ?? '',
                date: localTime,
                time: formattedTime,
                body: message.body,
              );
            },
          ),
      ],
    );
  }

  Widget _buildMessageContent(
      BuildContext context,
      bool isMe,
      String trimmedBody,
      bool isImage,
      bool isVideo,
      String? fileName,
      Uri? fileUri,
      dynamic colorTheme,
      String? inviteText,
      List<String>? inviteSplit,
      String? groupOnly,
      String? groupName,
      DateTime localTime,
      String formattedTime) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // Regular text message
        if (!message.body.contains(env!.minioUrl) &&
            !message.body.contains(invitationRegex) &&
            !message.body.contains(invitationDeclineRegex) &&
            !_isEmoji(message.body) &&
            !isImage)
          SelectableText(
            message.body,
            style: TextStyle(
              color: isMe ? Colors.black : Colors.white70,
            ),
            maxLines: null,
            contextMenuBuilder: (context, editableTextState) {
              // Platform-specific context menu styling for Windows
              if (Platform.isWindows) {
                return _buildWindowsContextMenu(context, editableTextState);
              } else {
                // Default behavior for Android, iOS, and macOS
                return AdaptiveTextSelectionToolbar.buttonItems(
                  anchors: editableTextState.contextMenuAnchors,
                  buttonItems: <ContextMenuButtonItem>[
                    ContextMenuButtonItem(
                      onPressed: () {
                        editableTextState.copySelection(SelectionChangedCause.toolbar);
                      },
                      label: 'Copy',
                    ),
                  ],
                );
              }
            },
          ),

        // Emoji message
        if (_isEmoji(message.body) && !isImage)
          for (final char in message.body.characters)
            Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(3)),
                color: colorTheme.primaryVariantColor,
              ),
              child: Padding(
                padding: const EdgeInsets.only(bottom: 1.0),
                child: Text(
                  char,
                  style: TextStyle(
                    color: isMe ? Colors.black : Colors.white70,
                    fontSize: 16,
                  ),
                  maxLines: null,
                ),
              ),
            ),

        // Image message
        if (isImage) _buildImageMessage(context, trimmedBody, formattedTime, colorTheme),

        // Video message
        if (isVideo && !isImage) VideoChatBubble(videoUrl: trimmedBody),

        // Document message
        if (!isImage && message.body.contains(env!.minioUrl) && !isVideo)
          DocumentChatBubble(
            fileName: fileName!,
            isMe: isMe,
            formattedTime: formattedTime,
            fileUrl: fileUri.toString(),
          ),

        // Invitation message
        if (!isImage && message.body.contains(invitationRegex))
          _buildInvitationMessage(context, groupOnly, inviteSplit, groupName, localTime, formattedTime),
      ],
    );
  }

  Widget _buildImageMessage(BuildContext context, String trimmedBody, String formattedTime, dynamic colorTheme) {
    return Stack(
      children: [
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              imagePreviewDialog(
                context: context,
                imageUrl: trimmedBody,
              );
            },
            child: CachedNetworkImage(
              imageUrl: trimmedBody,
              placeholder: (context, url) => SizedBox(
                width: width5XLarge,
                height: heightMedium,
                child: Center(
                  child: LoadingAnimationWidget.prograssiveDots(
                    color: colorTheme.onSecondaryColor,
                    size: prograssiveDotSize,
                  ),
                ),
              ),
              errorWidget: (context, url, error) {
                return Container(
                  width: MediaQuery.sizeOf(context).width * 0.25,
                  height: 50,
                  color: Colors.grey,
                  child: const Center(
                    child: Icon(Icons.error),
                  ),
                );
              },
            ),
          ),
        ),
        Positioned(
          right: 0,
          bottom: 0,
          child: Container(
            padding: const EdgeInsets.only(left: 5, top: 2, bottom: 2, right: 2),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.35),
            ),
            child: Text(
              formattedTime,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 10,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInvitationMessage(BuildContext context, String? groupOnly, List<String>? inviteSplit, String? groupName,
      DateTime localTime, String formattedTime) {
    return BlocBuilder<BookmarkListCubit, BookmarkListState>(
      builder: (context, state) {
        return InvitationChatBubble(
          onDeclinePressed: () async {
            await messagesCubit.sendMessage(
              receiverJid: infoState.receiver,
              loginCubit: loginCubit,
              mamListCubit: mamListCubit,
              chatUiCubit: chatUiCubit,
              contactsCubit: contactsCubit,
              text: '${message.msgId} [~InvitationDeclined~] Invitation to $groupOnly has been declined ',
            );
          },
          onAcceptPressed: () async {
            await chatCubit.joinChatRoom(
              groupOnly.toString(),
              sipAccount,
              loginCubit,
              addBookmarkCubit,
              bookmarkListCubit,
              groupUiCubit,
            );
            groupUiCubit.update();
          },
          groupOnly: groupOnly ?? '',
          isMe: message.sender == '',
          inviteSplit: inviteSplit!,
          groupName: groupName ?? '',
          date: localTime,
          time: formattedTime,
          body: message.body,
          id: message.msgId,
        );
      },
    );
  }

  /// Custom context menu builder for Windows platform to fix text color issue
  Widget _buildWindowsContextMenu(BuildContext context, EditableTextState editableTextState) {
    final TextSelectionToolbarAnchors anchors = editableTextState.contextMenuAnchors;

    return Stack(
      children: [
        Positioned(
          left: anchors.primaryAnchor.dx,
          top: anchors.primaryAnchor.dy - 50, // Position above the selection
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(8),
            color: const Color.fromARGB(255, 34, 34, 34), // Match app's dark background
            child: Container(
              constraints: const BoxConstraints(minWidth: 80),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: InkWell(
                onTap: () {
                  editableTextState.copySelection(SelectionChangedCause.toolbar);
                },
                borderRadius: BorderRadius.circular(8),
                child: const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Text(
                    'Copy',
                    style: TextStyle(
                      color: Colors.white, // White text for visibility on dark background
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class ChatDialogs {
  static void showInviteUserDialog({
    required BuildContext context,
    required InfoState infoState,
    required ChatCubit chatCubit,
    required LoginCubit loginCubit,
    required MessagesCubit messagesCubit,
    required MamListCubit mamListCubit,
    required ChatUiCubit chatUiCubit,
    required ContactsCubit contactsCubit,
    required TextEditingController userEditingController,
    required TextEditingController inviteReasonController,
    required String sipNumber,
    required String sipDomain,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: context.colorTheme().surface,
        title: const Center(
          child: Text(
            'Invite User',
            style: TextStyle(
              color: Colors.white,
              fontSize: 17,
            ),
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Invite your friends',
              style: TextStyle(
                color: Colors.white70,
              ),
            ),
            BlocBuilder<InfoCubit, InfoState>(
              builder: (context, infoState) {
                final groupNameReplaced = infoState.name.mucIDFilter();
                final groupNameFilter = groupNameRegex.firstMatch(groupNameReplaced);
                final groupNameOnly = groupNameFilter?.group(0);
                final groupNameOnlyReplaced = groupNameReplaced.replaceAll(groupNameOnly ?? '', '');

                return Text(
                  "Group (${groupNameOnlyReplaced.split('/')[0]})",
                  style: const TextStyle(
                    color: Colors.white70,
                  ),
                );
              },
            ),
            const SizedBox(height: 10),
            TextField(
              style: const TextStyle(color: Colors.white),
              cursorColor: context.colorTheme().primary,
              controller: userEditingController,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: InputDecoration(
                hintText: sipNumber,
                hintStyle: context.textStyle().bodyLarge!.copyWith(
                      color: Colors.white54,
                      fontWeight: FontWeight.w500,
                    ),
                enabledBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white70),
                ),
                focusedBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white70),
                ),
              ),
            ),
            const SizedBox(height: 5),
            TextField(
              style: const TextStyle(color: Colors.white),
              cursorColor: context.colorTheme().primary,
              controller: inviteReasonController,
              decoration: InputDecoration(
                hintText: 'Reason to invite',
                hintStyle: context.textStyle().bodyLarge!.copyWith(
                      color: Colors.white54,
                      fontWeight: FontWeight.w500,
                    ),
                enabledBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white70),
                ),
                focusedBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white70),
                ),
              ),
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const TextButton(
                  onPressed: pop,
                  child: Text(
                    'Cancel',
                    style: TextStyle(color: Colors.red),
                  ),
                ),
                BlocBuilder<InfoCubit, InfoState>(
                  builder: (context, infoState) {
                    return TextButton(
                      onPressed: () {
                        chatCubit.inviteUser(
                          loginCubit: loginCubit,
                          messagesCubit: messagesCubit,
                          mamListCubit: mamListCubit,
                          chatUiCubit: chatUiCubit,
                          contactsCubit: contactsCubit,
                          receiver: infoState.receiver.toString(),
                          userEditingController:
                              "${userEditingController.text.toString().trim().replaceAll(' ', '')}@$sipDomain",
                          inviteReasonEditingController: inviteReasonController.text.toString(),
                        );

                        pop();
                      },
                      child: const Text(
                        'Invite',
                        style: TextStyle(color: Colors.green),
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  static void showLeaveGroupDialog({
    required BuildContext context,
    required InfoState infoState,
    required ChatCubit chatCubit,
    required LoginCubit loginCubit,
    required DeleteBookmarkCubit deleteBookmarkCubit,
    required GroupUiCubit groupUiCubit,
    required BookmarkListCubit bookmarkListCubit,
    required InfoCubit infoCubit,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: context.colorTheme().surface,
        title: const Center(
          child: Text(
            'Leave Group',
            style: TextStyle(
              color: Colors.white,
              fontSize: 17,
            ),
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Are you sure you want to Leave the group?',
              style: TextStyle(
                color: Colors.white70,
              ),
            ),
            BlocBuilder<InfoCubit, InfoState>(
              builder: (context, infoState) {
                final groupNameReplaced = infoState.name.mucIDFilter();
                final groupNameFilter = groupNameRegex.firstMatch(groupNameReplaced);
                final groupNameOnly = groupNameFilter?.group(0);
                final groupNameOnlyReplaced = groupNameReplaced.replaceAll(groupNameOnly ?? '', '');

                return Text(
                  "Group (${groupNameOnlyReplaced.split('/')[0]})",
                  style: const TextStyle(
                    color: Colors.white70,
                  ),
                );
              },
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const TextButton(
                  onPressed: pop,
                  child: Text(
                    'Cancel',
                    style: TextStyle(
                      color: Colors.red,
                    ),
                  ),
                ),
                BlocBuilder<InfoCubit, InfoState>(
                  builder: (context, infoState) {
                    return TextButton(
                      onPressed: () {
                        chatCubit.leaveRoom(
                          loginCubit,
                          deleteBookmarkCubit,
                          groupUiCubit,
                          bookmarkListCubit,
                          infoState.receiver.toString(),
                          infoState.nick.toString(),
                        );

                        infoCubit.removeInfo();
                        pop();
                      },
                      child: const Text(
                        'Leave',
                        style: TextStyle(
                          color: Colors.green,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  static void showDestroyGroupDialog({
    required BuildContext context,
    required InfoState infoState,
    required ChatCubit chatCubit,
    required LoginCubit loginCubit,
    required DeleteBookmarkCubit deleteBookmarkCubit,
    required GroupUiCubit groupUiCubit,
    required BookmarkListCubit bookmarkListCubit,
    required InfoCubit infoCubit,
    required MamListCubit mamListCubit,
    required ContactsCubit contactsCubit,
    required TextEditingController destroyReasonController,
    required VoidCallback onShowError,
    required VoidCallback onHideError,
    required bool showError,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: context.colorTheme().surface,
        title: const Center(
          child: Text(
            'Remove Group',
            style: TextStyle(
              color: Colors.white,
              fontSize: 17,
            ),
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Are you sure you want to Remove the group?',
              style: TextStyle(
                color: Colors.white70,
              ),
            ),
            BlocBuilder<InfoCubit, InfoState>(
              builder: (context, infoState) {
                final groupNameReplaced = infoState.name.mucIDFilter();
                final groupNameFilter = groupNameRegex.firstMatch(groupNameReplaced);
                final groupNameOnly = groupNameFilter?.group(0);
                final groupNameOnlyReplaced = groupNameReplaced.replaceAll(groupNameOnly ?? '', '');

                return Text(
                  "Group (${groupNameOnlyReplaced.split('/')[0]})",
                  style: const TextStyle(
                    color: Colors.white70,
                  ),
                );
              },
            ),
            const SizedBox(height: 10),
            TextField(
              style: const TextStyle(color: Colors.white),
              cursorColor: context.colorTheme().primary,
              controller: destroyReasonController,
              decoration: InputDecoration(
                hintText: 'Reason to remove the group',
                hintStyle: context.textStyle().bodyLarge!.copyWith(
                      color: Colors.white54,
                      fontWeight: FontWeight.w500,
                    ),
                enabledBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white70),
                ),
                focusedBorder: const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white70),
                ),
              ),
            ),
            if (showError)
              const Text(
                'Reason cannot be empty',
                style: TextStyle(color: Colors.red),
              )
            else
              const Text(''),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const TextButton(
                  onPressed: pop,
                  child: Text(
                    'Cancel',
                    style: TextStyle(color: Colors.red),
                  ),
                ),
                BlocBuilder<InfoCubit, InfoState>(
                  builder: (context, infoState) {
                    return TextButton(
                      onPressed: () async {
                        if (destroyReasonController.text.isEmpty) {
                          onShowError();
                        } else {
                          onHideError();

                          await chatCubit.removeGroup(
                            loginCubit,
                            deleteBookmarkCubit,
                            groupUiCubit,
                            bookmarkListCubit,
                            infoState.receiver,
                            infoState.nick.toString(),
                            destroyReasonController.text,
                          );

                          infoCubit.removeInfo();
                          pop();
                          mamListCubit.getBookmarkList(contactsCubit: contactsCubit);
                        }
                      },
                      child: const Text(
                        'Remove',
                        style: TextStyle(color: Colors.green),
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class ChatInputField extends StatelessWidget {
  final TextEditingController textEditingController;
  final FocusNode focusNode;
  final bool isConnected;
  final InfoState infoState;
  final MessagesCubit messagesCubit;
  final LoginCubit loginCubit;
  final MamListCubit mamListCubit;
  final ChatUiCubit chatUiCubit;
  final ContactsCubit contactsCubit;
  final VoidCallback onAttachPressed;

  const ChatInputField({
    super.key,
    required this.textEditingController,
    required this.focusNode,
    required this.isConnected,
    required this.infoState,
    required this.messagesCubit,
    required this.loginCubit,
    required this.mamListCubit,
    required this.chatUiCubit,
    required this.contactsCubit,
    required this.onAttachPressed,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MessagesCubit, MessagesState>(
      builder: (context, state) {
        return SizedBox(
          height: 55,
          child: Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.attach_file_outlined),
                        color: Colors.grey[600],
                        onPressed: onAttachPressed,
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: isConnected ? _buildConnectedTextField(context) : _buildDisconnectedTextField(context),
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(right: 10, bottom: 5),
                child: IconButton(
                  onPressed: () => _sendMessage(),
                  icon: Icon(
                    Icons.send,
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildConnectedTextField(BuildContext context) {
    return TextField(
      style: const TextStyle(color: Colors.white),
      cursorColor: Colors.amber[900],
      cursorWidth: 2.0,
      focusNode: focusNode,
      controller: textEditingController,
      enabled: isConnected,
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.all(2),
        hintText: 'Type something to send...',
        hintStyle: context.textStyle().bodyLarge!.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
        filled: true,
        fillColor: context.colorTheme().surface,
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: context.colorTheme().surface,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: context.colorTheme().surface,
          ),
        ),
      ),
      onSubmitted: (String text) => _sendMessage(),
    );
  }

  Widget _buildDisconnectedTextField(BuildContext context) {
    return TextField(
      style: const TextStyle(color: Colors.white),
      textAlign: TextAlign.center,
      enabled: isConnected,
      decoration: InputDecoration(
        hintText: 'Failed to connect...',
        hintStyle: context.textStyle().bodyLarge!.copyWith(
              color: Colors.red,
              fontWeight: FontWeight.w500,
            ),
        disabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: context.colorTheme().surface,
          ),
        ),
      ),
    );
  }

  void _sendMessage() {
    if (infoState is! InfoLoaded) return;

    focusNode.requestFocus();

    if (textEditingController.text.trim().isNotEmpty) {
      messagesCubit.sendMessage(
        receiverJid: infoState.receiver.toString(),
        loginCubit: loginCubit,
        mamListCubit: mamListCubit,
        chatUiCubit: chatUiCubit,
        contactsCubit: contactsCubit,
        text: textEditingController.text,
      );
      textEditingController.clear();
    }
  }
}

class ChatMessagesList extends StatelessWidget {
  final ScrollController scrollController;
  final String sipAccount;
  final MamListState mamListState;
  final MessagesCubit messagesCubit;
  final LoginCubit loginCubit;
  final MamListCubit mamListCubit;
  final ChatUiCubit chatUiCubit;
  final ContactsCubit contactsCubit;
  final ChatCubit chatCubit;
  final AddBookmarkCubit addBookmarkCubit;
  final BookmarkListCubit bookmarkListCubit;
  final GroupUiCubit groupUiCubit;

  const ChatMessagesList({
    super.key,
    required this.scrollController,
    required this.sipAccount,
    required this.mamListState,
    required this.messagesCubit,
    required this.loginCubit,
    required this.mamListCubit,
    required this.chatUiCubit,
    required this.contactsCubit,
    required this.chatCubit,
    required this.addBookmarkCubit,
    required this.bookmarkListCubit,
    required this.groupUiCubit,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<InfoCubit, InfoState>(
      builder: (context, infoState) {
        if (infoState is InfoInitial) {
          return const SizedBox(
            width: double.infinity,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ChatShimmer(),
                  ChatShimmer(isLeft: false),
                  ChatShimmer(),
                  ChatShimmer(isLeft: false),
                  ChatShimmer(isLeft: false),
                  ChatShimmer(),
                ],
              ),
            ),
          );
        } else if (infoState is InfoLoaded) {
          context.read<ChatUiCubit>().done();

          final conversationMap = mamListState.mamList;
          final conversationGroupMap = mamListState.mamgroupList;

          return _buildChat(
            infoState.isGroupInfo() ? conversationGroupMap : conversationMap,
            infoState,
            context,
          );
        } else if (infoState is InfoLoading) {
          return const SizedBox(
            width: double.infinity,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ChatShimmer(),
                  ChatShimmer(isLeft: false),
                  ChatShimmer(),
                  ChatShimmer(isLeft: false),
                  ChatShimmer(isLeft: false),
                  ChatShimmer(),
                ],
              ),
            ),
          );
        } else {
          return const Center(
            child: Text('Error fetching data'),
          );
        }
      },
    );
  }

  Widget _buildChat(Map<String, List<MamInfo>> mamList, InfoState infoState, BuildContext context) {
    return BlocBuilder<MamListCubit, MamListState>(
      builder: (context, state) {
        List<MamInfo> selectedMessages = mamList[infoState.receiver] ?? [];
        List<MamInfo> reverseMessage = selectedMessages.reversed.toList();

        return reverseMessage.isNotEmpty
            ? ListView.builder(
                controller: scrollController,
                reverse: true,
                itemCount: reverseMessage.length,
                itemBuilder: (context, index) {
                  final MamInfo mamInfos = reverseMessage[index];
                  final messages = mamInfos;

                  if (messages.body.isNotEmpty) {
                    return ChatBubble(
                      // key: ValueKey(messages.msgId),
                      message: messages,
                      index: index,
                      reverseMessage: reverseMessage,
                      sipAccount: sipAccount,
                      messagesCubit: messagesCubit,
                      loginCubit: loginCubit,
                      mamListCubit: mamListCubit,
                      chatUiCubit: chatUiCubit,
                      contactsCubit: contactsCubit,
                      chatCubit: chatCubit,
                      addBookmarkCubit: addBookmarkCubit,
                      bookmarkListCubit: bookmarkListCubit,
                      groupUiCubit: groupUiCubit,
                      infoState: infoState,
                    );
                  } else {
                    return Container();
                  }
                },
              )
            : Container();
      },
    );
  }
}

class ChatShimmer extends StatelessWidget {
  final bool isLeft;
  const ChatShimmer({
    this.isLeft = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: Align(
            alignment: isLeft ? Alignment.centerLeft : Alignment.centerRight,
            child: Container(
              // height: 45,
              height: 85,
              width: width5XLarge,
              decoration: BoxDecoration(
                color: isLeft ? colorTheme.roundShapeInkWellColor : colorTheme.primaryColor.withAlpha(100),
                borderRadius: BorderRadius.circular(5),
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Shimmer.fromColors(
                  baseColor: Colors.grey,
                  highlightColor: Colors.grey.shade400,
                  enabled: true,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    // mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Container(
                              width: double.infinity,
                              color: Colors.grey,
                              height: 10.0,
                            ),
                          ),
                          const Spacer(),
                        ],
                      ),
                      const SizedBox(height: 7),
                      Container(
                        width: double.infinity,
                        color: Colors.grey,
                        height: 10.0,
                      ),
                      const SizedBox(height: 2),
                      Row(
                        children: [
                          Expanded(
                            flex: 2,
                            child: Container(
                              width: double.infinity,
                              color: Colors.grey,
                              height: 10.0,
                            ),
                          ),
                          const Spacer(),
                        ],
                      ),
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          const Spacer(),
                          Container(
                            width: 40,
                            color: Colors.grey,
                            height: 10.0,
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

enum ContentLineType {
  twoLines,
  threeLines,
}

class ContentPlaceholder extends StatelessWidget {
  final ContentLineType lineType;

  const ContentPlaceholder({
    super.key,
    required this.lineType,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(width: 12.0),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  height: 10.0,
                  color: Colors.white,
                  margin: const EdgeInsets.only(bottom: 8.0),
                ),
                if (lineType == ContentLineType.threeLines)
                  Container(
                    width: double.infinity,
                    height: 10.0,
                    color: Colors.white,
                    margin: const EdgeInsets.only(bottom: 8.0),
                  ),
                Container(
                  width: 100.0,
                  height: 10.0,
                  color: Colors.white,
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}

bool _isEmoji(String input) {
  // Unicode ranges for emoji characters
  for (final char in input.characters) {
    final codeUnit = char.runes.first;
    bool isEmoji = emojiRanges.any((range) {
      return codeUnit >= range[0] && codeUnit <= range[1];
    });
    if (!isEmoji) {
      return false;
    }
  }
  return true;
}

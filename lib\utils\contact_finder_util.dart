import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/models/contact_model.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/shared_preferences_service.dart';

/// Utility class for finding contacts and extracting contact information at call hsiory
/// Used for call history add on call and  messaging, and other contact-related operations since call hsitory return different value for did data for incoming, outgoing call
class ContactFinderUtil {
  /// Result class containing all contact information
  static ContactInfo findContactInfo({
    required String did,
    required String contactName,
    required List<ContactModel> contactList,
  }) {
    ContactModel? foundContact;

    // Tier 1: Match by extension/number (for outgoing calls and regular contacts)
    for (final contact in contactList) {
      for (final entry in contact.contactEntries) {
        final uriNumber = entry.uri.replaceFirst('sip:', '').split('@').firstOrNull;
        final didNumber = did.replaceFirst('sip:', '').split('@').firstOrNull;
        if (uriNumber != null && uriNumber == didNumber) {
          foundContact = contact;
          break;
        }
      }
      if (foundContact != null) break;
    }

    // Tier 2: Match by contact name (for incoming calls where did = contact name)
    if (foundContact == null) {
      for (final contact in contactList) {
        if (contact.displayName.toLowerCase() == did.toLowerCase()) {
          foundContact = contact;
          break;
        }
      }
    }

    // Tier 3: Match by contactId (for local contacts where uri = '-')
    if (foundContact == null) {
      for (final contact in contactList) {
        if (contact.contactId == did) {
          foundContact = contact;
          break;
        }
      }
    }

    // Extract contact information
    if (foundContact != null) {
      final isLocalContact = foundContact.isLocalContact ?? false;
      final displayName = foundContact.displayName;
      final sipAddress = _buildSipAddress(foundContact, isLocalContact);
      final callExtension = _extractCallExtension(foundContact, did);

      return ContactInfo(
        contact: foundContact,
        displayName: displayName,
        isLocalContact: isLocalContact,
        sipAddress: sipAddress,
        callExtension: callExtension,
        isFound: true,
      );
    }

    // Contact not found - return default info
    return ContactInfo(
      contact: null,
      displayName: contactName,
      isLocalContact: false,
      sipAddress: _buildFallbackSipAddress(did),
      callExtension: _extractFallbackCallExtension(did),
      isFound: false,
    );
  }

  /// Build sipAddress for messaging (extension@domain format)
  static String? _buildSipAddress(ContactModel contact, bool isLocalContact) {
    if (isLocalContact) {
      return null; // Local contacts don't support messaging
    }

    final prefs = sl.get<SharedPreferencesService>();
    final sipDomain = prefs.getString(CacheKeys.sipDomain) ?? '';

    String? actualExtension;

    // First try contactId if it's numeric
    if (contact.contactId.isNotEmpty && RegExp(r'^\d+$').hasMatch(contact.contactId)) {
      actualExtension = contact.contactId;
    } else {
      // Try to find numeric extension from URI entries
      for (final entry in contact.contactEntries) {
        if (entry.uri.contains('@')) {
          final uriNumber = entry.uri.split('@').first;
          if (RegExp(r'^\d+$').hasMatch(uriNumber)) {
            actualExtension = uriNumber;
            break;
          }
        } else if (entry.uri != '-' && RegExp(r'^\d+$').hasMatch(entry.uri)) {
          actualExtension = entry.uri;
          break;
        }
      }
    }

    if (actualExtension != null) {
      return '$actualExtension@$sipDomain';
    }

    return null;
  }

  /// Extract extension number for calling
  static String _extractCallExtension(ContactModel contact, String fallbackDid) {
    // First try contactId if it's numeric
    if (contact.contactId.isNotEmpty && RegExp(r'^\d+$').hasMatch(contact.contactId)) {
      return contact.contactId;
    }

    // Try to find numeric extension from URI entries
    for (final entry in contact.contactEntries) {
      if (entry.uri.contains('@')) {
        final uriNumber = entry.uri.split('@').first;
        if (RegExp(r'^\d+$').hasMatch(uriNumber)) {
          return uriNumber;
        }
      } else if (entry.uri != '-' && RegExp(r'^\+?\d+$').hasMatch(entry.uri)) {
        return entry.uri;
      }
    }

    // Fallback to cleaned DID
    return _extractFallbackCallExtension(fallbackDid);
  }

  /// Build fallback sipAddress for unknown contacts
  static String? _buildFallbackSipAddress(String did) {
    final cleanDid = _extractFallbackCallExtension(did);
    if (RegExp(r'^\+?\d+$').hasMatch(cleanDid)) {
      final prefs = sl.get<SharedPreferencesService>();
      final sipDomain = prefs.getString(CacheKeys.sipDomain) ?? '';
      return '$cleanDid@$sipDomain';
    }
    return null;
  }

  /// Extract extension from DID for fallback cases
  static String _extractFallbackCallExtension(String did) {
    String cleanDid = did;

    // Clean up the number format
    if (cleanDid.contains('sip:')) {
      cleanDid = cleanDid.substring(4).split('@')[0];
    }
    if (cleanDid.contains('@')) {
      cleanDid = cleanDid.split('@')[0];
    }

    return cleanDid;
  }

  /// Find contact by name and resolve to extension (for call history calling)
  static String resolveCallExtension({
    required String originalExtension,
    required List<ContactModel> contactList,
  }) {
    String callNum = originalExtension;

    // Clean up the number format
    if (callNum.contains('sip:')) {
      callNum = callNum.substring(4).split('@')[0];
    }
    if (callNum.contains('@')) {
      callNum = callNum.split('@')[0];
    }

    // If callNum is not numeric (e.g., contact name), try to find the actual extension
    if (!RegExp(r'^\+?\d+$').hasMatch(callNum)) {
      // Try to find the contact by name and get the extension
      for (final contact in contactList) {
        if (contact.displayName.toLowerCase() == callNum.toLowerCase()) {
          // Found contact by name - use contactId or extract from URI
          if (contact.contactId.isNotEmpty && RegExp(r'^\d+$').hasMatch(contact.contactId)) {
            return contact.contactId;
          } else {
            // Try to find numeric extension from URI entries
            for (final entry in contact.contactEntries) {
              if (entry.uri.contains('@')) {
                final uriNumber = entry.uri.split('@').first;
                if (RegExp(r'^\d+$').hasMatch(uriNumber)) {
                  return uriNumber;
                }
              } else if (entry.uri != '-' && RegExp(r'^\+?\d+$').hasMatch(entry.uri)) {
                return entry.uri;
              }
            }
            break;
          }
        }
      }
    }

    return callNum;
  }
}

/// Data class containing all contact information
class ContactInfo {
  final ContactModel? contact;
  final String displayName;
  final bool isLocalContact;
  final String? sipAddress;
  final String callExtension;
  final bool isFound;

  const ContactInfo({
    required this.contact,
    required this.displayName,
    required this.isLocalContact,
    required this.sipAddress,
    required this.callExtension,
    required this.isFound,
  });

  /// Check if contact supports messaging
  /// Only found contacts that are not local can message
  bool get canMessage => isFound && sipAddress != null && !isLocalContact;

  /// Check if contact can be called
  bool get canCall => callExtension.isNotEmpty;

  /// Get contact type description
  String get contactType {
    if (!isFound) return 'Unknown';
    if (isLocalContact) return 'Local';
    if (contact?.isPhoneBookContact == true) return 'Phone Book';
    return 'API';
  }
}

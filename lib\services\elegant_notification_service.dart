import 'package:ddone/utils/string_util.dart';
import 'package:elegant_notification/elegant_notification.dart';
import 'package:elegant_notification/resources/arrays.dart';
import 'package:elegant_notification/resources/stacked_options.dart';
import 'package:flutter/material.dart';
class ElegantNotificationService {
  static const int kTitleCharLength = 40;
  static const int kDescriptionCharLength = 80;
  void showStackNotification(
    BuildContext context, {
    required String title,
    required String desc,
    GestureTapCallback? gestureTapCallback,
  }) {
    // Process the description to show icons for attachments
    String processedDesc = _getAttachmentDescription('Text', desc);
    ElegantNotification.info(
      width: 360,
      stackedOptions: StackedOptions(
        key: 'topRight',
        type: StackedType.same,
        itemOffset: const Offset(0, 5),
      ),
      position: Alignment.topRight,
      animation: AnimationType.fromRight,
      title: Text(
        StringUtil.clipString(title, kTitleCharLength),
        style: const TextStyle(color: Colors.black),
      ),
      description: Text(
        StringUtil.clipString(processedDesc, kDescriptionCharLength),
        style: const TextStyle(color: Colors.black),
      ),
      onDismiss: () {},
      onNotificationPressed: gestureTapCallback,
      animationDuration: const Duration(milliseconds: 500),
      toastDuration: const Duration(seconds: 4),
    ).show(context);
  }
  /// Process attachment descriptions to show icons instead of URLs
  String _getAttachmentDescription(String messageType, String message) {
    if (messageType == 'Text') {
      // Check if the text message contains a URL that points to a file
      final String trimmedBody = message.trim();
      final Uri? fileUri = Uri.tryParse(trimmedBody);
      // Only process if it's a valid URL
      if (fileUri != null && fileUri.hasScheme && fileUri.pathSegments.isNotEmpty) {
        final RegExp imageExtensions = RegExp(r'\.(jpg|jpeg|png|gif|bmp|webp|svg|ico)$', caseSensitive: false);
        final RegExp videoExtensions = RegExp(r'\.(mp4|mkv|mov|avi|wmv|flv|webm|m4v)$', caseSensitive: false);
        final isVideo = videoExtensions.hasMatch(trimmedBody);
        final isImage = imageExtensions.hasMatch(trimmedBody);
        if (isImage) {
          return 'Image';
        } else if (isVideo) {
          return 'Video';
        } else {
          return 'Document';
        }
      }
      // If it's not a file URL, return the original message
      return message;
    }
    // For non-text message types, check the message content
    final String trimmedBody = message.trim();
    final Uri? fileUri = Uri.tryParse(trimmedBody);
    final RegExp imageExtensions = RegExp(r'\.(jpg|jpeg|png|gif|bmp|webp|svg|ico)$', caseSensitive: false);
    final RegExp videoExtensions = RegExp(r'\.(mp4|mkv|mov|avi|wmv|flv|webm|m4v)$', caseSensitive: false);
    final isVideo = videoExtensions.hasMatch(trimmedBody);
    final isImage = imageExtensions.hasMatch(trimmedBody);
    if (isImage) {
      return '🖼️ Image';
    } else if (isVideo) {
      return '🎥 Video';
    } else if (fileUri != null && fileUri.hasScheme && fileUri.pathSegments.isNotEmpty) {
      return '📄 Document';
    }
    return messageType;
  }
}
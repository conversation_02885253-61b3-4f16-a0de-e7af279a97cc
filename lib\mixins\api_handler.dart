import 'dart:async';

import 'package:ddone/service_locator.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

const duration = Duration(seconds: 4);

mixin ApiHandler {
  final Logger log = sl.get<Logger>();

  Future<void> apiErrorHandler(
    Function() body, {
    FutureOr<void> Function(Object)? onError,
    String? customMessage,
  }) async {
    try {
      await body();
    } on DioException catch (e) {
      if (onError != null) {
        await onError(e);
      }

      // If custom message is provided, use it for all error types
      if (customMessage != null) {
        log.w('Api Handler ${e.type}', error: e);
        EasyLoadingService().showErrorWithText(
          customMessage,
          duration: duration,
        );
        return;
      }

      /// Unit test context here meaning a non-widget test, but plain unit test
      // final isUnitTestContext = WidgetKeys.navKey.currentState == null;

      // final buildContext = isUnitTestContext ? null : WidgetKeys.navKey.currentState!.overlay!.context;

      // String debugText = '';
      // if (e.response != null) {
      //   if (e.response!.data == '' || e.response!.statusCode == 404 || e.response!.statusCode == 500) {
      //     debugText = e.message!;
      //   } else if (e.response!.data["details"] != null) {
      //     debugText = e.response!.data["details"];
      //   } else {
      //     debugText = e.response!.data["detail"];
      //   }
      // } else {
      //   debugText = e.message!;
      // }

      final statusCode = e.response?.statusCode; // Use null-safe access
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
          log.w('Api Handler connectionTimeout', error: e);
          EasyLoadingService().showErrorWithText(
            'Taking too long to connect. Please ensure you have a stable internet connection.',
            duration: duration,
          );
          break;
        case DioExceptionType.connectionError:
          log.w('Api Handler connectionError', error: e);
          EasyLoadingService().showErrorWithText(
            'Network error. There seems to be a problem with your internet connection.',
            duration: duration,
          );
          break;
        case DioExceptionType.sendTimeout:
          log.w('Api Handler sendTimeout', error: e);
          EasyLoadingService().showErrorWithText(
            "Sending data timed out. We couldn't send your information in time. Please try again.",
            duration: duration,
          );
          break;
        case DioExceptionType.receiveTimeout:
          log.w('Api Handler receiveTimeout', error: e);
          EasyLoadingService().showErrorWithText(
            "Receiving data timed out. We didn't get a response back from our servers in time. Please try again.",
            duration: duration,
          );
          break;
        case DioExceptionType.badResponse:
          log.w('Api Handler badResponse', error: e);
          if (statusCode == 401) {
            EasyLoadingService().showErrorWithText(
              "It looks like you're not logged in, or your session has expired. Please log in again.",
              duration: duration,
            );
          } else if (statusCode == 404) {
            EasyLoadingService().showErrorWithText(
              "The item you're looking for couldn't be found.",
              duration: duration,
            );
          } else {
            EasyLoadingService().showErrorWithText(
              'Something went wrong. We received an unexpected response from our services. Please try again in a moment.',
              duration: duration,
            );
          }
          break;
        case DioExceptionType.badCertificate:
          log.w('Api Handler badCertificate', error: e);
          EasyLoadingService().showErrorWithText(
            "Connection not secure. There's a problem with the server's security certificate. Please contact support if this continues.",
            duration: duration,
          );
          break;
        case DioExceptionType.cancel:
          log.w('Api Handler cancel', error: e);
          EasyLoadingService().showErrorWithText(
            'Action cancelled. Your request was stopped.',
            duration: duration,
          );
          break;
        case DioExceptionType.unknown:
          log.w('Api Handler unknown', error: e);
          EasyLoadingService().showErrorWithText(
            'Unexpected error. Please try restarting the app or contacting support if this continues.',
            duration: duration,
          );
          break;
        default:
          log.w('Api Handler default', error: e);
          EasyLoadingService().showErrorWithText(
            'We encountered a problem. Please try restarting the app or contacting support if this continues.',
            duration: duration,
          );

          rethrow;
      }
    } catch (e) {
      if (onError != null) {
        await onError(e);
      }

      rethrow;
    }
  }
}

import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ntp/ntp.dart';

class NTPTime extends StatelessWidget {
  final bool isMe;
  const NTPTime({
    super.key,
    required this.isMe,
  });

  Future<DateTime> internetTime() async {
    final time = await NTP.now();
    debugPrint("NTP DATETIME : $time");
    return time;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        // final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        return FutureBuilder(
          future: internetTime(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const CircularProgressIndicator();
            } else if (snapshot.hasError) {
              return Text('Error: ${snapshot.error}');
            } else if (snapshot.hasData) {
              final time = snapshot.data;
              return Text(
                '$time',
                style: textTheme.labelLarge?.copyWith(
                  color: isMe ? Colors.black87 : Colors.white70,
                  fontWeight: FontWeight.w700,
                ),
              );
            } else {
              return const Text('No data');
            }
          },
        );
      },
    );
  }
}

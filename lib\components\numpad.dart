import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/models/enums/dialpad_enum.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class Numpad extends StatefulWidget {
  final Function(String)? callBack;

  const Numpad({
    this.callBack,
    super.key,
  });

  @override
  State<Numpad> createState() => _NumpadState();
}

class _NumpadState extends State<Numpad> {
  late HomeCubit _homeCubit;

  @override
  void initState() {
    super.initState();

    _homeCubit = BlocProvider.of<HomeCubit>(context);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: labels.map((e) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: e.map((row) {
            return BlocBuilder<ThemeCubit, ThemeState>(
              builder: (context, themeState) {
                final textTheme = themeState.themeData.textTheme;

                final numberTextStyle = context.responsiveSize<TextStyle>(
                  moileSize: textTheme.titleLarge!,
                  tabletSize: textTheme.titleLarge!,
                  desktopSize: textTheme.titleMedium!,
                  largeScreenSize: textTheme.titleLarge!,
                );

                final secondRowTextStyle = context.responsiveSize<TextStyle>(
                  moileSize: textTheme.labelSmall!,
                  tabletSize: textTheme.labelSmall!,
                  desktopSize: textTheme.labelSmall!,
                  largeScreenSize: textTheme.labelMedium!,
                );

                return RoundShapeInkWell(
                  onTap: () {
                    _homeCubit.playDialpadSound(row['name']);

                    widget.callBack?.call(row.keys.first);
                  },
                  contentWidget: Center(
                    child: RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        text: row.keys.first,
                        style: numberTextStyle,
                        children: <TextSpan>[
                          TextSpan(
                            text: '\n${row.values.first} ',
                            style: secondRowTextStyle,
                          )
                        ],
                      ),
                    ),
                  ),
                  // contentWidget: Column(
                  //   mainAxisSize: MainAxisSize.min,
                  //   mainAxisAlignment: MainAxisAlignment.center,
                  //   crossAxisAlignment: CrossAxisAlignment.center,
                  //   children: [
                  //     Align(
                  //       alignment: Alignment.center,
                  //       child: Text(
                  //         row.keys.first,
                  //         style: numberTextStyle,
                  //         textAlign: TextAlign.center,
                  //       ),
                  //     ),
                  //     Text(
                  //       row.values.first,
                  //       style: secondRowTextStyle,
                  //       textAlign: TextAlign.center,
                  //     ),
                  //   ],
                  // ),
                );
              },
            );
          }).toList(),
        );
      }).toList(),
    );
  }
}

const List<List<Map<String, dynamic>>> labels = [
  [
    {
      '1': '',
      'name': DialpadEnum.one,
    },
    {
      '2': 'abc',
      'name': DialpadEnum.two,
    },
    {
      '3': 'def',
      'name': DialpadEnum.three,
    },
  ],
  [
    {
      '4': 'ghi',
      'name': DialpadEnum.four,
    },
    {
      '5': 'jkl',
      'name': DialpadEnum.five,
    },
    {
      '6': 'mno',
      'name': DialpadEnum.six,
    }
  ],
  [
    {
      '7': 'pqrs',
      'name': DialpadEnum.seven,
    },
    {
      '8': 'tuv',
      'name': DialpadEnum.eight,
    },
    {
      '9': 'wxyz',
      'name': DialpadEnum.nine,
    }
  ],
  [
    {
      '*': '',
      'name': DialpadEnum.star,
    },
    {
      '0': '+',
      'name': DialpadEnum.zero,
    },
    {
      '#': '',
      'name': DialpadEnum.pound,
    }
  ],
];

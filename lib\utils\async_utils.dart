import 'dart:math';

import 'package:ddone/utils/logger_util.dart';

/// Asynchronously waits for a given condition to become true within a specified timeout.
///
/// The `condition` function is periodically evaluated. If it returns `true`,
/// this function completes with `true`. If the `timeout` is reached before
/// the `condition` becomes `true`, this function completes with `false`.
///
/// The `condition` function should return a `Future<bool>`.
///
/// Example usage:
///
/// ```dart
/// bool _isDataLoaded = false;
///
/// Future<void> fetchData() async {
///   // ... your data fetching logic ...
///   await Future.delayed(const Duration(seconds: 1));
///   _isDataLoaded = true;
/// }
///
/// Future<void> waitForData() async {
///   await fetchData();
///   bool loaded = await waitForCondition(() async => _isDataLoaded,
///       timeout: const Duration(seconds: 5), debugMsg: "Waiting for data to load");
///   if (loaded) {
///     print("Data loaded successfully!");
///   } else {
///     print("Timeout waiting for data.");
///   }
/// }
/// ```
///
/// Parameters:
///   - `condition`: A function that returns a `Future<bool>`. This function
///     will be called repeatedly until it returns `true` or the timeout is reached.
///   - `timeout`: The maximum duration to wait for the condition to become `true`.
///     Defaults to 500 milliseconds.
///   - `debugMsg`: An optional string that will be logged using `dart:developer.log`
///     at each check interval if the condition is not yet met. This can be useful
///     for debugging long-waiting operations.
///
/// Returns:
///   A `Future<bool>` that resolves to `true` if the `condition` becomes `true`
///   within the `timeout`, and `false` otherwise.
Future<bool> waitForCondition(Future<bool> Function() condition,
    {Duration timeout = const Duration(milliseconds: 500), String? debugMsg}) async {
  final deadline = DateTime.now().add(timeout);
  while (DateTime.now().isBefore(deadline)) {
    if (await condition()) {
      return true; // Condition met
    }
    await Future.delayed(const Duration(milliseconds: 100)); // Check every 100ms
    if (debugMsg != null) log.d(debugMsg);
  }
  return false;
}

/// Randomly waits for a duration between [minMilliseconds] and [maxMilliseconds] (inclusive).
///
/// Throws an [ArgumentError] if [minMilliseconds] is greater than [maxMilliseconds]
/// or if either is negative.
Future<void> randomWait(int minMilliseconds, int maxMilliseconds) async {
  if (minMilliseconds < 0 || maxMilliseconds < 0) {
    throw ArgumentError('minMilliseconds and maxMilliseconds cannot be negative.');
  }
  if (minMilliseconds > maxMilliseconds) {
    throw ArgumentError('minMilliseconds cannot be greater than maxMilliseconds.');
  }

  final random = Random();
  final int duration = minMilliseconds + random.nextInt(maxMilliseconds - minMilliseconds + 1);

  await Future.delayed(Duration(milliseconds: duration));
}

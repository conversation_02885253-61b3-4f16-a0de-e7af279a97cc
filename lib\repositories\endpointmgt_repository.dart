import 'package:ddone/models/endpointmgt_models.dart';
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'endpointmgt_repository.g.dart';

@RestApi(baseUrl: 'https://endpointmgt.dev.dotdash8.com/flows/trigger/') // will be overwritten by env ENDPOINTMGT_URL
abstract class EndpointmgtRepository {
  factory EndpointmgtRepository(Dio dio, {String baseUrl}) = _EndpointmgtRepository;

  @POST('{path}')
  Future<CheckUpdateResponse> checkAppUpdate(
    @Path('path') String path,
    @Body() CheckUpdateRequest request,
  );

  @POST('{path}')
  Future<String> updateUser(
    @Path('path') String path,
    @Body() UpdateUserRequest request,
  );

  @POST('{path}')
  Future<List<CallHistoryItem>> storeCallHistory(
    @Path('path') String path,
    @Body() CallHistoryRequest request,
  );

  @POST('{path}')
  Future<List<CallHistoryItem>> retrieveCallHistory(
    @Path('path') String path,
    @Body() CallHistoryRequest request,
  );
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RoundShapeAvatar extends StatelessWidget {
  final String? profileImgUrl;
  final double? radiusSize;
  final IconData? icon;

  const RoundShapeAvatar({
    this.profileImgUrl,
    this.radiusSize,
    this.icon,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;

        final iconSize = context.responsiveSize(
          moileSize: iconSizeLarge,
          tabletSize: iconSizeLarge,
          desktopSize: iconSizeExtraLarge,
          largeScreenSize: iconSizeXXLarge,
        );

        final avatarRadius = context.responsiveSize<double>(
          moileSize: radiusExtraLarge,
          tabletSize: radiusExtraLarge,
          desktopSize: radiusXLarge,
          largeScreenSize: radiusXXLarge,
        );

        return Padding(
          padding: const EdgeInsets.fromLTRB(0, 20, 0, 10),
          child: Container(
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  Color.fromARGB(255, 43, 43, 43),
                  Color.fromARGB(255, 66, 66, 66),
                ],
              ),
            ),
            child: CircleAvatar(
              backgroundColor: Colors.transparent,
              radius: radiusSize ?? avatarRadius,
              child: profileImgUrl != null
                  ? CachedNetworkImage(imageUrl: profileImgUrl!)
                  : Icon(
                      size: iconSize,
                      icon ?? Icons.person_outline,
                      color: colorTheme.primaryColor,
                    ),
            ),
          ),
        );
      },
    );
  }
}

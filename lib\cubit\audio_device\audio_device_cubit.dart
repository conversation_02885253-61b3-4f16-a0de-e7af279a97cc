import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:ddone/models/audio_device_model.dart';
import 'package:ddone/models/enums/audio_device_type_enum.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/audio_device_service.dart';
import 'package:ddone/services/voip_service.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:equatable/equatable.dart';

part 'audio_device_state.dart';

class AudioDeviceCubit extends Cubit<AudioDeviceState> {
  final AudioDeviceService _audioDeviceService;
  final VoipService _voipService;
  StreamSubscription<List<AudioDeviceModel>>? _deviceChangeSubscription;

  AudioDeviceCubit._({
    AudioDeviceState? state,
  })  : _audioDeviceService = sl.get<AudioDeviceService>(),
        _voipService = sl.get<VoipService>(),
        super(const AudioDeviceInitial()) {
    _initialize();
  }

  factory AudioDeviceCubit.initial({AudioDeviceState? state}) {
    return sl.isRegistered<AudioDeviceCubit>() ? sl.get<AudioDeviceCubit>() : AudioDeviceCubit._(state: state);
  }

  /// Initialize the cubit and start listening to device changes
  Future<void> _initialize() async {
    try {
      // Listen to device changes
      _deviceChangeSubscription = _audioDeviceService.deviceChangeStream.listen(
        _onDeviceListChanged,
        onError: (error) {
          log.e('AudioDeviceCubit: Device change stream error', error: error);
          emit(AudioDeviceError(
            errorMessage: 'Failed to monitor device changes: $error',
            availableDevices: state.availableDevices,
            selectedDevice: state.selectedDevice,
          ));
        },
      );

      // Load initial devices
      await loadAvailableDevices();
    } catch (e) {
      log.e('AudioDeviceCubit: Failed to initialize', error: e);
      emit(AudioDeviceError(
        errorMessage: 'Failed to initialize audio device management: $e',
      ));
    }
  }

  /// Load available audio devices
  Future<void> loadAvailableDevices() async {
    try {
      emit(AudioDeviceLoading(
        availableDevices: state.availableDevices,
        selectedDevice: state.selectedDevice,
      ));

      final devices = await _audioDeviceService.getAvailableDevices();

      // Determine default device if none is selected
      AudioDeviceModel? selectedDevice = state.selectedDevice;
      if (selectedDevice == null || !devices.any((d) => d.deviceId == selectedDevice!.deviceId)) {
        selectedDevice = _audioDeviceService.getDefaultDevice(devices);
      }

      // Update device selection states
      final updatedDevices = devices.map((device) {
        return device.copyWith(
          isSelected: device.deviceId == selectedDevice?.deviceId,
        );
      }).toList();

      emit(AudioDeviceLoaded(
        availableDevices: updatedDevices,
        selectedDevice: selectedDevice,
      ));

      log.t('AudioDeviceCubit: Loaded ${devices.length} devices, selected: ${selectedDevice?.label}');
    } catch (e) {
      log.e('AudioDeviceCubit: Failed to load devices', error: e);
      emit(AudioDeviceError(
        errorMessage: 'Failed to load audio devices: $e',
        availableDevices: state.availableDevices,
        selectedDevice: state.selectedDevice,
      ));
    }
  }

  /// Select an audio device
  Future<void> selectDevice(AudioDeviceModel device) async {
    try {
      log.t('AudioDeviceCubit: Selecting device ${device.label}');

      // Handle special "none" case for desktop
      if (device.deviceId == 'none' && isDesktop) {
        // Turn off speaker audio
        _voipService.muteSpeakerAudio(true);
      } else {
        // Select the actual device
        final success = await _audioDeviceService.selectDevice(device);
        if (!success) {
          emit(AudioDeviceError(
            errorMessage: 'Failed to select audio device: ${device.label}',
            availableDevices: state.availableDevices,
            selectedDevice: state.selectedDevice,
          ));
          return;
        }

        // If we were previously muted (desktop), unmute
        if (isDesktop) {
          _voipService.muteSpeakerAudio(false);
        }
      }

      // Update state
      final updatedDevices = state.availableDevices.map((d) {
        return d.copyWith(isSelected: d.deviceId == device.deviceId);
      }).toList();

      emit(AudioDeviceSelectionChanged(
        availableDevices: updatedDevices,
        selectedDevice: device.copyWith(isSelected: true),
      ));

      log.t('AudioDeviceCubit: Successfully selected device ${device.label}');
    } catch (e) {
      log.e('AudioDeviceCubit: Failed to select device ${device.label}', error: e);
      emit(AudioDeviceError(
        errorMessage: 'Failed to select audio device: $e',
        availableDevices: state.availableDevices,
        selectedDevice: state.selectedDevice,
      ));
    }
  }

  /// Toggle between earpiece and speaker
  /// - only use this when it has 2 audio devices.
  Future<void> toggleSpeakerPhone() async {
    try {
      if (isMobile) {
        // For mobile, toggle between earpiece and speaker
        final currentDevice = state.selectedDevice;
        final devices = state.availableDevices;

        AudioDeviceModel? targetDevice;

        targetDevice = devices.firstWhere(
          (d) => d.deviceId != currentDevice?.deviceId,
          orElse: () => devices.first,
        );

        await selectDevice(targetDevice);
      } else {
        // For desktop, toggle speaker mute
        final isCurrentlyMuted = state.selectedDevice?.deviceId == 'none';
        if (isCurrentlyMuted) {
          // Unmute - select the first available device
          final firstDevice = state.availableDevices.firstWhere(
            (d) => d.deviceId != 'none',
            orElse: () => state.availableDevices.first,
          );
          await selectDevice(firstDevice);
        } else {
          // Mute - select "none"
          final noneDevice = state.availableDevices.firstWhere(
            (d) => d.deviceId == 'none',
            orElse: () => const AudioDeviceModel(
              deviceId: 'none',
              label: 'None (Turn off speaker)',
              type: AudioDeviceType.none,
            ),
          );
          await selectDevice(noneDevice);
        }
      }
    } catch (e) {
      log.e('AudioDeviceCubit: Failed to toggle speaker phone', error: e);
      emit(AudioDeviceError(
        errorMessage: 'Failed to toggle speaker: $e',
        availableDevices: state.availableDevices,
        selectedDevice: state.selectedDevice,
      ));
    }
  }

  /// Toggle speaker mute/unmute for desktop users
  /// This is a simplified version for desktop that only toggles between muted and unmuted
  Future<void> toggleSpeakerMute() async {
    try {
      await loadAvailableDevices();

      if (state is AudioDeviceLoaded) {
        final currentState = state as AudioDeviceLoaded;
        final currentDevice = currentState.selectedDevice;

        if (currentDevice?.deviceId == 'none') {
          // Currently muted, unmute by selecting the first available device
          final availableDevices = currentState.availableDevices.where((d) => d.deviceId != 'none').toList();
          if (availableDevices.isNotEmpty) {
            final defaultDevice = _audioDeviceService.getDefaultDevice(availableDevices);
            if (defaultDevice != null) {
              await selectDevice(defaultDevice);
            }
          }
        } else {
          // Currently unmuted, mute by selecting "none"
          const noneDevice = AudioDeviceModel(
            deviceId: 'none',
            label: 'None (Turn off speaker)',
            type: AudioDeviceType.none,
          );
          await selectDevice(noneDevice);
        }
      }
    } catch (e) {
      log.e('AudioDeviceCubit: Failed to toggle speaker mute', error: e);
      emit(AudioDeviceError(
        errorMessage: 'Failed to toggle speaker mute: $e',
        availableDevices: state.availableDevices,
        selectedDevice: state.selectedDevice,
      ));
    }
  }

  /// Check if speaker is currently on
  bool get isPhoneSpeakerOn {
    if (isMobile) {
      return state.selectedDevice?.deviceId == _audioDeviceService.getLoudSpeakerDeviceId();
    } else {
      return state.selectedDevice?.deviceId != 'none';
    }
  }

  /// Handle device list changes
  void _onDeviceListChanged(List<AudioDeviceModel> newDevices) {
    log.t('AudioDeviceCubit: Device list changed, updating state');

    // Check if currently selected device is still available
    AudioDeviceModel? selectedDevice = state.selectedDevice;
    AudioDeviceModel? defaultDevice = _audioDeviceService.getDefaultDevice(newDevices);
    if ((selectedDevice != null && !newDevices.any((d) => d.deviceId == selectedDevice!.deviceId)) ||
        (selectedDevice != null &&
            defaultDevice != null &&
            selectedDevice.type.priority < defaultDevice.type.priority)) {
      // Selected device is no longer available or there is a higher priority default device, select default.
      selectedDevice = defaultDevice;
      log.t('AudioDeviceCubit: switching to ${selectedDevice?.label}');

      // Select the new device
      if (selectedDevice != null) selectDevice(selectedDevice);
    }

    // Update device selection states
    final updatedDevices = newDevices.map((device) {
      return device.copyWith(
        isSelected: device.deviceId == selectedDevice?.deviceId,
      );
    }).toList();

    emit(AudioDeviceLoaded(
      availableDevices: updatedDevices,
      selectedDevice: selectedDevice,
    ));
  }

  @override
  Future<void> close() {
    _deviceChangeSubscription?.cancel();
    _audioDeviceService.dispose();
    return super.close();
  }
}

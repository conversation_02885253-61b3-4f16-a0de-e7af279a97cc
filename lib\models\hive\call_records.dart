import 'package:ddone/constants/hive_id_constants.dart';
import 'package:ddone/models/enums/hive/call_type_enum.dart';
import 'package:hive/hive.dart';

part 'call_records.g.dart';

@HiveType(typeId: callRecordId)
class CallRecords extends HiveObject {
  @HiveField(0)
  final String contactName;

  @HiveField(1)
  final String did;

  @HiveField(2)
  final String duration;

  @HiveField(3)
  final CallType type;

  @HiveField(4)
  final DateTime datetime;

  CallRecords({
    required this.contactName,
    required this.did,
    required this.duration,
    required this.type,
    required this.datetime,
  });
}

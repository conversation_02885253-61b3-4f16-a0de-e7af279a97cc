// import 'package:ddone/screens/dialpad.dart';
// import 'package:flutter/material.dart';

// class MobileHomepage extends StatefulWidget {
//   static const routeName = '/mobileHomePage';

//   const MobileHomepage({super.key});

//   @override
//   State<MobileHomepage> createState() => _MobileHomePageState();
// }

// class _MobileHomePageState extends State<MobileHomepage> {
//   @override
//   void initState() {
//     super.initState();
//   }

//   @override
//   void dispose() {
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SafeArea(
//       child: Scaffold(
//         backgroundColor: const Color(0x00FFBC27),
//         body: const Dialpad(),
//         bottomNavigationBar: BottomAppBar(
//           padding: const EdgeInsets.symmetric(horizontal: 10),
//           height: 70,
//           notchMargin: 7,
//           shape: const CircularNotchedRectangle(),
//           child: Row(
//             mainAxisSize: MainAxisSize.max,
//             mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//             children: [
//               IconButton(
//                 icon: const Icon(
//                   Icons.phone,
//                   color: Colors.black,
//                   size: 35,
//                 ),
//                 onPressed: () {},
//               ),
//               IconButton(
//                 icon: const Icon(
//                   Icons.chat,
//                   color: Colors.black,
//                   size: 35,
//                 ),
//                 onPressed: () {
//                   print('IconButton pressed ...');
//                 },
//               ),
//               const SizedBox.shrink(),
//               const SizedBox.shrink(),
//               IconButton(
//                 icon: const Icon(
//                   Icons.groups,
//                   color: Colors.black,
//                   size: 35,
//                 ),
//                 onPressed: () {
//                   print('IconButton pressed ...');
//                 },
//               ),
//               IconButton(
//                 icon: const Icon(
//                   Icons.settings,
//                   color: Colors.black,
//                   size: 35,
//                 ),
//                 onPressed: () {
//                   print('IconButton pressed ...');
//                 },
//               ),
//             ],
//           ),
//         ),
//         floatingActionButton: MediaQuery.of(context).viewInsets.bottom == 0
//             ? SizedBox(
//                 width: 80,
//                 height: 80,
//                 child: FloatingActionButton(
//                   onPressed: () {},
//                   shape: const CircleBorder(),
//                   child: const Icon(
//                     Icons.dialpad_outlined,
//                     color: Colors.black,
//                     size: 40,
//                   ),
//                 ),
//               )
//             : null,
//         floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
//       ),
//     );
//   }
// }

import 'package:ddone/models/contact_entry.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'contact_model.g.dart';

@JsonSerializable(explicitToJson: true)
class ContactModel extends Equatable {
  @JsonKey(name: 'contactEntries')
  final List<ContactEntry> contactEntries;

  final String contactId;

  final String displayName;

  final bool? isLocalContact;

  final bool? isPhoneBookContact;

  const ContactModel({
    required this.contactEntries,
    required this.contactId,
    required this.displayName,
    this.isLocalContact = false,
    this.isPhoneBookContact = false,
  });

  factory ContactModel.fromJson(Map<String, dynamic> json) => _$ContactModelFromJson(json);

  Map<String, dynamic> toJson() => _$ContactModelToJson(this);

  @override
  List<Object?> get props => [
        contactEntries,
        contactId,
        displayName,
        isLocalContact,
        isPhoneBookContact,
      ];
}

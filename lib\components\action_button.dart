import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:flutter/material.dart';
import 'dart:io';

class ActionButton extends StatefulWidget {
  final String? title;
  final String subTitle;
  final IconData? icon;
  final bool checked;
  final bool number;
  final Color? fillColor;
  final Function()? onPressed;
  final Function()? onLongPress;

  const ActionButton({
    super.key,
    this.title,
    this.subTitle = '',
    this.icon,
    this.onPressed,
    this.onLongPress,
    this.checked = false,
    this.number = false,
    this.fillColor,
  });

  @override
  State<ActionButton> createState() => _ActionButtonState();
}

class _ActionButtonState extends State<ActionButton> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        GestureDetector(
            onLongPress: widget.onLongPress,
            onTap: widget.onPressed,
            child: RawMaterialButton(
              onPressed: widget.onPressed,
              splashColor: widget.fillColor ?? (widget.checked ? Colors.black : Colors.orange),
              fillColor: widget.fillColor ?? (widget.checked ? Colors.orange : Colors.black),
              elevation: 10.0,
              shape: const CircleBorder(),
              child: Padding(
                padding: EdgeInsets.all(context.deviceWidth(0.018)),
                child: widget.number
                    ? Column(mainAxisAlignment: MainAxisAlignment.center, children: <Widget>[
                        Text('${widget.title}',
                            style: TextStyle(
                              fontSize: Platform.isAndroid ? 22 : context.deviceWidth(0.018),
                              color: widget.fillColor ?? Colors.white,
                            )),
                        Text(widget.subTitle.toUpperCase(),
                            style: TextStyle(
                              fontSize: Platform.isAndroid ? 12 : context.deviceWidth(0.01),
                              color: widget.fillColor ?? Colors.white,
                            ))
                      ])
                    : Icon(
                        widget.icon,
                        size: Platform.isAndroid ? 32 : context.deviceWidth(0.03),
                        color: widget.fillColor != null ? Colors.black : (widget.checked ? Colors.white : Colors.white),
                      ),
              ),
            )),
        widget.number
            ? Container(margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 2.0))
            : Container(
                margin: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 2.0),
                child: (widget.number || widget.title == null)
                    ? null
                    : Text(
                        widget.title!,
                        style: TextStyle(
                          fontSize: 15.0,
                          color: widget.fillColor ?? Colors.grey[500],
                        ),
                      ),
              )
      ],
    );
  }
}

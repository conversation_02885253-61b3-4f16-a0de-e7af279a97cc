// An enum to represent the final status of our download operations.
enum DownloadResultStatus {
  successful,
  canceled,
  paused,
  failed,
  fileNotAvailable,
  fileAlreadyExists,
}

// A class to hold the result of a download operation.
class DownloadResult {
  final DownloadResultStatus status;
  final String? message; // Optional message for errors or info
  final String? filePath; // Path to the downloaded file if successful

  DownloadResult(this.status, {this.message, this.filePath});
}

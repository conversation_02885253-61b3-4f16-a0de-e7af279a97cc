import 'package:azlistview/azlistview.dart';
import 'package:ddone/components/common/common_text_field.dart';
import 'package:ddone/components/list_tile/card_list_tile_with_title.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/constants/regex_constants.dart';
import 'package:ddone/cubit/auth/auth_cubit.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/focus/focus_cubit.dart';
import 'package:ddone/cubit/selected_contact/selected_contact_cubit.dart';
import 'package:ddone/cubit/selected_group/selected_group_cubit.dart';
import 'package:ddone/models/contact_az_item.dart';
import 'package:ddone/models/contact_model.dart';
import 'package:ddone/screens/selected_contact.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/extensions/string_ext.dart';
import 'package:ddone/utils/pop_out_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:moxxmpp/model/conference_info.dart';

class ContactList extends StatefulWidget {
  static const routeName = '/contactList';

  const ContactList({super.key});

  @override
  State<ContactList> createState() => _ContactlistState();
}

class _ContactlistState extends State<ContactList> {
  late TextEditingController _searchNameController;

  late ContactsCubit _contactsCubit;
  late SelectedContactCubit _selectedContactCubit;
  late SelectedGroupCubit _selectedGroupCubit;
  late FocusCubit _focusCubit;

  final bool _isContact = true;

  @override
  void initState() {
    super.initState();

    _contactsCubit = BlocProvider.of<ContactsCubit>(context);
    _selectedContactCubit = BlocProvider.of<SelectedContactCubit>(context);
    _selectedGroupCubit = BlocProvider.of<SelectedGroupCubit>(context);
    _focusCubit = BlocProvider.of<FocusCubit>(context);

    _searchNameController = TextEditingController();

    // Request contacts permission and then fetch contacts
    _initializeContacts();
  }

  Future<void> _initializeContacts() async {
    // Request contacts permission first
    await _contactsCubit.requestContactsPermission(context);

    // Then fetch contacts (this will include phone book contacts if permission was granted)
    _contactsCubit.getContactList();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listener: (context, authState) {
        if (authState is AuthSuccess) {
          _initializeContacts();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: context.colorTheme().surface,
          title: Text(_isContact ? 'Contact List' : 'Group List'),
          leading: BlocBuilder<ThemeCubit, ThemeState>(
            builder: (context, themeState) {
              final colorTheme = themeState.colorTheme;

              return IconButton(
                onPressed: () {
                  /// TEMP: HIDE GROUP CHAT FUNCTIONALITY BEFORE IT IS READY!
                  // _searchNameController.clear();
                  // setState(() {
                  //   _isContact = !_isContact;
                  // });
                  // if (_isContact) {
                  //   _contactsCubit.filterContactList('');
                  // } else {
                  //   _contactsCubit.filterBookmarkList('');
                  // }
                },
                icon: Icon(
                  _isContact ? Icons.person : Icons.group,
                  color: colorTheme.primaryColor,
                ),
              );
            },
          ),
          actions: <Widget>[
            if (!isMobile)
              Padding(
                padding: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                child: IconButton(
                  icon: Icon(
                    Icons.refresh,
                    color: context.colorTheme().primary,
                    size: iconSizeLarge,
                  ),
                  splashRadius: iconSizeLarge,
                  onPressed: () {
                    _initializeContacts();

                    _searchNameController.clear();
                  },
                ),
              ),
            if (_isContact)
              Padding(
                padding: const EdgeInsets.fromLTRB(0, 0, 10, 0),
                child: IconButton(
                  icon: Icon(
                    Icons.person_add_alt,
                    color: context.colorTheme().primary,
                    size: iconSizeLarge,
                  ),
                  splashRadius: iconSizeLarge,
                  onPressed: () {
                    // Dialpad.textfieldFocused = true;

                    showAddContactDialog(context);
                  },
                ),
              ),
          ],
        ),
        body: Container(
          color: context.colorTheme().surface,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: maxWidthForDesktop),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(0, 10, 0, 20),
                  child: CommonTextField(
                    textEditingController: _searchNameController,
                    onValueChanged: (value) {
                      if (_isContact) {
                        _contactsCubit.filterContactList(value);
                      } else {
                        _contactsCubit.filterBookmarkList(value);
                      }
                    },
                    hintText: 'Search',
                    prefixIcon: const Icon(
                      Icons.search,
                      size: iconSizeMedium,
                    ),
                    onFocusChange: (value) {
                      if (value) {
                        _focusCubit.focusContactField();
                      } else {
                        _focusCubit.unfocusContactField();
                      }
                    },
                  ),
                ),
              ),
              _isContact
                  ? ContactListView(
                      selectedContactCubit: _selectedContactCubit,
                      selectedGroupCubit: _selectedGroupCubit,
                      onRefresh: () async {
                        await _contactsCubit.requestContactsPermission(context);
                        await _contactsCubit.getContactList();

                        _searchNameController.clear();
                      },
                    )
                  : BookmarkGroupListView(
                      selectedContactCubit: _selectedContactCubit,
                      selectedGroupCubit: _selectedGroupCubit,
                    ),
            ],
          ),
        ),
      ),
    );
  }
}

class ContactListView extends StatelessWidget {
  final SelectedContactCubit selectedContactCubit;
  final SelectedGroupCubit selectedGroupCubit;
  final Future<void> Function() onRefresh;

  const ContactListView({
    required this.selectedContactCubit,
    required this.selectedGroupCubit,
    required this.onRefresh,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: BlocBuilder<ContactsCubit, ContactsState>(
        builder: (context, contactState) {
          final filteredContactList = contactState.filteredContactModelList;
          final favouriteContactList = contactState.favouriteContactModelList;

          /// TODO: Add loading state check
          // if (contactState is ContactsLoading) {
          //   return const Center(
          //     child: CircularProgressIndicator(),
          //   );
          // }

          if (filteredContactList.isEmpty) {
            return Center(
              child: SizedBox(
                height: context.deviceHeight(0.75),
                child: const Center(
                  child: Text(
                    'No contacts . . .',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
            );
          }

          // Convert contacts to AzListView items
          final List<ContactAzItem> azItems = ContactAzItem.fromContactModelList(filteredContactList);

          // Ensure azItems is not empty after conversion
          if (azItems.isEmpty) {
            return const Center(
              child: Text(
                'No contacts available',
                style: TextStyle(color: Colors.white),
              ),
            );
          }

          return Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: maxWidthForDesktop),
              child: RefreshIndicator(
                onRefresh: onRefresh,
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // Ensure we have valid constraints
                    if (constraints.maxHeight <= 0) {
                      return const SizedBox.shrink();
                    }

                    return AzListView(
                      data: azItems,
                      itemCount: azItems.length,
                      itemBuilder: (context, index) {
                        final azItem = azItems[index];
                        final contact = azItem.contactModel;

                        return Column(
                          mainAxisSize: MainAxisSize.min, // Add this
                          children: [
                            // Show favourite contacts at the top
                            if (index == 0 && favouriteContactList.isNotEmpty)
                              Column(
                                mainAxisSize: MainAxisSize.min, // Add this
                                children: favouriteContactList.asMap().entries.map((favouriteContactModel) {
                                  return ContactCard(
                                    contactModel: favouriteContactModel.value,
                                    showTitle: favouriteContactModel.key == 0,
                                    title: 'Favourite',
                                    selectedContactCubit: selectedContactCubit,
                                    selectedGroupCubit: selectedGroupCubit,
                                  );
                                }).toList(),
                              ),
                            ContactCard(
                              contactModel: contact,
                              showTitle: azItem.isShowSuspension,
                              selectedContactCubit: selectedContactCubit,
                              selectedGroupCubit: selectedGroupCubit,
                            ),
                          ],
                        );
                      },
                      physics: const BouncingScrollPhysics(),
                      susItemBuilder: (context, index) {
                        if (index >= azItems.length) return const SizedBox.shrink();
                        final azItem = azItems[index];
                        return _buildSuspensionWidget(azItem.getSuspensionTag(), context);
                      },
                      indexBarData: ContactAzItem.getAlphabeticTags(azItems),
                      indexBarOptions: IndexBarOptions(
                        needRebuild: true,
                        ignoreDragCancel: true,
                        downTextStyle: TextStyle(
                          fontSize: 12,
                          color: context.colorTheme().primary,
                        ),
                        downItemDecoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: context.colorTheme().primary.withOpacity(0.8),
                        ),
                        indexHintWidth: 60, // Reduced from 120/2
                        indexHintHeight: 50, // Reduced from 100/2
                        indexHintDecoration: BoxDecoration(
                          color: context.colorTheme().primary.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(4.0),
                        ),
                        indexHintTextStyle: TextStyle(
                          fontSize: 24.0,
                          color: context.colorTheme().onPrimary,
                        ),
                        indexHintAlignment: Alignment.centerRight,
                        indexHintChildAlignment: const Alignment(-0.25, 0.0),
                        indexHintOffset: Offset(isDesktop ? -context.deviceWidth(0.5) : -20, 0),
                      ),
                    );
                  },
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// Build suspension header widget
  Widget _buildSuspensionWidget(String tag, BuildContext context) {
    return Container(
      height: 40,
      // width: double.infinity,
      padding: const EdgeInsets.only(left: 16.0),
      color: context.colorTheme().surface.withOpacity(0.8),
      alignment: Alignment.centerLeft,
      child: Text(
        tag,
        softWrap: false,
        style: TextStyle(
          fontSize: 14.0,
          color: context.colorTheme().primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

class BookmarkGroupListView extends StatelessWidget {
  final SelectedContactCubit selectedContactCubit;
  final SelectedGroupCubit selectedGroupCubit;

  const BookmarkGroupListView({
    required this.selectedContactCubit,
    required this.selectedGroupCubit,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: BlocBuilder<ContactsCubit, ContactsState>(
        builder: (context, contactState) {
          final bookmarkGroupList = contactState.filterBookmarkGroupList;

          if (bookmarkGroupList.isEmpty) {
            return SizedBox(
              height: context.deviceHeight(0.75),
              child: const Center(
                child: Text(
                  'Group is empty . . .',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            );
          }

          bookmarkGroupList.sort((a, b) => a.name.toLowerCase().compareTo(b.name.toLowerCase()));

          return ScrollbarTheme(
            data: ScrollbarThemeData(thumbColor: WidgetStatePropertyAll(Colors.grey.shade700)),
            child: Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: maxWidthForDesktop),
                child: ListView.builder(
                  itemCount: bookmarkGroupList.length,
                  itemBuilder: (context, index) {
                    final bookmarkGroup = bookmarkGroupList[index];

                    bool showTitle = true;

                    if (index != 0) {
                      showTitle = !bookmarkGroup.name
                          .toLowerCase()
                          .startsWith(bookmarkGroupList[index - 1].name[0].toLowerCase());
                    }

                    return BookmarkGroupCard(
                      conferenceInfo: bookmarkGroup,
                      showTitle: showTitle,
                      selectedContactCubit: selectedContactCubit,
                      selectedGroupCubit: selectedGroupCubit,
                    );
                  },
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

class ContactCard extends StatelessWidget {
  final ContactModel contactModel;
  final bool showTitle;
  final String? title;
  final SelectedContactCubit selectedContactCubit;
  final SelectedGroupCubit selectedGroupCubit;

  const ContactCard({
    this.title,
    required this.contactModel,
    required this.showTitle,
    required this.selectedContactCubit,
    required this.selectedGroupCubit,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final uri = contactModel.contactEntries[0].uri;
    final displayName = contactModel.displayName;
    final contactId = contactModel.contactId;

    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;

        return BlocBuilder<SelectedContactCubit, SelectedContactState>(
          builder: (context, selectedContactState) {
            bool isSelected = false;

            if (selectedContactState.isLocalContact) {
              if (selectedContactState.contactId == contactId) {
                isSelected = true;
              }
            } else if (uri == selectedContactState.sipAddress) {
              isSelected = true;
            }
            return CardListTileWithTitle(
              title: title ?? contactModel.displayName[0].toUpperCase(),
              listTileTitle: displayName,
              leadingIcon: Icon(
                Icons.person_outline,
                color: colorTheme.primaryColor,
              ),
              onTap: () {
                selectedGroupCubit.removeSelectedGroupInfo();

                selectedContactCubit.setContactInfo(
                  displayName: displayName,
                  contactId: contactId,
                  sipAddress: uri,
                  isLocalContact: contactModel.isLocalContact,
                  isPhoneBookContact: contactModel.isPhoneBookContact,
                );

                if (isMobile) {
                  pushNamed(SelectedContact.routeName);
                }
              },
              isTileSelected: isSelected,
              showTitle: showTitle,
            );
          },
        );
      },
    );
  }
}

class BookmarkGroupCard extends StatelessWidget {
  final ConferenceInfo conferenceInfo;
  final bool showTitle;
  final SelectedContactCubit selectedContactCubit;
  final SelectedGroupCubit selectedGroupCubit;

  const BookmarkGroupCard({
    required this.conferenceInfo,
    required this.showTitle,
    required this.selectedContactCubit,
    required this.selectedGroupCubit,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;

        return BlocBuilder<SelectedGroupCubit, SelectedGroupState>(
          builder: (context, selectedGroupState) {
            // final groupNameRegex = RegExp(r'\_[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$');
            final groupNameReplaced = conferenceInfo.name.mucIDFilter();
            final groupNameFilter = groupNameRegex.firstMatch(groupNameReplaced);
            final String? groupNameOnly = groupNameFilter?.group(0);
            final String groupNameOnlyReplaced = groupNameReplaced.replaceAll(groupNameOnly ?? '', '');
            return CardListTileWithTitle(
              title: conferenceInfo.name[0].toUpperCase(),
              listTileTitle: groupNameOnlyReplaced,
              leadingIcon: Icon(
                Icons.group,
                color: colorTheme.primaryColor,
              ),
              onTap: () {
                selectedGroupCubit.setGroupInfo(
                  groupName: conferenceInfo.name,
                  groupJid: conferenceInfo.jid,
                  autoJoin: conferenceInfo.autojoin,
                  nick: conferenceInfo.nick,
                );

                selectedContactCubit.removeSelectedContacInfo();

                if (isMobile) {
                  pushNamed(SelectedContact.routeName);
                }
              },
              isTileSelected: selectedGroupState.groupJid == conferenceInfo.jid,
              showTitle: showTitle,
            );
          },
        );
      },
    );
  }
}

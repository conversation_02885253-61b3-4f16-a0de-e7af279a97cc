import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'contact_entry.g.dart';

@JsonSerializable(explicitToJson: true)
class ContactEntry extends Equatable {
  final String entryId;

  final String label;

  final String type;

  final String uri;

  const ContactEntry({
    required this.entryId,
    required this.label,
    required this.type,
    required this.uri,
  });

  factory ContactEntry.fromJson(Map<String, dynamic> json) => _$ContactEntryFromJson(json);

  Map<String, dynamic> toJson() => _$ContactEntryToJson(this);

  @override
  List<Object?> get props => [
        entryId,
        label,
        type,
        uri,
      ];
}

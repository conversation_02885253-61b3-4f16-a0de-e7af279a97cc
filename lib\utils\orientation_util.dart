import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ddone/utils/screen_util.dart';

/// Utility class for managing device orientation based on device type
class OrientationUtil {
  /// Sets device orientations based on device type:
  /// - iPhone: Portrait only
  /// - Android mobile: Portrait only
  /// - iPad: All orientations (handled by iOS Info.plist)
  /// - Android tablet: All orientations
  static Future<void> setDeviceOrientations(BuildContext context) async {
    // For iOS devices, let the Info.plist handle orientation settings
    // iPhone will be portrait only, iPad will support all orientations
    if (isIOS) {
      // iOS orientation is handled by Info.plist
      // iPhone: UISupportedInterfaceOrientations = [UIInterfaceOrientationPortrait]
      // iPad: UISupportedInterfaceOrientations~ipad = [all orientations]
      return;
    }

    // For Android devices, check if it's a tablet
    if (isAndroid) {
      final isTablet = isAndroidTablet(context);
      if (isTablet) {
        // Android tablet: Allow all orientations
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ]);
      } else {
        // Android mobile: Portrait only
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
        ]);
      }
      return;
    }

    // For other platforms (desktop), allow all orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  /// Sets portrait-only orientation (fallback for initialization)
  static Future<void> setPortraitOnly() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
  }

  /// Sets all orientations
  static Future<void> setAllOrientations() async {
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  /// Gets the current device orientation description for debugging
  static String getDeviceOrientationInfo(BuildContext context) {
    final orientation = MediaQuery.of(context).orientation;
    final size = MediaQuery.of(context).size;

    String deviceType = 'Unknown';
    if (isIOS) {
      deviceType = 'iOS';
    } else if (isAndroid) {
      deviceType = isAndroidTablet(context) ? 'Android Tablet' : 'Android Mobile';
    } else if (isDesktop) {
      deviceType = 'Desktop';
    }

    return 'Device: $deviceType, Orientation: $orientation, Size: ${size.width}x${size.height}';
  }
}

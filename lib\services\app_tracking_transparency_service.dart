import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:flutter/material.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';

/// Service to handle App Tracking Transparency (ATT) requests
/// Required by Apple for iOS 14.5+ when apps collect data
/// that could be used for tracking users across apps and websites
/// Note: ATT is iOS-only, not required for macOS
class AppTrackingTransparencyService {
  /// Request App Tracking Transparency permission
  /// Should be called after the app has launched and user has had a chance
  /// to understand the app's functionality
  static Future<bool> requestTrackingPermission() async {
    // Only request on iOS (ATT is iOS-only)
    if (!isIOS) {
      log.t('ATT: Not iOS, skipping tracking permission request');
      return true;
    }

    try {
      // Check if we can request permission (iOS 14.5+, macOS 11+)
      final status = await AppTrackingTransparency.trackingAuthorizationStatus;
      log.t('ATT: Current tracking status: $status');

      // If already determined, don't request again
      if (status != TrackingStatus.notDetermined) {
        log.t('ATT: Permission already determined: $status');
        return status == TrackingStatus.authorized;
      }

      // Request permission
      log.t('ATT: Requesting tracking permission...');
      final result = await AppTrackingTransparency.requestTrackingAuthorization();
      log.t('ATT: Permission result: $result');

      return result == TrackingStatus.authorized;
    } catch (e, stackTrace) {
      log.e('ATT: Error requesting tracking permission', error: e, stackTrace: stackTrace);
      // Return true to not block app functionality if ATT fails
      return true;
    }
  }

  /// Check current tracking authorization status
  static Future<TrackingStatus> getTrackingStatus() async {
    if (!isIOS) {
      return TrackingStatus.authorized; // Not applicable on non-iOS platforms
    }

    try {
      return await AppTrackingTransparency.trackingAuthorizationStatus;
    } catch (e, stackTrace) {
      log.e('ATT: Error getting tracking status', error: e, stackTrace: stackTrace);
      return TrackingStatus.authorized; // Default to authorized to not block functionality
    }
  }

  /// Check if tracking is authorized
  static Future<bool> isTrackingAuthorized() async {
    final status = await getTrackingStatus();
    return status == TrackingStatus.authorized;
  }

  /// Get the advertising identifier (IDFA)
  /// Only available if tracking is authorized
  static Future<String?> getAdvertisingIdentifier() async {
    if (!isIOS) {
      return null;
    }

    try {
      final isAuthorized = await isTrackingAuthorized();
      if (!isAuthorized) {
        log.t('ATT: Tracking not authorized, cannot get advertising identifier');
        return null;
      }

      return await AppTrackingTransparency.getAdvertisingIdentifier();
    } catch (e, stackTrace) {
      log.e('ATT: Error getting advertising identifier', error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// Show a pre-permission dialog to explain why tracking is needed
  /// This helps improve permission grant rates by providing context
  static Future<bool> showPrePermissionDialog(BuildContext context) async {
    if (!isIOS) {
      return true;
    }

    // Check if permission is already determined
    final status = await getTrackingStatus();
    if (status != TrackingStatus.notDetermined) {
      return status == TrackingStatus.authorized;
    }

    if (!context.mounted) return false;

    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Help Us Improve DDOne'),
          content: const Text(
            'DDOne uses crash reporting to identify and fix issues, helping us provide you with a better experience.\n\n'
            'Your privacy is important to us. The data collected is used solely for app improvement and is not shared with third parties for advertising purposes.\n\n'
            'You can change this setting anytime in your device Settings.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Continue'),
            ),
          ],
        );
      },
    );

    if (result == true) {
      return await requestTrackingPermission();
    }

    return false;
  }

  /// Initialize tracking permission request
  /// Should be called after app launch when appropriate
  static Future<void> initializeTracking(BuildContext context) async {
    if (!isIOS) {
      return;
    }

    try {
      // Wait a bit after app launch to show the dialog
      // await Future.delayed(const Duration(seconds: 2));

      if (!context.mounted) return;

      await showPrePermissionDialog(context);
    } catch (e, stackTrace) {
      log.e('ATT: Error initializing tracking', error: e, stackTrace: stackTrace);
    }
  }
}

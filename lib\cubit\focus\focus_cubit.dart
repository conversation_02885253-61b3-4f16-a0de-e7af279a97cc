import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'focus_state.dart';

class FocusCubit extends Cubit<FocusState> {
  // FocusNode contactNode;
  FocusNode dialpadNode;
  // FocusNode transferContactNode;
  // FocusNode transferDialpadNode;

  FocusCubit()
      // : contactNode = FocusNode(),
      : dialpadNode = FocusNode(),
        // transferContactNode = FocusNode(),
        // transferDialpadNode = FocusNode(),
        super(const FocusState());

  @override
  Future<void> close() {
    dialpadNode.dispose();
    // transferDialpadNode.dispose();

    return super.close();
  }

  void focusContactField() {
    emit(state.copyWith(isContactFocused: true));
  }

  void unfocusContactField() {
    emit(state.copyWith(isContactFocused: false));
  }

  void focusDialpadField() {
    emit(state.copyWith(isDialpadFocused: true));
  }

  void unfocusDialpadField() {
    emit(state.copyWith(isDialpadFocused: false));
  }

  void focusTransferContactField() {
    emit(state.copyWith(isTransferContactFocused: true));
  }

  void unfocusTransferContactField() {
    emit(state.copyWith(isTransferContactFocused: false));
  }

  void focusTransferDialpadField() {
    emit(state.copyWith(isTransferDialpadFocused: true));
  }

  void unfocusTransferDialpadField() {
    emit(state.copyWith(isTransferDialpadFocused: false));
  }

  void focusNode(FocusNode node) {
    node.requestFocus();

    if (node == dialpadNode) {
      focusDialpadField();
    } 
    // else if (node == transferDialpadNode) {
    //   focusTransferDialpadField();
    // }
  }

  // void unfocusNode(FocusNode node) {
  //   node.unfocus();

  //   if (node == dialpadNode) {
  //     unfocusDialpadField();
  //   } else if (node == transferDialpadNode) {
  //     unfocusTransferDialpadField();
  //   }
  // }
}

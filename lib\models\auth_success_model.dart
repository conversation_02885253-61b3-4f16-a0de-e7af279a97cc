import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'auth_success_model.g.dart';

@JsonSerializable(explicitToJson: true)
class AuthSuccessModel extends Equatable {
  @Json<PERSON>ey(name: 'username')
  final String? userName;

  final String? password;

  final String? domain;

  @Json<PERSON>ey(name: 'displayname')
  final String? displayName;

  final String? transport;

  @JsonKey(name: 'url_pn') // obsolete, for old push API
  final String? urlPn;

  @<PERSON>son<PERSON><PERSON>(name: 'url_fs') // obsolete, for old chat system
  final String? urlFs;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'url_contact')
  final String? urlContact;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'url_sunaj')
  final String? urlSunaj;

  final String? proxy;

  @Json<PERSON>ey(name: 'proxy_transport')
  final String? proxyTransport;

  const AuthSuccessModel({
    required this.userName,
    required this.password,
    required this.domain,
    required this.displayName,
    required this.transport,
    required this.urlPn,
    required this.urlFs,
    required this.urlContact,
    required this.urlSunaj,
    required this.proxy,
    required this.proxyTransport,
  });

  factory AuthSuccessModel.fromJson(Map<String, dynamic> json) => _$AuthSuccessModelFromJson(json);

  Map<String, dynamic> toJson() => _$AuthSuccessModelToJson(this);

  String? get urlContactBase {
    if (urlContact == null || urlContact!.isEmpty) {
      return '';
    }
    final uri = Uri.parse(urlContact!);
    return uri.origin + uri.path;
  }

  String? get urlContactParams {
    if (urlContact == null || urlContact!.isEmpty) {
      return '';
    }
    final uri = Uri.parse(urlContact!);
    if (uri.query.isNotEmpty) {
      return uri.query;
    }
    return '';
  }

  @override
  List<Object?> get props => [
        userName ?? '',
        password ?? '',
        domain ?? '',
        displayName ?? '',
        transport ?? '',
        urlPn ?? '',
        urlFs ?? '',
        urlContact ?? '',
        urlSunaj ?? '',
        proxy ?? '',
        proxyTransport ?? '',
      ];
}

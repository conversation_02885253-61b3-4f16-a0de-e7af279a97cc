part of 'voip_cubit.dart';

abstract class VoipState extends Equatable {
  final String statusMessage;
  final String? caller; // person that initiated the call
  final String? callerId;
  final String? callee; // person that receive the call
  final String? calleeId;

  const VoipState({
    this.statusMessage = '',
    this.caller,
    this.callerId,
    this.callee,
    this.calleeId,
  });

  @override
  List<Object?> get props => [
        statusMessage,
        caller,
        callerId,
        callee,
        calleeId,
      ];

  Map<String, dynamic> toJson();

  factory VoipState.fromJson(Map<String, dynamic> json) {
    switch (json['type']) {
      case 'VoipInitial':
        return VoipInitial.fromJson(json);
      case 'VoipSipRegistering':
        return VoipSipRegistering.fromJson(json);
      case 'VoipSipRegistered':
        return VoipSipRegistered.fromJson(json);
      case 'VoipSipRegisteredError':
        return VoipSipRegisteredError.from<PERSON>son(json);
      case 'VoipSipUnregistered':
        return VoipSipUnregistered.from<PERSON>son(json);
      case 'VoipSipIncomingCall':
        return VoipSipIncomingCall.fromJson(json);
      case 'VoipSipAccepted':
        return VoipSipAccepted.fromJson(json);
      case 'VoipSipAcceptedLoading':
        return VoipSipAcceptedLoading.fromJson(json);
      case 'VoipSipAcceptedSuccess':
        return VoipSipAcceptedSuccess.fromJson(json);
      case 'VoipSipAcceptedError':
        return VoipSipAcceptedError.fromJson(json);
      case 'VoipSipHangup':
        return VoipSipHangup.fromJson(json);
      case 'VoipSipHangingUp':
        return VoipSipHangingUp.fromJson(json);
      case 'VoipSipHungUp':
        return VoipSipHungUp.fromJson(json);
      case 'VoipSipHangupError':
        return VoipSipHangupError.fromJson(json);
      case 'VoipSipProgress':
        return VoipSipProgress.fromJson(json);
      case 'VoipSipCalling':
        return VoipSipCalling.fromJson(json);
      case 'VoipSipTransferCall':
        return VoipSipTransferCall.fromJson(json);
      case 'VoipSipProceeding':
        return VoipSipProceeding.fromJson(json);
      case 'VoipSipRinging':
        return VoipSipRinging.fromJson(json);
      case 'VoipConnectionError':
        return VoipConnectionError.fromJson(json);
      default:
        throw UnimplementedError('Unknown VoipState type: ${json['type']}');
    }
  }
}

class VoipInitial extends VoipState {
  const VoipInitial();

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipInitial',
    };
  }

  factory VoipInitial.fromJson(Map<String, dynamic> json) {
    return const VoipInitial();
  }
}

class VoipSipRegistering extends VoipState {
  const VoipSipRegistering();

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipRegistering',
    };
  }

  factory VoipSipRegistering.fromJson(Map<String, dynamic> json) {
    return const VoipSipRegistering();
  }
}

class VoipSipRegistered extends VoipState {
  const VoipSipRegistered({super.statusMessage, super.caller, super.callerId, super.callee, super.calleeId});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipRegistered',
      'statusMessage': statusMessage,
      'caller': caller,
      'callerId': callerId,
      'callee': callee,
      'calleeId': calleeId,
    };
  }

  factory VoipSipRegistered.fromJson(Map<String, dynamic> json) {
    switch (json['type']) {
      case 'VoipSipRegistered':
        return VoipSipRegistered(
          statusMessage: json['statusMessage'],
          caller: json['caller'],
          callerId: json['callerId'],
          callee: json['callee'],
          calleeId: json['calleeId'],
        );
      case 'VoipSipIncomingCall':
        return VoipSipIncomingCall.fromJson(json);
      case 'VoipSipAccepted':
        return VoipSipAccepted.fromJson(json);
      case 'VoipSipAcceptedLoading':
        return VoipSipAcceptedLoading.fromJson(json);
      case 'VoipSipAcceptedSuccess':
        return VoipSipAcceptedSuccess.fromJson(json);
      case 'VoipSipAcceptedError':
        return VoipSipAcceptedError.fromJson(json);
      case 'VoipSipProgress':
        return VoipSipProgress.fromJson(json);
      case 'VoipSipCalling':
        return VoipSipCalling.fromJson(json);
      case 'VoipSipTransferCall':
        return VoipSipTransferCall.fromJson(json);
      case 'VoipSipProceeding':
        return VoipSipProceeding.fromJson(json);
      case 'VoipSipRinging':
        return VoipSipRinging.fromJson(json);
      default:
        throw UnimplementedError('Unknown VoipSipRegistered type: ${json['type']}');
    }
  }
}

class VoipSipRegisteredError extends VoipState {
  const VoipSipRegisteredError({super.statusMessage});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipRegisteredError',
      'statusMessage': statusMessage,
    };
  }

  factory VoipSipRegisteredError.fromJson(Map<String, dynamic> json) {
    return VoipSipRegisteredError(
      statusMessage: json['statusMessage'],
    );
  }
}

class VoipSipUnregistered extends VoipState {
  const VoipSipUnregistered();

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipUnregistered',
    };
  }

  factory VoipSipUnregistered.fromJson(Map<String, dynamic> json) {
    return const VoipSipUnregistered();
  }
}

class VoipSipIncomingCall extends VoipSipRegistered {
  const VoipSipIncomingCall({
    super.statusMessage,
    required super.callerId,
    required super.caller,
    super.callee,
    super.calleeId,
  });

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipIncomingCall',
      'statusMessage': statusMessage,
      'caller': caller,
      'callerId': callerId,
      'callee': callee,
      'calleeId': calleeId,
    };
  }

  factory VoipSipIncomingCall.fromJson(Map<String, dynamic> json) {
    return VoipSipIncomingCall(
      statusMessage: json['statusMessage'],
      caller: json['caller'],
      callerId: json['callerId'],
      callee: json['callee'],
      calleeId: json['calleeId'],
    );
  }
}

class VoipSipAccepted extends VoipSipRegistered {
  const VoipSipAccepted({super.statusMessage, super.caller, super.callerId, super.callee, super.calleeId});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipAccepted',
      'statusMessage': statusMessage,
      'caller': caller,
      'callerId': callerId,
      'callee': callee,
      'calleeId': calleeId,
    };
  }

  factory VoipSipAccepted.fromJson(Map<String, dynamic> json) {
    switch (json['type']) {
      case 'VoipSipAccepted':
        return VoipSipAccepted(
          statusMessage: json['statusMessage'],
          caller: json['caller'],
          callerId: json['callerId'],
          callee: json['callee'],
          calleeId: json['calleeId'],
        );
      case 'VoipSipAcceptedLoading':
        return VoipSipAcceptedLoading.fromJson(json);
      case 'VoipSipAcceptedSuccess':
        return VoipSipAcceptedSuccess.fromJson(json);
      default:
        throw UnimplementedError('Unknown VoipSipAccepted type: ${json['type']}');
    }
  }
}

class VoipSipAcceptedLoading extends VoipSipAccepted {
  const VoipSipAcceptedLoading({super.statusMessage, super.caller, super.callerId, super.callee, super.calleeId});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipAcceptedLoading',
      'statusMessage': statusMessage,
      'caller': caller,
      'callerId': callerId,
      'callee': callee,
      'calleeId': calleeId,
    };
  }

  factory VoipSipAcceptedLoading.fromJson(Map<String, dynamic> json) {
    return VoipSipAcceptedLoading(
      statusMessage: json['statusMessage'],
      caller: json['caller'],
      callerId: json['callerId'],
      callee: json['callee'],
      calleeId: json['calleeId'],
    );
  }
}

class VoipSipAcceptedSuccess extends VoipSipAccepted {
  const VoipSipAcceptedSuccess({super.statusMessage, super.caller, super.callerId, super.callee, super.calleeId});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipAcceptedSuccess',
      'statusMessage': statusMessage,
      'caller': caller,
      'callerId': callerId,
      'callee': callee,
      'calleeId': calleeId,
    };
  }

  factory VoipSipAcceptedSuccess.fromJson(Map<String, dynamic> json) {
    return VoipSipAcceptedSuccess(
      statusMessage: json['statusMessage'],
      caller: json['caller'],
      callerId: json['callerId'],
      callee: json['callee'],
      calleeId: json['calleeId'],
    );
  }
}

class VoipSipAcceptedError extends VoipSipRegistered {
  const VoipSipAcceptedError({super.statusMessage});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipAcceptedError',
      'statusMessage': statusMessage,
    };
  }

  factory VoipSipAcceptedError.fromJson(Map<String, dynamic> json) {
    return VoipSipAcceptedError(
      statusMessage: json['statusMessage'],
    );
  }
}

class VoipSipHangup extends VoipState {
  const VoipSipHangup({super.statusMessage, super.caller, super.callerId, super.callee, super.calleeId});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipHangup',
      'statusMessage': statusMessage,
      'caller': caller,
      'callerId': callerId,
      'callee': callee,
      'calleeId': calleeId,
    };
  }

  factory VoipSipHangup.fromJson(Map<String, dynamic> json) {
    switch (json['type']) {
      case 'VoipSipHangup':
        return VoipSipHangup(
          statusMessage: json['statusMessage'],
          caller: json['caller'],
          callerId: json['callerId'],
          callee: json['callee'],
          calleeId: json['calleeId'],
        );
      case 'VoipSipHangingUp':
        return VoipSipHangingUp.fromJson(json);
      case 'VoipSipHungUp':
        return VoipSipHungUp.fromJson(json);
      default:
        throw UnimplementedError('Unknown VoipSipHangup type: ${json['type']}');
    }
  }
}

class VoipSipHangingUp extends VoipSipHangup {
  const VoipSipHangingUp({super.statusMessage, super.caller, super.callerId, super.callee, super.calleeId});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipHangingUp',
      'statusMessage': statusMessage,
      'caller': caller,
      'callerId': callerId,
      'callee': callee,
      'calleeId': calleeId,
    };
  }

  factory VoipSipHangingUp.fromJson(Map<String, dynamic> json) {
    return VoipSipHangingUp(
      statusMessage: json['statusMessage'],
      caller: json['caller'],
      callerId: json['callerId'],
      callee: json['callee'],
      calleeId: json['calleeId'],
    );
  }
}

class VoipSipHungUp extends VoipSipHangup {
  const VoipSipHungUp({super.statusMessage, super.caller, super.callerId, super.callee, super.calleeId});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipHungUp',
      'statusMessage': statusMessage,
      'caller': caller,
      'callerId': callerId,
      'callee': callee,
      'calleeId': calleeId,
    };
  }

  factory VoipSipHungUp.fromJson(Map<String, dynamic> json) {
    return VoipSipHungUp(
      statusMessage: json['statusMessage'],
      caller: json['caller'],
      callerId: json['callerId'],
      callee: json['callee'],
      calleeId: json['calleeId'],
    );
  }
}

class VoipSipHangupError extends VoipState {
  const VoipSipHangupError({super.statusMessage});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipHangupError',
      'statusMessage': statusMessage,
    };
  }

  factory VoipSipHangupError.fromJson(Map<String, dynamic> json) {
    return VoipSipHangupError(
      statusMessage: json['statusMessage'],
    );
  }
}

class VoipSipProgress extends VoipSipRegistered {
  const VoipSipProgress({super.statusMessage, super.caller, super.callerId, super.callee, super.calleeId});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipProgress',
      'statusMessage': statusMessage,
      'caller': caller,
      'callerId': callerId,
      'callee': callee,
      'calleeId': calleeId,
    };
  }

  factory VoipSipProgress.fromJson(Map<String, dynamic> json) {
    return VoipSipProgress(
      statusMessage: json['statusMessage'],
      caller: json['caller'],
      callerId: json['callerId'],
      callee: json['callee'],
      calleeId: json['calleeId'],
    );
  }
}

class VoipSipCalling extends VoipSipRegistered {
  const VoipSipCalling(
      {super.statusMessage, super.caller, super.callerId, required super.callee, required super.calleeId});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipCalling',
      'statusMessage': statusMessage,
      'caller': caller,
      'callerId': callerId,
      'callee': callee,
      'calleeId': calleeId,
    };
  }

  factory VoipSipCalling.fromJson(Map<String, dynamic> json) {
    return VoipSipCalling(
      statusMessage: json['statusMessage'],
      caller: json['caller'],
      callerId: json['callerId'],
      callee: json['callee'],
      calleeId: json['calleeId'],
    );
  }
}

class VoipSipTransferCall extends VoipSipRegistered {
  const VoipSipTransferCall({super.statusMessage, super.caller, super.callerId, super.callee, super.calleeId});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipTransferCall',
      'statusMessage': statusMessage,
      'caller': caller,
      'callerId': callerId,
      'callee': callee,
      'calleeId': calleeId,
    };
  }

  factory VoipSipTransferCall.fromJson(Map<String, dynamic> json) {
    return VoipSipTransferCall(
      statusMessage: json['statusMessage'],
      caller: json['caller'],
      callerId: json['callerId'],
      callee: json['callee'],
      calleeId: json['calleeId'],
    );
  }
}

class VoipSipProceeding extends VoipSipRegistered {
  const VoipSipProceeding({super.statusMessage, super.caller, super.callerId, super.callee, super.calleeId});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipProceeding',
      'statusMessage': statusMessage,
      'caller': caller,
      'callerId': callerId,
      'callee': callee,
      'calleeId': calleeId,
    };
  }

  factory VoipSipProceeding.fromJson(Map<String, dynamic> json) {
    return VoipSipProceeding(
      statusMessage: json['statusMessage'],
      caller: json['caller'],
      callerId: json['callerId'],
      callee: json['callee'],
      calleeId: json['calleeId'],
    );
  }
}

class VoipSipRinging extends VoipSipRegistered {
  const VoipSipRinging({super.statusMessage, super.caller, super.callerId, super.callee, super.calleeId});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipSipRinging',
      'statusMessage': statusMessage,
      'caller': caller,
      'callerId': callerId,
      'callee': callee,
      'calleeId': calleeId,
    };
  }

  factory VoipSipRinging.fromJson(Map<String, dynamic> json) {
    return VoipSipRinging(
      statusMessage: json['statusMessage'],
      caller: json['caller'],
      callerId: json['callerId'],
      callee: json['callee'],
      calleeId: json['calleeId'],
    );
  }
}

class VoipConnectionError extends VoipState {
  const VoipConnectionError({super.statusMessage});

  @override
  Map<String, dynamic> toJson() {
    return {
      'type': 'VoipConnectionError',
      'statusMessage': statusMessage,
    };
  }

  factory VoipConnectionError.fromJson(Map<String, dynamic> json) {
    return VoipConnectionError(
      statusMessage: json['statusMessage'],
    );
  }
}

import 'package:ddone/utils/logger_util.dart';
import 'package:moxlib/moxlib.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:uuid/uuid.dart';

/// A negotiator that implements resource binding.
/// It can either request a server-generated resource or a client-specified one.
class FixedResourceBindingNegotiator extends ResourceBindingNegotiator {
  /// The fixed resource ID to request. If null or empty, the server will generate one.
  final String? _fixedResourceId;

  FixedResourceBindingNegotiator(String fixedResourceId)
      : _fixedResourceId = fixedResourceId,
        super();

  /// Flag indicating the state of the negotiator:
  /// - True: We sent a binding request
  /// - False: We have not yet sent the binding request
  bool _requestSent = false;

  @override
  Future<Result<NegotiatorState, NegotiatorError>> negotiate(
    XMLNode nonza,
  ) async {
    // Ensure the connection attributes are available
    if (!_requestSent) {
      final bindChildren = <XMLNode>[];
      if (_fixedResourceId != null && _fixedResourceId.isNotEmpty) {
        bindChildren.add(
          XMLNode(
            tag: 'resource',
            text: _fixedResourceId,
          ),
        );
      }

      final stanza = XMLNode.xmlns(
        tag: 'iq',
        xmlns: stanzaXmlns,
        attributes: {
          'type': 'set',
          'id': const Uuid().v4(),
        },
        children: [
          XMLNode.xmlns(
            tag: 'bind',
            xmlns: bindXmlns,
            children: bindChildren,
          ),
        ],
      );

      _requestSent = true;
      attributes.sendNonza(stanza);
      return const Result(NegotiatorState.ready);
    } else {
      // This part handles the server's response
      if (nonza.tag != 'iq' || nonza.attributes['type'] != 'result') {
        log.e('Received non-result IQ or unexpected stanza');
        return Result(ResourceBindingFailedError());
      }

      final bind = nonza.firstTag('bind', xmlns: bindXmlns);
      if (bind == null) {
        log.e('<bind> element not found in result');
        return Result(ResourceBindingFailedError());
      }

      final jidNode = bind.firstTag('jid');
      if (jidNode == null || jidNode.innerText().isEmpty) {
        log.e('<jid> element not found or empty in <bind> result');
        return Result(ResourceBindingFailedError());
      }

      final rawJid = jidNode.innerText();
      try {
        final boundJid = JID.fromString(rawJid);
        attributes.setResource(boundJid.resource);

        // Optional: Verify if the server respected the requested resource
        if (_fixedResourceId != null && _fixedResourceId.isNotEmpty && boundJid.resource != _fixedResourceId) {
          // Log a warning or handle as an error if strict matching is required
          // For now, we accept what the server gives us.
          log.w("Server assigned resource '${boundJid.resource}' despite client requesting '$_fixedResourceId'");
        }
        return const Result(NegotiatorState.done);
      } catch (e) {
        log.e('Failed to parse JID from server: $e');
        return Result(ResourceBindingFailedError());
      }
    }
  }

  @override
  void reset() {
    _requestSent = false;
    super.reset();
  }
}

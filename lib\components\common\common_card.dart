import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CommonCard extends StatelessWidget {
  final String title, desc;

  const CommonCard({
    required this.title,
    required this.desc,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final textTheme = themeState.themeData.textTheme;
        final colorTheme = themeState.colorTheme;

        final height = context.responsiveSize<double>(
          moileSize: heightExtraLarge,
          tabletSize: heightExtraLarge,
          desktopSize: heightExtraLarge,
          largeScreenSize: heightXXLarge,
        );

        final titleTextStyle = context.responsiveSize<TextStyle>(
          moileSize: textTheme.titleMedium!,
          tabletSize: textTheme.titleMedium!,
          desktopSize: textTheme.titleMedium!,
          largeScreenSize: textTheme.titleLarge!,
        );

        final contentTextStyle = context.responsiveSize<TextStyle>(
          moileSize: textTheme.titleMedium!,
          tabletSize: textTheme.titleMedium!,
          desktopSize: textTheme.titleMedium!,
          largeScreenSize: textTheme.titleLarge!,
        );

        final cardPadding = context.responsiveSize<EdgeInsets>(
          moileSize: const EdgeInsets.symmetric(horizontal: spacingLarge),
          tabletSize: const EdgeInsets.symmetric(horizontal: spacingLarge),
          desktopSize: const EdgeInsets.symmetric(horizontal: spacingLarge),
          largeScreenSize: const EdgeInsets.symmetric(horizontal: spacingMedium),
        );

        final textPadding = context.responsiveSize<EdgeInsets>(
          moileSize: const EdgeInsets.fromLTRB(0, 4, 0, 0),
          tabletSize: const EdgeInsets.fromLTRB(0, 4, 0, 0),
          desktopSize: const EdgeInsets.fromLTRB(0, 4, 0, 0),
          largeScreenSize: const EdgeInsets.fromLTRB(0, 8, 0, 0),
        );

        return Padding(
          padding: cardPadding,
          child: SizedBox(
            height: height,
            child: Card(
              elevation: 3,
              margin: const EdgeInsets.fromLTRB(40, 4, 40, 4),
              color: colorTheme.primaryColor.withOpacity(opacityExtraLow),
              child: ListTile(
                title: Padding(
                  padding: textPadding,
                  child: Text(
                    title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: titleTextStyle.copyWith(color: colorTheme.onPrimaryColor),
                  ),
                ),
                subtitle: Padding(
                  padding: textPadding,
                  child: BlocBuilder<ThemeCubit, ThemeState>(
                    builder: (context, themeState) {
                      return Text(
                        desc,
                        overflow: TextOverflow.ellipsis,
                        style: contentTextStyle.copyWith(color: colorTheme.primaryColor),
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

part 'contacts_repository.g.dart';

@RestApi(baseUrl: '')
abstract class ContactRepository {
  factory ContactRepository(Dio dio, {String baseUrl}) = _ContactRepository;

  @GET('') // https://dir.uc1.dotdashtech.com/ctc/tcsp3.php?domain={sipDomain}&ext={sipNumber}&ha1=
  Future<dynamic> getContactList(
    @Query('domain') String sipDomain,
    @Query('ext') String sipNumber,
    @Query('ha1') String secretHash, // pass in empty string for this secretHash for now, DDPhone side got issue.
  );
}

final characterOnlyRegex = RegExp(r'^([a-zA-Z]\w*)$');

final hashtagRegex = RegExp(r'\B(\#' + _unicodeCharConstant + r'+)(?!;)', multiLine: true);

final imageContentTypeRegex = RegExp(r'image\/*');

final passwordPatternRegex =
    RegExp("((?=.*[a-z])(?=.*\\d)(?=.*[A-Z])(?=.*[!\"”#\$%&'’()*+,-./:;<=>?@\\[\\\\\\]^_`{|}~])(?=\\S+\$).{8,})");

final rawUserTagRegex = RegExp(_rawUserTagConstant);

final singleAliasRegex = RegExp('@');

final singleHashRegex = RegExp('#');

final socialPostRegex = RegExp(hashAndUrlRegexConst + r'|' + _rawUserTagConstant);

final urlRegex = RegExp(_urlRegexConstant);

final contactIdRegex = RegExp(_contactIdRegexConstant);

final sipNumberRegex = RegExp(_sipNumberRegex);

final noSpecialCharacterRegex = RegExp(_noSpecialCharacterRegex);

final createGroupNameRegex = RegExp(createGroupNameConstant);

const hashAndUrlRegexConst = r'\B([#]' + _unicodeCharConstant + r'+)(?!;)|' + _urlRegexConstant;

const _aliasAnd24HexValueConstant = r'[@][0-9a-fA-F]{24}';

const _rawUserTagConstant = r'(\[.*?\]).*?(\((' + _aliasAnd24HexValueConstant + r')(?!;)\))';

const _unicodeCharConstant = r'[^\s!@#$%^&*()=+./,\[{\]};:"' r'"?><]';

const _urlRegexConstant =
    r'(https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*))';

const _contactIdRegexConstant = "r'[a-zA-Z]'";

const _sipNumberRegex = r'sip:(\d+)@';

const _noSpecialCharacterRegex = r'[^a-zA-Z0-9]';

final groupNameRegex = RegExp(r'\_[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$');

final groupDomainRegex = RegExp(groupDomainConstant);

const groupDomainConstant = "r'@muc.[a-zA-Z0-9]+.(dotdashtech.com)'";

const createGroupNameConstant = r'[a-zA-Z0-9]';

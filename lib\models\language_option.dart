import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

const defaultLanguage = LanguageOption(Locale('en', 'GB'), 'English', 'gb');
const chineseLanguage = LanguageOption(Locale('zh', 'CN'), '简体中文', 'cn');
const malayLanguage = LanguageOption(Locale('ms', 'MY'), 'Bahasa Melayu', 'my');

List<LanguageOption> supportedLanguages = [
  chineseLanguage,
  defaultLanguage,
  malayLanguage,
];

class LanguageOption extends Equatable {
  final Locale locale;
  final String displayName;
  final String countryFlagName;

  const LanguageOption(this.locale, this.displayName, this.countryFlagName);

  factory LanguageOption.fromString(String localeStr) {
    return supportedLanguages.firstWhere(
      (language) => language.locale.toString() == localeStr,
      orElse: () => defaultLanguage,
    );
  }

  @override
  List<Object?> get props => [locale.countryCode, locale.languageCode, displayName];
}

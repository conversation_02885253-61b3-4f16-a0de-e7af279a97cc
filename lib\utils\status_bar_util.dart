import 'package:ddone/utils/logger_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ddone/utils/screen_util.dart';

/// Utility class for managing status bar appearance
/// Handles status bar color, brightness, and icon colors consistently across the app
class StatusBarUtil {
  /// Sets status bar style based on theme brightness
  /// This ensures status bar icons are always visible regardless of background
  static void setStatusBarStyle({
    required Brightness brightness,
    Color? statusBarColor,
  }) {
    if (!isMobile) return; // Only apply to mobile devices

    // Determine appropriate status bar icon brightness
    // For dark themes: use light icons (white)
    // For light themes: use dark icons (black)
    final statusBarIconBrightness = brightness == Brightness.dark
        ? Brightness.light // Light icons on dark background
        : Brightness.dark; // Dark icons on light background

    // Set system UI overlay style
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        // Status bar background color (transparent for edge-to-edge)
        statusBarColor: statusBarColor ?? Colors.transparent,

        // Status bar icon brightness (battery, time, etc.)
        statusBarIconBrightness: statusBarIconBrightness,

        // Status bar text brightness (for Android)
        statusBarBrightness: statusBarIconBrightness,

        // // Navigation bar styling (for Android)
        // systemNavigationBarColor: Colors.transparent,
        // systemNavigationBarIconBrightness: statusBarIconBrightness,

        // // Enforce edge-to-edge on Android
        // systemNavigationBarDividerColor: Colors.transparent,
      ),
    );
  }

  /// Sets light status bar (dark icons on light background)
  /// Use this for light themes or light backgrounds
  static void setLightStatusBar() {
    if (!isMobile) return;

    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark, // Dark icons
        statusBarBrightness: Brightness.dark, // Dark text
        // systemNavigationBarColor: Colors.transparent,
        // systemNavigationBarIconBrightness: Brightness.dark,
        // systemNavigationBarDividerColor: Colors.transparent,
      ),
    );
  }

  /// Sets dark status bar (light icons on dark background)
  /// Use this for dark themes or dark backgrounds
  static void setDarkStatusBar() {
    if (!isMobile) return;

    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light, // Light icons
        statusBarBrightness: Brightness.light, // Light text
        // systemNavigationBarColor: Colors.transparent,
        // systemNavigationBarIconBrightness: Brightness.light,
        // systemNavigationBarDividerColor: Colors.transparent,
      ),
    );
  }

  /// Sets transparent status bar with appropriate icon colors
  /// This is the recommended approach for modern apps
  static void setTransparentStatusBar({
    required Brightness appBrightness,
  }) {
    setStatusBarStyle(
      brightness: appBrightness,
      statusBarColor: Colors.transparent,
    );
  }

  /// Platform-specific status bar setup
  /// Call this during app initialization
  static void initializeStatusBar({
    required Brightness appBrightness,
  }) {
    if (isMobile) {
      // Set transparent status bar for edge-to-edge experience
      setTransparentStatusBar(appBrightness: appBrightness);
    }
    // Desktop platforms don't need status bar styling
  }

  /// Get appropriate SystemUiOverlayStyle for AppBar
  /// Use this in AppBar.systemOverlayStyle
  static SystemUiOverlayStyle getAppBarOverlayStyle({
    required Brightness appBrightness,
  }) {
    final statusBarIconBrightness = appBrightness == Brightness.dark ? Brightness.light : Brightness.dark;

    return SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: statusBarIconBrightness,
      statusBarBrightness: statusBarIconBrightness,
      // systemNavigationBarColor: Colors.transparent,
      // systemNavigationBarIconBrightness: statusBarIconBrightness,
      // systemNavigationBarDividerColor: Colors.transparent,
    );
  }

  /// Update status bar based on current system theme
  /// Call this when system theme changes or app theme changes
  static void updateStatusBarForTheme(ThemeData themeData) {
    if (!isMobile) return;

    setStatusBarStyle(brightness: themeData.brightness);
  }

  /// Debug method to log current status bar settings
  static void debugStatusBarInfo({
    required Brightness appBrightness,
    String? context,
  }) {
    final iconBrightness = appBrightness == Brightness.dark ? 'light' : 'dark';
    log.t('StatusBar Debug ${context ?? ''}: '
        'App brightness: $appBrightness, '
        'Icon brightness: $iconBrightness');
  }
}

/// Widget that automatically manages status bar styling based on system theme
class StatusBarWrapper extends StatelessWidget {
  final Widget child;

  const StatusBarWrapper({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    // Get current system brightness
    final brightness = MediaQuery.of(context).platformBrightness;

    // Set status bar style based on system brightness
    final statusBarIconBrightness = brightness == Brightness.dark
        ? Brightness.light // Light icons on dark background
        : Brightness.dark; // Dark icons on light background

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: statusBarIconBrightness,
        statusBarBrightness: statusBarIconBrightness,
        // systemNavigationBarColor: Colors.transparent,
        // systemNavigationBarIconBrightness: statusBarIconBrightness,
        // systemNavigationBarDividerColor: Colors.transparent,
      ),
      child: child,
    );
  }
}

import 'package:ddone/constants/value_constants.dart';

enum DialpadEnum {
  zero,
  one,
  two,
  three,
  four,
  five,
  six,
  seven,
  eight,
  nine,
  pound,
  star,
}

extension DialpadEnumExtension on DialpadEnum {
  String dialpadTonePath() {
    const String prefix = dialPadSoundPrefix;

    switch (this) {
      case DialpadEnum.zero:
        return '$prefix/0.mp3';
      case DialpadEnum.one:
        return '$prefix/1.mp3';
      case DialpadEnum.two:
        return '$prefix/2.mp3';
      case DialpadEnum.three:
        return '$prefix/3.mp3';
      case DialpadEnum.four:
        return '$prefix/4.mp3';
      case DialpadEnum.five:
        return '$prefix/5.mp3';
      case DialpadEnum.six:
        return '$prefix/6.mp3';
      case DialpadEnum.seven:
        return '$prefix/7.mp3';
      case DialpadEnum.eight:
        return '$prefix/8.mp3';
      case DialpadEnum.nine:
        return '$prefix/9.mp3';
      case DialpadEnum.pound:
        return '$prefix/pound.mp3';
      case DialpadEnum.star:
        return '$prefix/star.mp3';
      default:
        return '$prefix/star.mp3';
    }
  }
}

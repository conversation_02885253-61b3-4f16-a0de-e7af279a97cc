import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/invitation/invitation_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/cubit/xmpp/connection/xmpp_connection_cubit.dart' as xmpp_cubit;
import 'package:ddone/service_locator.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:ddone/cubit/add_bookmark/add_bookmark_cubit.dart';

import 'package:ddone/models/invitation_model.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationsDropdown extends StatefulWidget {
  const NotificationsDropdown({super.key});

  @override
  State<NotificationsDropdown> createState() => _NotificationsDropdownState();
}

class _NotificationsDropdownState extends State<NotificationsDropdown> {
  late AddBookmarkCubit addBookmarkCubit;
  late LoginCubit _loginCubit;
  late InvitationCubit _invitationCubit;
  late GroupUiCubit _groupUiCubit;
  final BookmarkListCubit bookmarkListCubit = BookmarkListCubit();
  List<bool> roomStates = [];
  @override
  void initState() {
    super.initState();
    _loginCubit = BlocProvider.of<LoginCubit>(context);
    _invitationCubit = BlocProvider.of<InvitationCubit>(context);
    _groupUiCubit = BlocProvider.of<GroupUiCubit>(context);
    addBookmarkCubit = AddBookmarkCubit.initial();
    debugPrint('$roomStates');
  }

  Future<void> joinChatRoom() async {
    try {
      if (_loginCubit.state is LoginAuthenticated) {
        LoginAuthenticated currentLoginState = _loginCubit.state as LoginAuthenticated;

        if (_invitationCubit.state is InvitationReceived) {
          // final InvitationCubit invitationCubit = InvitationCubit();
          InvitationReceived currentInvitationState = _invitationCubit.state as InvitationReceived;
          final invitationInfo = currentInvitationState.invitationList;
          for (var invitations in invitationInfo) {
            final toSplit = invitations.to.split('@');
            final mucName = JID.fromString(invitations.roomJid.toString());
            final nickName = JID.fromString(toSplit[0].toString());
            final xmppAccount = sl.get<SharedPreferences>();
            final sipNum = xmppAccount.get(CacheKeys.sipNumber);
            final sipDomain = xmppAccount.get(CacheKeys.sipDomain);
            final sipAccount = '$sipNum@$sipDomain';

            final muc = currentLoginState.connection.getManagerById<MUCManager>(mucManager)!;

            if (mucName.toString().isNotEmpty && nickName.toString().isNotEmpty) {
              addBookmarkCubit.addPendingBookmark(
                mucName.toString(),
                'true',
                mucName.toString(),
                nickName.toString(),
              );
              await addBookmarkCubit.addBookmark(
                loginCubit: _loginCubit,
                bookmarkListCubit: bookmarkListCubit,
                groupUiCubit: _groupUiCubit,
                // groupUiCubit: _groupUiCubit,
                // roomJid: roomJid,
                // nick: nick,
              );

              await muc.joinRoom(
                mucName,
                nickName.toString(),
                maxHistoryStanzas: 0,
              );
            }

            await muc.configure(
              mucName,
              nickName.toString(),
              JID.fromString(sipAccount),
            );

            // await muc.setConfigure(
            //   mucName,
            //   nickName.toString(),
            // );
            try {
              await muc.registerUser(
                mucName,
                nickName.toString(),
                sipAccount,
              );
            } catch (e) {
              debugPrint('Error registering user: $e');
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error creating chat room: $e');
    }
  }

  Future<void> _groupState(List<InvitationModel> invitationList, MUCManager muc) async {
    for (var i = 0; i < invitationList.length; i++) {
      var info = invitationList[i];

      final room = JID.fromString(info.roomJid).toBare();

      if (info.roomJid.isNotEmpty) {
        final roomState = (await muc.getRoomState(room));
        debugPrint("Members: ${roomState?.members.values.map((m) => m.nick).join(", ")}");
        debugPrint(
            'Room State: $roomState Joined? ${roomState?.joined}, Members: ${roomState?.members} bool ${roomStates.isEmpty} ${info.roomJid.toJID()}');
        if (roomState != null) {
          bool mucS = roomState.joined;
          setState(() {
            roomStates[i] = mucS;
          });
        } else {
          setState(() {
            roomStates[i] = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<xmpp_cubit.XmppConnectionCubit, xmpp_cubit.XmppConnectionState>(
      builder: (context, xmppConnectionState) {
        if (xmppConnectionState is xmpp_cubit.XmppConnectionSuccess) {
          return BlocBuilder<InvitationCubit, InvitationState>(
            builder: (context, invitationState) {
              debugPrint('invitationState.invitationList.length: ${invitationState.invitationList}');
              debugPrint('invitationState.invitationList.length: ${invitationState.invitationList.length}');
              debugPrint(
                  'roomStates: $roomStates, ${roomStates.isEmpty} ${roomStates.contains(true)}, ${roomStates.contains(false)}');

              // LoginAuthenticated currentLoginState = xmppConnectionState;
              // final muc = currentLoginState.connection.getManagerById<MUCManager>(mucManager)!;
              if (roomStates.isEmpty && invitationState.invitationList.isNotEmpty) {
                roomStates = List<bool>.filled(invitationState.invitationList.length, false);
                // _groupState(invitationState.invitationList, muc);
                debugPrint('${invitationState.invitationList.length}');
              }
              return MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTapDown: (TapDownDetails details) {
                    _showCustomMenu(
                      context,
                      details.globalPosition,
                      invitationState.invitationList,
                    );
                  },
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          invitationState.invitationList.isEmpty || roomStates.contains(true)
                              ? Icons.notifications_none_sharp
                              : Icons.notifications_active_rounded,
                          color: context.colorTheme().primaryContainer,
                          size: 28,
                        ),
                      ),
                      // if (invitationState.invitationList.isNotEmpty)
                      roomStates.contains(true)
                          ? Positioned(
                              right: 11,
                              top: 10,
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  color: Colors.black,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                constraints: const BoxConstraints(
                                  minWidth: 15,
                                  minHeight: 5,
                                ),
                                child: const Text(
                                  '0',
                                  style: TextStyle(color: Colors.white, fontSize: 11, fontWeight: FontWeight.w400),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            )
                          : Positioned(
                              right: 11,
                              top: 10,
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  color: Colors.black,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                constraints: const BoxConstraints(
                                  minWidth: 15,
                                  minHeight: 5,
                                ),
                                child: Text(
                                  '${invitationState.invitationList.length}',
                                  style:
                                      const TextStyle(color: Colors.white, fontSize: 11, fontWeight: FontWeight.w400),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                    ],
                  ),
                ),
              );
            },
          );
        } else {
          return const SizedBox();
        }
      },
    );
  }

  void _showCustomMenu(BuildContext context, Offset position, List<InvitationModel> invitations) {
    if (_loginCubit.state is LoginAuthenticated) {
      final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
      LoginAuthenticated currentLoginState = _loginCubit.state as LoginAuthenticated;
      final muc = currentLoginState.connection.getManagerById<MUCManager>(mucManager)!;
      if (roomStates.isEmpty && invitations.isNotEmpty) {
        roomStates = List<bool>.filled(invitations.length, false);
        _groupState(invitations, muc);
        debugPrint('${invitations.length}');
      }
      debugPrint('InviteList: ${invitations.length}');

      showMenu(
        context: context,
        position: RelativeRect.fromLTRB(
          position.dx,
          position.dy,
          overlay.size.width - position.dx,
          overlay.size.height - position.dy,
        ),
        items: invitations.isEmpty || roomStates.contains(true)
            ? [
                PopupMenuItem<int>(
                  value: 0,
                  enabled: false,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: const Text(
                      'No notifications',
                      style: TextStyle(color: Colors.black),
                    ),
                  ),
                ),
              ]
            : invitations.map((invitation) {
                return PopupMenuItem<int>(
                  value: invitations.indexOf(invitation),
                  child: ListTile(
                    leading: Icon(
                      Icons.group_add_outlined,
                      color: Colors.amber[300],
                    ),
                    title: Text(
                      'Group invitation from: ${invitation.from}',
                      style: const TextStyle(
                        color: Colors.black,
                      ),
                    ),
                    // subtitle: Text(
                    //   invitation.reason,
                    //   style: const TextStyle(
                    //     color: Colors.black,
                    //   ),
                    // ),
                    onTap: () {
                      // showAcceptInviteDialog(context);
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          backgroundColor: context.colorTheme().surface,
                          title: const Center(
                            child: Text(
                              'Group Invitation',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 17,
                              ),
                            ),
                          ),
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Text(
                                'Accept/Decline this group invitation',
                                style: TextStyle(
                                  color: Colors.white70,
                                ),
                              ),
                              Text(
                                'from ${invitation.from}',
                                style: const TextStyle(
                                  color: Colors.white70,
                                ),
                              ),
                              Text(
                                'Group ${invitation.roomJid}',
                                style: const TextStyle(
                                  color: Colors.white70,
                                ),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              // const TextField(
                              //   style: TextStyle(
                              //     color: Colors.white,
                              //   ),
                              //   cursorColor: ddOrange,
                              //   // controller: _invitereasonEditingController,
                              //   decoration: InputDecoration(
                              //     hintText: 'Reason to decline',
                              //     hintStyle: TextStyle(
                              //       color: Colors.white54,
                              //     ),
                              //     // filled: true,
                              //     // fillColor: greyBackground,
                              //     enabledBorder: OutlineInputBorder(
                              //       borderSide: BorderSide(
                              //         color: Colors.white70,
                              //       ),
                              //     ),
                              //     focusedBorder: OutlineInputBorder(
                              //       borderSide: BorderSide(
                              //         color: Colors.white70,
                              //       ),
                              //     ),
                              //   ),
                              // ),
                              const SizedBox(
                                height: 10,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  TextButton(
                                    onPressed: () {
                                      // Navigator.pop(context);
                                      pop();
                                    },
                                    child: const Text(
                                      'Cancel',
                                      style: TextStyle(
                                        color: Colors.red,
                                      ),
                                    ),
                                  ),
                                  BlocBuilder<GroupUiCubit, GroupUiState>(
                                    builder: (context, state) {
                                      return TextButton(
                                        onPressed: () async {
                                          joinChatRoom();
                                          context.read<GroupUiCubit>().update();
                                          // for (var i = 0; i < invitations.length; i++) {
                                          //   setState(() {
                                          //     roomStates[i] = true;
                                          //   });
                                          // }

                                          // inviteUser();
                                          pop();
                                        },
                                        child: const Text(
                                          'Accept',
                                          style: TextStyle(
                                            color: Colors.green,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                      // Handle invitation tap
                    },
                  ),
                );
              }).toList(),
      );
    }
  }
}

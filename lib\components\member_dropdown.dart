import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/members_presence/members_presence_cubit.dart';
import 'package:ddone/cubit/update/info_cubit.dart';
import 'package:ddone/models/xmpp_model/presence_info.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/extensions/string_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MembersDropdown extends StatefulWidget {
  const MembersDropdown({super.key});
  static String? selectedGroup;

  @override
  State<MembersDropdown> createState() => _MembersDropdownState();
}

class _MembersDropdownState extends State<MembersDropdown> {
  int getMemberCount(Map<String, List<PresenceInfo>> members) {
    final infoState = _infoCubit.state;

    if (infoState.receiver.isNotEmpty && members.containsKey(infoState.receiver)) {
      print('Group Length: ${members[infoState.receiver]!.length} <> Selected Grp : ${infoState.receiver}');
      return members[infoState.receiver]!.length;
    }
    return 0;
  }

  late InfoCubit _infoCubit;
  late LoginCubit _loginCubit;
  late PresencesCubit _presencesCubit;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _infoCubit = BlocProvider.of<InfoCubit>(context);
    _loginCubit = BlocProvider.of<LoginCubit>(context);
    _presencesCubit = BlocProvider.of<PresencesCubit>(context);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PresencesCubit, PresencesState>(
      builder: (context, presenceState) {
        final membList = presenceState.presenceList;

        return BlocBuilder<InfoCubit, InfoState>(
          builder: (context, state) {
            int memberCount = getMemberCount(membList);
            return MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTapDown: (TapDownDetails details) {
                  _showCustomMenu(
                    context,
                    details.globalPosition,
                    membList,
                    _loginCubit,
                  );
                },
                child: Stack(
                  children: [
                    Icon(
                      // mucStates.contains(true) ?
                      Icons.people_outline_sharp,
                      // : Icons.notifications_none_sharp,
                      color: context.colorTheme().primaryContainer,
                      // Colors.amber[100],
                      size: 28,
                    ),
                    if (memberCount > 0)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Colors.black,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 15,
                            minHeight: 5,
                          ),
                          child: Text(
                            '$memberCount',
                            style: const TextStyle(color: Colors.white, fontSize: 11, fontWeight: FontWeight.w400),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _showCustomMenu(
    BuildContext context,
    Offset position,
    Map<String, List<PresenceInfo>> members,
    LoginCubit loginCubit,
  ) {
    if (loginCubit.state is LoginAuthenticated) {
      LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;
      final muc = currentLoginState.connection.getManagerById<MUCManager>(mucManager)!;
      final RenderBox overlay = Overlay.of(context).context.findRenderObject() as RenderBox;
      TextEditingController deleteReason = TextEditingController();

      final xmppAccount = sl.get<SharedPreferences>();
      final sipNum = xmppAccount.get(CacheKeys.sipNumber);
      final sipDomain = xmppAccount.get(CacheKeys.sipDomain);
      final sipAccount = '$sipNum@$sipDomain';

      final infoState = _infoCubit.state;

      // if (mucStates.isEmpty && invitations.isNotEmpty) {
      //   mucStates = List<bool>.filled(invitations.length, false);
      //   _updateMucStates(invitations, muc);
      // }

      List<PresenceInfo> filteredMembers = [];
      if (infoState.receiver.isNotEmpty && members.containsKey(infoState.receiver)) {
        filteredMembers = members[infoState.receiver]!;
      }

      final mem = _presencesCubit.state.presenceList[infoState.receiver];
      bool isMeOwner = false;
      if (mem != null) {
        isMeOwner = mem.any((member) => member.affiliation == 'owner' && member.jid == sipAccount);
      } else {
        isMeOwner = false;
      }

      showMenu(
        context: context,
        position: RelativeRect.fromLTRB(
          position.dx,
          position.dy,
          overlay.size.width - position.dx,
          overlay.size.height - position.dy,
        ),
        items: members.isEmpty
            ? [
                PopupMenuItem<int>(
                  value: 0,
                  enabled: false,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: const Text(
                      'No member',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ]
            : filteredMembers.map((member) {
                final memberJidSplit = member.jid.userIDFilter();
                Color backgroundColor;
                switch (member.affiliation) {
                  case 'owner':
                    backgroundColor = Colors.orange[300]!;
                    break;
                  case 'member':
                    backgroundColor = Colors.green[200]!;
                    break;
                  case 'admin':
                    backgroundColor = Colors.blue[300]!;
                    break;
                  default:
                    backgroundColor = Colors.grey[300]!;
                    break;
                }
                return PopupMenuItem(
                  value: filteredMembers.indexOf(member),
                  child: ListTile(
                    leading: Icon(
                      Icons.person,
                      color: Colors.amber[300],
                    ),
                    title: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          member.jid == sipAccount ? '$memberJidSplit (You)' : memberJidSplit,
                          style: const TextStyle(
                            color: Colors.black,
                          ),
                        ),
                        const SizedBox(width: 5),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                              20,
                            ),
                            color: backgroundColor,
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6.0,
                              vertical: 3.0,
                            ),
                            child: Text(
                              member.affiliation,
                              style: const TextStyle(
                                color: Colors.black,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          backgroundColor: context.colorTheme().surface,
                          title: Center(
                            child: Text(
                              member.affiliation,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 17,
                              ),
                            ),
                          ),
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Text(
                                    'User:',
                                    style: TextStyle(
                                      color: Colors.white70,
                                    ),
                                  ),
                                  Card(
                                    elevation: 3,
                                    color: Colors.grey[700],
                                    child: Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: Text(
                                        member.jid,
                                        style: const TextStyle(
                                          color: Colors.white70,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  const Text(
                                    'Affiliation:',
                                    style: TextStyle(
                                      color: Colors.white70,
                                    ),
                                  ),
                                  Card(
                                    elevation: 3,
                                    color: Colors.grey[700],
                                    child: Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: Text(
                                        member.affiliation,
                                        style: const TextStyle(
                                          color: Colors.white70,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              member.nick.isNotEmpty
                                  ? Row(
                                      children: [
                                        const Text(
                                          'Nickname:',
                                          style: TextStyle(
                                            color: Colors.white70,
                                          ),
                                        ),
                                        Card(
                                          elevation: 3,
                                          color: Colors.grey[700],
                                          child: Padding(
                                            padding: const EdgeInsets.all(10.0),
                                            child: Text(
                                              member.nick,
                                              style: const TextStyle(
                                                color: Colors.white70,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    )
                                  : Container(),
                              member.role.isNotEmpty && member.role != 'null'
                                  ? Row(
                                      children: [
                                        const Text(
                                          'Role:',
                                          style: TextStyle(
                                            color: Colors.white70,
                                          ),
                                        ),
                                        Card(
                                          elevation: 3,
                                          color: Colors.grey[700],
                                          child: Padding(
                                            padding: const EdgeInsets.all(10.0),
                                            child: Text(
                                              member.role,
                                              style: const TextStyle(
                                                color: Colors.white70,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    )
                                  : Container(),
                              const SizedBox(
                                height: 10,
                              ),
                              // const TextField(
                              //   style: TextStyle(
                              //     color: Colors.white,
                              //   ),
                              //   cursorColor: ddOrange,
                              //   // controller: _invitereasonEditingController,
                              //   decoration: InputDecoration(
                              //     hintText: 'Reason to decline',
                              //     hintStyle: TextStyle(
                              //       color: Colors.white54,
                              //     ),
                              //     // filled: true,
                              //     // fillColor: greyBackground,
                              //     enabledBorder: OutlineInputBorder(
                              //       borderSide: BorderSide(
                              //         color: Colors.white70,
                              //       ),
                              //     ),
                              //     focusedBorder: OutlineInputBorder(
                              //       borderSide: BorderSide(
                              //         color: Colors.white70,
                              //       ),
                              //     ),
                              //   ),
                              // ),
                              const SizedBox(
                                height: 10,
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  TextButton(
                                    onPressed: () async {
                                      // Navigator.pop(context);
                                      pop();
                                      member.jid == sipAccount || !isMeOwner
                                          ? null
                                          : await showDialog(
                                              context: context,
                                              builder: (context) => AlertDialog(
                                                backgroundColor: context.colorTheme().surface,
                                                title: Center(
                                                  child: Text(
                                                    'Kicking a ${member.affiliation}',
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 17,
                                                    ),
                                                  ),
                                                ),
                                                content: Column(
                                                  mainAxisSize: MainAxisSize.min,
                                                  children: [
                                                    Text(
                                                      'You sure want to kick ${member.nick}?',
                                                      style: const TextStyle(
                                                        color: Colors.white70,
                                                      ),
                                                    ),
                                                    Text(
                                                      'User: ${member.jid}',
                                                      style: const TextStyle(
                                                        color: Colors.white70,
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      height: 10,
                                                    ),
                                                    TextField(
                                                      controller: deleteReason,
                                                      style: const TextStyle(
                                                        color: Colors.white,
                                                      ),
                                                      decoration: InputDecoration(
                                                        hintText: 'Reason to kick',
                                                        hintStyle: context.textStyle().bodyLarge!.copyWith(
                                                              color: Colors.white54,
                                                              fontWeight: FontWeight.w500,
                                                            ),
                                                        //  const TextStyle(
                                                        //   color: Colors.white54,
                                                        // ),
                                                        enabledBorder: OutlineInputBorder(
                                                          borderRadius: BorderRadius.circular(
                                                            20,
                                                          ),
                                                          borderSide: const BorderSide(
                                                            color: Colors.white54,
                                                          ),
                                                        ),
                                                        focusedBorder: OutlineInputBorder(
                                                          borderRadius: BorderRadius.circular(
                                                            20,
                                                          ),
                                                          borderSide: const BorderSide(
                                                            color: Colors.white54,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    const SizedBox(
                                                      height: 10,
                                                    ),
                                                    TextButton(
                                                      onPressed: () async {
                                                        if (loginCubit.state is LoginAuthenticated) {
                                                          // await muc.kickUseraffi(
                                                          //   JID.fromString(infoState.receiver),
                                                          //   member.nick,
                                                          //   sipAccount,
                                                          // );

                                                          await muc.kickUser(
                                                            JID.fromString(infoState.receiver),
                                                            member.nick,
                                                            deleteReason.text,
                                                            sipAccount,
                                                          );
                                                        }
                                                        pop();
                                                      },
                                                      child: const Text(
                                                        'Kick',
                                                        style: TextStyle(
                                                          color: Colors.red,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            );
                                    },
                                    child: Text(
                                      member.jid == sipAccount || !isMeOwner ? 'Close' : 'Kick',
                                      style: const TextStyle(
                                        color: Colors.red,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                    // subtitle: Container(
                    //   // width:
                    //   //  context.deviceWidth() * 0.0005,
                    //   color: backgroundColor,
                    //   child: Text(
                    //     member.affiliation,
                    //     style: const TextStyle(
                    //       color: Colors.black,
                    //     ),
                    //     textAlign: TextAlign.center,
                    //   ),
                    // ),
                  ),
                );
              }).toList(),

        // members.entries.expand((entry) {
        //  final roomName = entry.key;
        //   final roomMembers = entry.value;

        //   return roomMembers.map((member) {
        //     return PopupMenuItem<int>(
        //       value: roomMembers.indexOf(member),
        //       child: ListTile(
        //         leading: Icon(
        //           Icons.person,
        //           color: Colors.amber[300],
        //         ),
        //         title: Text(
        //           member.nick,
        //           style: const TextStyle(
        //             color: Colors.black,
        //           ),
        //         ),
        //         subtitle: Text(
        //           member.affiliation,
        //           style: const TextStyle(
        //             color: Colors.black,
        //           ),
        //         ),
        //         // onTap: () {
        //         //   // showAcceptInviteDialog(context);
        //         //   showDialog(
        //         //     context: context,
        //         //     builder: (context) => AlertDialog(
        //         //       backgroundColor: greyBackground,
        //         //       title: const Center(
        //         //         child: Text(
        //         //           'Group Invitation',
        //         //           style: TextStyle(
        //         //             color: Colors.white,
        //         //             fontSize: 17,
        //         //           ),
        //         //         ),
        //         //       ),
        //         //       content: Column(
        //         //         mainAxisSize: MainAxisSize.min,
        //         //         children: [
        //         //           const Text(
        //         //             "Accept/Decline this group invitation",
        //         //             style: TextStyle(
        //         //               color: Colors.white70,
        //         //             ),
        //         //           ),
        //         //           Text(
        //         //             'from ${invitation.from}',
        //         //             style: const TextStyle(
        //         //               color: Colors.white70,
        //         //             ),
        //         //           ),
        //         //           Text(
        //         //             "Group ${invitation.roomJid}",
        //         //             style: const TextStyle(
        //         //               color: Colors.white70,
        //         //             ),
        //         //           ),
        //         //           const SizedBox(
        //         //             height: 10,
        //         //           ),
        //         //           // const TextField(
        //         //           //   style: TextStyle(
        //         //           //     color: Colors.white,
        //         //           //   ),
        //         //           //   cursorColor: ddOrange,
        //         //           //   // controller: _invitereasonEditingController,
        //         //           //   decoration: InputDecoration(
        //         //           //     hintText: 'Reason to decline',
        //         //           //     hintStyle: TextStyle(
        //         //           //       color: Colors.white54,
        //         //           //     ),
        //         //           //     // filled: true,
        //         //           //     // fillColor: greyBackground,
        //         //           //     enabledBorder: OutlineInputBorder(
        //         //           //       borderSide: BorderSide(
        //         //           //         color: Colors.white70,
        //         //           //       ),
        //         //           //     ),
        //         //           //     focusedBorder: OutlineInputBorder(
        //         //           //       borderSide: BorderSide(
        //         //           //         color: Colors.white70,
        //         //           //       ),
        //         //           //     ),
        //         //           //   ),
        //         //           // ),
        //         //           const SizedBox(
        //         //             height: 10,
        //         //           ),
        //         //           Row(
        //         //             mainAxisAlignment: MainAxisAlignment.center,
        //         //             children: [
        //         //               TextButton(
        //         //                 onPressed: () {
        //         //                   Navigator.pop(context);
        //         //                 },
        //         //                 child: const Text(
        //         //                   "Cancel",
        //         //                   style: TextStyle(
        //         //                     color: Colors.red,
        //         //                   ),
        //         //                 ),
        //         //               ),
        //         //               BlocBuilder<GroupUiCubit, GroupUiState>(
        //         //                 builder: (context, state) {
        //         //                   return TextButton(
        //         //                     onPressed: () async {
        //         //                       joinChatRoom();
        //         //                       context.read<GroupUiCubit>().update();
        //         //                       // inviteUser();
        //         //                       pop();
        //         //                     },
        //         //                     child: const Text(
        //         //                       "Accept",
        //         //                       style: TextStyle(
        //         //                         color: Colors.green,
        //         //                       ),
        //         //                     ),
        //         //                   );
        //         //                 },
        //         //               ),
        //         //             ],
        //         //           ),
        //         //         ],
        //         //       ),
        //         //     ),
        //         //   );
        //         //   // Handle invitation tap
        //         // },
        //       ),
        //     );
        //   }).toList();
        // }).toList(),
      );
    }
  }
}

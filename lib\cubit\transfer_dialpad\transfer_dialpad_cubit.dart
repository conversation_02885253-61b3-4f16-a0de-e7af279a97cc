import 'package:bloc/bloc.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/models/contact_model.dart';
import 'package:ddone/service_locator.dart';
import 'package:equatable/equatable.dart';

part 'transfer_dialpad_state.dart';

class TransferDialpadCubit extends Cubit<TransferDialpadState> {
  TransferDialpadCubit._({TransferDialpadState? state}) : super(state ?? TransferDialpadInitial());

  factory TransferDialpadCubit.initial({TransferDialpadState? state}) {
    if (sl.isRegistered<TransferDialpadCubit>()) {
      return sl.get<TransferDialpadCubit>();
    }

    return TransferDialpadCubit._(state: state);
  }

  void getContacts(ContactsCubit contactsCubit) {
    final contactState = contactsCubit.state;

    emit(
      TransferDialpadLoaded(
        contactModelList: contactState.contactModelList,
        filteredContactModelList: contactState.contactModelList,
      ),
    );
  }

  void filterContactList(String value) {
    List<ContactModel> tempContactModelList = [];

    tempContactModelList = state.contactModelList
        .where((element) => element.displayName.toLowerCase().contains(value.toLowerCase()))
        .toList();

    emit(
      TransferDialpadLoaded(
        contactModelList: state.contactModelList,
        filteredContactModelList: value.isEmpty ? state.contactModelList : tempContactModelList,
      ),
    );
  }
}

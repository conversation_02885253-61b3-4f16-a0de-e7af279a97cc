import 'package:bloc/bloc.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/delete_bookmark/delete_bookmark_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'chat_category_state.dart';

class ChatCategoryCubit extends Cubit<ChatCategoryState> {
  final SharedPreferences _sharedPreferences;
  ChatCategoryCubit._()
      : _sharedPreferences = sl.get<SharedPreferences>(),
        super(ChatCategoryInitial());

  factory ChatCategoryCubit.initial() {
    return ChatCategoryCubit._();
  }

  Future<void> removeRoster(
    String jid,
    LoginCubit loginCubit,
  ) async {
    if (loginCubit.state is LoginAuthenticated) {
      LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;
      final presenceSubscribe = currentLoginState.connection.getManagerById<PresenceManager>(presenceManager)!;

      // await presenceSubscribe.requestSubscription(JID.fromString(jid));
      await presenceSubscribe.unsubscribe(JID.fromString(jid));
      final addRosterTo = currentLoginState.connection.getManagerById<RosterManager>(rosterManager)!;
      // final jid = _contactEditingController.text;
      // await addRosterTo.addToRoster(jid, title);
      await addRosterTo.removeFromRoster(jid);
    }
  }

  Future<void> removeGroup(
    String jid,
    String nick,
    LoginCubit loginCubit,
    DeleteBookmarkCubit deleteBookmarkCubit,
    GroupUiCubit groupUiCubit,
    BookmarkListCubit bookmarkListCubit,
  ) async {
    try {
      if (loginCubit.state is LoginAuthenticated) {
        LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;

        String? sipNumber = _sharedPreferences.getString(CacheKeys.sipNumber);
        String? sipDomain = _sharedPreferences.getString(CacheKeys.sipDomain);
        var userAtDomain = "${sipNumber!}@${sipDomain!}";

        final mucName = JID.fromString(jid);
        final nickName = JID.fromString(nick);
        final userAccount = JID.fromString(userAtDomain);
        // final reason = _destroyreasonEditingController.text;

        final muc = currentLoginState.connection.getManagerById<MUCManager>(mucManager)!;

        if (mucName.toString().isNotEmpty && nickName.toString().isNotEmpty) {
          await muc.removeRoom(
            mucName,
            userAccount,
            nickName.toString(),
            '',
          );

          await deleteBookmarkCubit.deleteBookmark(
            loginCubit: loginCubit,
            groupUiCubit: groupUiCubit,
            bookmarkListCubit: bookmarkListCubit,
            bookmarkName: jid,
          );
        }
      }
    } catch (e) {
      debugPrint('Error removing room: $e');
    }
  }
}

import 'package:flutter/material.dart';

// Heights
const double heightTiny = 8.0;
const double heightExtraSmall = 12.0;
const double heightSmall = 24.0;
const double heightMedium = 48.0;
const double heightLarge = 72.0;
const double heightExtraLarge = 96.0;
const double heightXXLarge = 120.0;
const double heightXXXLarge = 144.0;
const double heightXXXXLarge = 168.0;
const double height5XLarge = 192.0;
const double height6XLarge = 216.0;
const double height7XLarge = 240.0;
const double height8XLarge = 264.0;
const double height9XLarge = 288.0;
const double height10XLarge = 312.0;

// Widths
const double widthTiny = 8.0;
const double widthExtraSmall = 12.0;
const double widthSmall = 24.0;
const double widthMedium = 48.0;
const double widthLarge = 72.0;
const double widthExtraLarge = 96.0;
const double widthXXLarge = 120.0;
const double widthXXXLarge = 144.0;
const double widthXXXXLarge = 168.0;
const double width5XLarge = 192.0;
const double width6XLarge = 216.0;
const double width7XLarge = 240.0;
const double width8XLarge = 264.0;
const double width9XLarge = 288.0;
const double width10XLarge = 312.0;
const double dialpadFieldWidth = 300;

// Screen size related
const double maxWidthForDesktop = 700;
const double maxWidthFor4k = 1500;

// Opacities
const double opacityExtraExtraLow = 0.05;
const double opacityExtraLow = 0.1;
const double opacityLow = 0.25;
const double opacityMedium = 0.5;
const double opacityHigh = 0.75;
const double opacityExtraHigh = 0.9;
const double opacityFull = 1.0;

// Radii
const double radiusTiny = 1.0;
const double radiusExtraSmall = 2.0;
const double radiusSmall = 4.0;
const double radiusMedium = 8.0;
const double radiusLarge = 16.0;
const double radiusExtraLarge = 32.0;
const double radiusXLarge = 50.0;
const double radiusXXLarge = 64.0;
const double radiusXXXLarge = 128.0;
const double roundShapeRadius = 999;

// Padding
const EdgeInsets paddingTiny = EdgeInsets.all(2.0);
const EdgeInsets paddingExtraSmall = EdgeInsets.all(4.0);
const EdgeInsets paddingSmall = EdgeInsets.all(8.0);
const EdgeInsets paddingMedium = EdgeInsets.all(16.0);
const EdgeInsets paddingLarge = EdgeInsets.all(24.0);
const EdgeInsets paddingExtraLarge = EdgeInsets.all(32.0);
const EdgeInsets paddingXXLarge = EdgeInsets.all(40.0);
const EdgeInsets paddingXXXLarge = EdgeInsets.all(48.0);
const EdgeInsets listTileTitlePadding = EdgeInsets.fromLTRB(spacingExtraLarge, 10, 0, 5);

// Margin
const EdgeInsets marginTiny = EdgeInsets.all(2.0);
const EdgeInsets marginExtraSmall = EdgeInsets.all(4.0);
const EdgeInsets marginSmall = EdgeInsets.all(8.0);
const EdgeInsets marginMedium = EdgeInsets.all(16.0);
const EdgeInsets marginLarge = EdgeInsets.all(24.0);
const EdgeInsets marginExtraLarge = EdgeInsets.all(32.0);
const EdgeInsets marginXXLarge = EdgeInsets.all(40.0);
const EdgeInsets marginXXXLarge = EdgeInsets.all(48.0);

// Border Widths
const double borderWidthUltraThin = 0.5;
const double borderWidthThin = 1.0;
const double borderWidthMedium = 2.0;
const double borderWidthThick = 4.0;
const double borderWidthExtraThick = 8.0;
const double borderWidthUltraThick = 16.0;

// Font Sizes
const double displayLargeFontSize = 34.0;
const double displayMediumFontSize = 24.0;
const double displaySmallFontSize = 20.0;
const double headlineLargeFontSize = 32.0;
const double headlineMediumFontSize = 28.0;
const double headlineSmallFontSize = 24.0;
const double titleLargeFontSize = 22.0;
const double titleMediumFontSize = 20.0;
const double titleSmallFontSize = 18.0;
const double bodyLargeFontSize = 16.0;
const double bodyMediumFontSize = 14.0;
const double bodySmallFontSize = 12.0;
const double labelLargeFontSize = 14.0;
const double labelMediumFontSize = 12.0;
const double labelSmallFontSize = 10.0;

// Icon Sizes
const double iconSizeTiny = 8.0;
const double iconSizeExtraSmall = 12.0;
const double iconSizeSmall = 16.0;
const double iconSizeMedium = 24.0;
const double iconSizeLarge = 32.0;
const double iconSizeExtraLarge = 48.0;
const double iconSizeXXLarge = 64.0;
const double iconSizeXXXLarge = 80.0;

// Elevation
const double elevationNone = 0.0;
const double elevationLow = 2.0;
const double elevationMedium = 4.0;
const double elevationHigh = 8.0;
const double elevationExtraHigh = 16.0;
const double elevationUltraHigh = 32.0;

// Spacing
const double spacingTiny = 2.0;
const double spacingExtraSmall = 4.0;
const double spacingSmall = 8.0;
const double spacingMedium = 16.0;
const double spacingLarge = 24.0;
const double spacingExtraLarge = 32.0;
const double spacingXXLarge = 40.0;
const double spacingXXXLarge = 48.0;

// Loading Animation
const double prograssiveDotSize = 30.0;
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class EasyLoadingService {
  Future<void> initEasyLoadingService() async {
    EasyLoading.instance
      ..displayDuration = const Duration(milliseconds: 2000)
      ..indicatorType = EasyLoadingIndicatorType.cubeGrid
      ..loadingStyle = EasyLoadingStyle.light
      ..textColor = Colors.orange
      ..backgroundColor = Colors.white
      ..indicatorColor = Colors.orange
      ..maskColor = Colors.orange
      ..userInteractions = false;
  }

  void showLoadingWithText(String text) {
    if (!isNowLoading()) EasyLoading.show(status: text);
  }

  void showInfoWithText(String text, {Duration? duration}) {
    EasyLoading.showInfo(
      text,
      duration: duration ?? const Duration(seconds: 2),
    );
  }

  void showSuccessWithText(String text, {Duration? duration}) {
    EasyLoading.showSuccess(
      text,
      duration: duration ?? const Duration(seconds: 2),
    );
  }

  void showErrorWithText(String text, {Duration? duration}) {
    EasyLoading.showError(
      text,
      duration: duration ?? const Duration(seconds: 2),
    );
  }

  void dismissLoading() {
    if (isNowLoading()) EasyLoading.dismiss();
  }

  bool isNowLoading() {
    return EasyLoading.isShow;
  }
}

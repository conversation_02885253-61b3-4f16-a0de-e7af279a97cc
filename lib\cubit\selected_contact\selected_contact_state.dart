part of 'selected_contact_cubit.dart';

abstract class SelectedContactState extends Equatable {
  final String profileImageUrl;
  final String displayName;
  final String contactId;
  final String sipAddress;
  final bool isLocalContact;
  final bool isPhoneBookContact;
  final bool isFavouriteContact;
  final bool isContainerVisible;

  const SelectedContactState({
    this.profileImageUrl = '',
    this.displayName = '',
    this.contactId = '',
    this.sipAddress = '',
    this.isLocalContact = false,
    this.isPhoneBookContact = false,
    this.isFavouriteContact = false,
    this.isContainerVisible = false,
  });

  @override
  List<Object> get props => [
        profileImageUrl,
        displayName,
        contactId,
        sipAddress,
        isLocalContact,
        isPhoneBookContact,
        isFavouriteContact,
        isContainerVisible,
      ];
}

class SelectedContactInitial extends SelectedContactState {
  const SelectedContactInitial();
}

class SelectedContactLoaded extends SelectedContactState {
  const SelectedContactLoaded({
    super.profileImageUrl,
    required super.displayName,
    required super.contactId,
    required super.sipAddress,
    required super.isLocalContact,
    required super.isPhoneBookContact,
    required super.isFavouriteContact,
    required super.isContainerVisible,
  });
}

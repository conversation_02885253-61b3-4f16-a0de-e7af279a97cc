part of 'transfer_dialpad_cubit.dart';

abstract class TransferDialpadState extends Equatable {
  final List<ContactModel> contactModelList;
  final List<ContactModel> filteredContactModelList;

  const TransferDialpadState({
    this.contactModelList = const [],
    this.filteredContactModelList = const [],
  });

  @override
  List<Object> get props => [
        contactModelList,
        filteredContactModelList,
      ];
}

class TransferDialpadInitial extends TransferDialpadState {}

class TransferDialpadLoaded extends TransferDialpadState {
  const TransferDialpadLoaded({
    required List<ContactModel> contactModelList,
    required List<ContactModel> filteredContactModelList,
  }) : super(
          contactModelList: contactModelList,
          filteredContactModelList: filteredContactModelList,
        );
}

import 'package:device_info_plus/device_info_plus.dart';
import 'package:ddone/models/enums/platform_enum.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/multiplatform/multiplatform.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

bool get isAndroid {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return PlatformEnum.android == platform;
}

bool get isIOS {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return PlatformEnum.ios == platform;
}

bool get isMobile {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return {
    PlatformEnum.android,
    PlatformEnum.ios,
  }.contains(platform);
}

bool get isMacOS {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return PlatformEnum.macos == platform;
}

bool get isWindows {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return PlatformEnum.windows == platform;
}

bool get isDesktop {
  final PlatformEnum platform = sl.isRegistered<PlatformEnum>() ? sl.get<PlatformEnum>() : getPlatform();
  return {
    PlatformEnum.web,
    PlatformEnum.macos,
    PlatformEnum.windows,
    PlatformEnum.linux,
  }.contains(platform);
}

/// Detects if the current device is an iPad
Future<bool> get isIpad async {
  if (!isIOS) return false;

  try {
    final deviceInfo = DeviceInfoPlugin();
    final iosInfo = await deviceInfo.iosInfo;

    // Check if device model contains 'iPad'
    return iosInfo.model.toLowerCase().contains('ipad');
  } catch (e) {
    return false;
  }
}

/// Detects if the current device is an Android tablet
bool isAndroidTablet(BuildContext context) {
  if (!isAndroid) return false;

  try {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;

    // Calculate smallest width (sw) - minimum of width/height regardless of orientation
    final smallestWidth = math.min(screenWidth, screenHeight);

    // Android's official tablet threshold is sw600dp
    final isTabletBySmallestWidth = smallestWidth >= 600;

    // Additional check: aspect ratio shouldn't be too narrow (phones can be wide)
    final aspectRatio = math.max(screenWidth, screenHeight) / math.min(screenWidth, screenHeight);
    final hasTabletAspectRatio = aspectRatio <= 2.0; // Tablets typically < 2:1

    return isTabletBySmallestWidth && hasTabletAspectRatio;
  } catch (e) {
    // conservative fallback - only return true if we're confident
    return false;
  }
}

/// Detects if the current device is a tablet (iPad or Android tablet)
Future<bool> isTablet(BuildContext context) async {
  // For iOS, check if it's an iPad
  if (isIOS) {
    return await isIpad;
  }

  // For Android, check if it's a tablet using screen metrics
  if (isAndroid) {
    return isAndroidTablet(context);
  }

  // For other platforms, not considered tablets
  return false;
}

import 'package:ddone/constants/hive_id_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';

enum CallType {
  answered,
  missed,
  outgoing,
}

class CallTypeAdapter extends TypeAdapter<CallType> {
  @override
  final int typeId = callTypeId; // Ensure this is unique for each adapter

  @override
  CallType read(BinaryReader reader) {
    return CallType.values[reader.readInt()];
  }

  @override
  void write(BinaryWriter writer, CallType obj) {
    writer.writeInt(obj.index);
  }
}

extension CallTypeExtension on CallType {
  String callTypeStatus() {
    switch (this) {
      case CallType.answered:
        return 'answered';
      case CallType.missed:
        return 'missed';
      default:
        return 'outgoing';
    }
  }

  String statusIcon() {
    switch (this) {
      case CallType.answered:
        return 'call_received';
      case CallType.missed:
        return 'call_missed';
      default:
        return 'call_made';
    }
  }

  IconData callTypeIcon() {
    switch (this) {
      case CallType.answered:
        return Icons.call_received;
      case CallType.outgoing:
        return Icons.call_made;
      default:
        return Icons.call_missed;
    }
  }

  Color callTypeIconColor(ColorTheme colorTheme) {
    switch (this) {
      case CallType.answered:
        return colorTheme.connectedColor;
      case CallType.outgoing:
        return colorTheme.primaryColor;
      default:
        return colorTheme.errorColor;
    }
  }
}

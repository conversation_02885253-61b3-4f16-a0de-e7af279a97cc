import 'package:bloc/bloc.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/xmpp_service.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'info_state.dart';

class InfoCubit extends Cubit<InfoState> {
  SharedPreferences sharePref;

  InfoCubit._()
      : sharePref = sl.get<SharedPreferences>(),
        super(InfoInitial());

  factory InfoCubit.initial() {
    return InfoCubit._();
  }

  void getChatHistory({
    required String receiver,
    required LoginCubit loginCubit,
    required MamListCubit mamListCubit,
    // bool? isContainerVisible,
    // Function? toggleContainer,
  }) async {
    emit(InfoLoading(receiver: receiver, nick: ''));

    await Future.delayed(const Duration(milliseconds: 500));

    if (loginCubit.state is LoginAuthenticated) {
      final loginState = loginCubit.state as LoginAuthenticated;

      final sipNumber = sharePref.getString(CacheKeys.sipNumber) ?? '';
      final sipDomain = sharePref.getString(CacheKeys.sipDomain) ?? '';

      // MamListCubit.stanza = {};

      MamListCubit.stanza.forEach((key, value) {
        if (key == receiver) {
          value.clear();
        }
      });

      mamListCubit.clearMsg(receiver);

      print('MamListCubit.stanza123213 ${MamListCubit.stanza}');
      // print("MamListCubit.stanzaMamListCubit.stanza: ${MamListCubit.stanza}");      // mamListCubit.clearMsg();

      await loginState.connection.getManagerById<XmppService>(mamManager)!.requestMsg(
            JID.fromString('$sipNumber@$sipDomain'),
            requestJid: JID.fromString(receiver),
            // endDate: DateTime.now().subtract(const Duration(days: 10)).toUtc(),
            pageSize: 50,
          );

      mamListCubit.getMamList();

      emit(InfoLoaded(receiver: receiver, nick: ''));
    }
  }

  void getLoadChatHistory({
    required String receiver,
    required LoginCubit loginCubit,
    required MamListCubit mamListCubit,
    required String beforeId,
  }) async {
    // emit(InfoLoading(receiver: receiver, nick: ''));

    if (loginCubit.state is LoginAuthenticated) {
      final loginState = loginCubit.state as LoginAuthenticated;

      final sipNumber = sharePref.getString(CacheKeys.sipNumber) ?? '';
      final sipDomain = sharePref.getString(CacheKeys.sipDomain) ?? '';

      MamListCubit.stanza.clear();

      await loginState.connection.getManagerById<XmppService>(mamManager)!.requestMoreMsg(
            JID.fromString('$sipNumber@$sipDomain'),
            requestJid: JID.fromString(receiver),
            beforeId: beforeId,
            // endDate: DateTime.now().subtract(const Duration(days: 10)).toUtc(),
            pageSize: 50,
          );

      mamListCubit.getMamList(isLazyLoad: true);

      emit(InfoLoaded(receiver: receiver, nick: ''));
    }
  }

  void getGroupChatHistory({
    required String receiver,
    required String nick,
    required String name,
    required LoginCubit loginCubit,
    required MamListCubit mamListCubit,
  }) async {
    emit(InfoLoading(receiver: receiver, nick: nick, name: name));

    await Future.delayed(const Duration(milliseconds: 500));

    if (loginCubit.state is LoginAuthenticated) {
      final loginState = loginCubit.state as LoginAuthenticated;
      final muc = loginState.connection.getManagerById<MUCManager>(mucManager)!;

      final sipNumber = sharePref.getString(CacheKeys.sipNumber) ?? '';
      final sipDomain = sharePref.getString(CacheKeys.sipDomain) ?? '';

      final sipAccount = '$sipNumber@$sipDomain';

      MamListCubit.test.forEach((key, value) {
        if (key == receiver) {
          value.clear();
        }
      });

      mamListCubit.clearGroupMsg(receiver);

      await loginState.connection.getManagerById<XmppService>(mamManager)!.requestGroupMsg(
            JID.fromString(receiver),
            50,
          );
      await muc.configure(
        JID.fromString(receiver),
        nick,
        JID.fromString(sipAccount),
      );
      await muc.getRoomNick(
        JID.fromString(receiver),
        sipAccount,
      );
      // await muc.getOwnerList(
      //   JID.fromString(receiver),
      //   sipAccount,
      // );
      // await muc.getPartList(
      //   JID.fromString(receiver),
      //   sipAccount,
      // );

      mamListCubit.getMamList();
      // memberCubit.getMemberList();

      emit(InfoLoaded(receiver: receiver, nick: nick, name: name));
    }
  }

  void getLoadGroupChatHistory({
    required String receiver,
    required String nick,
    required String name,
    required LoginCubit loginCubit,
    required MamListCubit mamListCubit,
    // required MemberCubit memberCubit,
    required String beforeId,
    // bool? isContainerVisible,
    // Function? toggleContainer,
  }) async {
    if (loginCubit.state is LoginAuthenticated) {
      final loginState = loginCubit.state as LoginAuthenticated;
      final muc = loginState.connection.getManagerById<MUCManager>(mucManager)!;

      final sipNumber = sharePref.getString(CacheKeys.sipNumber) ?? '';
      final sipDomain = sharePref.getString(CacheKeys.sipDomain) ?? '';

      final sipAccount = '$sipNumber@$sipDomain';

      await loginState.connection.getManagerById<XmppService>(mamManager)!.requestMoreGroupMsg(
            JID.fromString(receiver),
            50,
            beforeId,
          );
      await muc.configure(
        JID.fromString(receiver),
        nick,
        JID.fromString(sipAccount),
      );
      // await muc.getOwnerList(
      //   JID.fromString(receiver),
      //   sipAccount,
      // );
      // await muc.getPartList(
      //   JID.fromString(receiver),
      //   sipAccount,
      // );

      mamListCubit.getMamList(isLazyLoad: true);
      // memberCubit.getMemberList();

      emit(InfoLoaded(receiver: receiver, nick: nick, name: name));
    }
  }

  void removeInfo() {
    emit(
      InfoInitial(),
    );
  }

  void triggerBookmarkList() {
    emit(
      InfoLoaded(
        receiver: state.receiver,
        nick: state.nick,
      ),
    );
  }
}

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:windows_notification/notification_message.dart';

//Category Identifier
const callActions = 'call_actions';

//Action Identifier
const notificationAcceptCallAction = 'accept_action';
const notificationDeclineCallAction = 'decline_action';

NotificationMessage windowsNotificationMessage({
  String id = '',
  required String group,
  required String launch,
  required Map<String, dynamic> payload,
}) {
  // return NotificationMessage.fromCustomTemplate(
  //   "notificationid_1",
  //   group: "incoming_call_group",
  //   launch: 'callLaunchArg',
  //   payload: {'type': 'incoming_call'},
  // );
  return NotificationMessage.fromCustomTemplate(
    id,
    group: group,
    launch: launch,
    payload: payload,
  );
}

String singleButtonsNotificationTemplateForWindows({
  required String title,
  required String desc,
  required String buttonText,
  required String buttonArg,
  String scenario = 'scenario',
  String launch = 'launch',
}) {
  String template = '''
<?xml version="1.0" encoding="utf-8"?>
<toast launch="$launch" scenario="$scenario" activationType="protocol">
  <visual>
    <binding template="ToastGeneric">
      <text>$title</text>
      <text>$desc</text>
    </binding>
  </visual>
  <actions>
    <action content="$buttonText" imageUri="" activationType="protocol" arguments="action:$buttonArg" />
  </actions>
</toast>
''';

  return template;
}

String twoButtonsNotificationTemplateForWindows({
  required String title,
  required String desc,
  required String leftButtonText,
  required String rightButtonText,
  required String leftButtonArg,
  required String rightButtonArg,
  String scenario = 'scenario',
  String launch = 'launch',
}) {
  String template = '''
<?xml version="1.0" encoding="utf-8"?>
<toast launch="$launch" scenario="$scenario" activationType="protocol">
  <visual>
    <binding template="ToastGeneric">
      <text>$title</text>
      <text>$desc</text>
    </binding>
  </visual>
  <actions>
    <action content="$leftButtonText" imageUri="" activationType="protocol" arguments="action:$leftButtonArg" />
    <action content="$rightButtonText" imageUri="" activationType="protocol" arguments="action:$rightButtonArg" />
  </actions>
</toast>
''';

  return template;
}

const List<AndroidNotificationAction> androidIncomingCallTemplate = [
  AndroidNotificationAction(
    notificationAcceptCallAction, // Action key for "Accept"
    'Accept', // Button label
  ),
  AndroidNotificationAction(
    notificationDeclineCallAction, // Action key for "Decline"
    'Decline', // Button label
  ),
];

const DarwinNotificationDetails iOSMacosIncomingCallTemplete = DarwinNotificationDetails(
  categoryIdentifier: callActions, // Referencing the category with actions
);

List<DarwinNotificationCategory> iosMacosNotificationTemplate = [
  DarwinNotificationCategory(
    callActions, // Category ID
    actions: <DarwinNotificationAction>[
      DarwinNotificationAction.plain(
        notificationAcceptCallAction,
        'Accept',
        options: <DarwinNotificationActionOption>{DarwinNotificationActionOption.foreground},
      ),
      DarwinNotificationAction.plain(
        notificationDeclineCallAction, // Action identifier
        'Decline', // Button label
        options: <DarwinNotificationActionOption>{DarwinNotificationActionOption.destructive},
      ),
    ],
  ),
];

import 'package:bloc/bloc.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/constants/regex_constants.dart';
import 'package:ddone/mixins/api_handler.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/models/contact_entry.dart';
import 'package:ddone/models/contact_model.dart';
import 'package:ddone/models/hive/favourite_contact.dart';
import 'package:ddone/models/hive/local_contact.dart';
import 'package:ddone/repositories/contacts_repository.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:ddone/utils/permission_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:moxxmpp/model/conference_info.dart';

part 'contacts_state.dart';

class ContactsCubit extends Cubit<ContactsState> with ApiHandler, PrefsAware {
  final Dio dio;
  final HiveService hiveService;
  late ContactRepository contactRepository;

  ContactsCubit._({
    ContactsState? state,
  })  : dio = sl.get<Dio>(),
        hiveService = sl.get<HiveService>(),
        super(state ?? const ContactsInitial());

  factory ContactsCubit.initial({ContactsState? state}) {
    //the case of is register only happen when you mock the cubit in test case
    if (sl.isRegistered<ContactsCubit>()) {
      return sl.get<ContactsCubit>();
    }
    return ContactsCubit._(state: state);
  }

  void init(String sipContactUrlBase) async {
    contactRepository = ContactRepository(dio, baseUrl: sipContactUrlBase);
  }

  /*Having this function here instead of call it in initial is because we dont 
  have splash screen to wait hivebox initial and contacts screen is our initial screen*/
  void initHiveBoxListener() {
    hiveService.returnBox<LocalContact>().listenable().addListener(_onLocalContactBoxChanged);
    hiveService.returnBox<FavouriteContact>().listenable().addListener(_onFavouriteContactBoxChanged);
  }

  void _onLocalContactBoxChanged() {
    getContactList();
  }

  void _onFavouriteContactBoxChanged() {
    emit(
      ContactsLoaded(
        favouriteContactModelList: hiveService.getFavouriteContactList(),
        contactModelList: state.contactModelList,
        filteredContactModelList: state.contactModelList,
        bookmarkGroupList: state.bookmarkGroupList,
        filterBookmarkGroupList: state.filterBookmarkGroupList,
      ),
    );
  }

  Future<void> getContactList() async {
    // Skip not logged in user
    if (!userHasLoggedIn()) {
      return;
    }

    // check what 'domain' query param should use.
    // - use contact_url domain query param if available, otherwise use back domain.
    final String? sipNumber = prefs.getString(CacheKeys.sipNumber);
    String? contactDomain = prefs.getString(CacheKeys.sipDomain);
    final String? sipContactUrlParams = prefs.getString(CacheKeys.sipContactUrlParams);
    if (sipContactUrlParams != null && sipContactUrlParams.isNotEmpty) {
      String? queryParamDomain = Uri.parse('?$sipContactUrlParams').queryParameters['domain'];
      if (queryParamDomain != null) {
        contactDomain = queryParamDomain;
      }
    }

    await apiErrorHandler(() async {
      List<ContactModel> contactModelList = [];

      // contact from fusion api
      final response = await contactRepository.getContactList(contactDomain!, sipNumber!, '');
      dynamic contactList = response['contacts'];
      for (int i = contactList.length - 1; i >= 0; i--) {
        if (contactIdRegex.hasMatch(contactList[i]['contactId'].toString())) {
          contactList.removeAt(i);
        } else {
          contactModelList.add(ContactModel.fromJson(contactList[i]));
        }
      }

      // contact in device phone book
      if (!isDesktop) {
        final phoneBookContacts = await _getPhoneBookContacts();
        contactModelList.addAll(phoneBookContacts);
      }

      // DDOne locally created contact
      final localContactBox = hiveService.returnBox<LocalContact>();
      if (localContactBox.length != 0) {
        for (int i = 0; i < localContactBox.length; i++) {
          var localContact = localContactBox.getAt(i);
          log.d('localContactlocalContact: $i ${localContact?.contactNumber}');
          contactModelList.add(
            ContactModel(
              contactEntries: const [
                ContactEntry(
                  entryId: '-',
                  label: '-',
                  type: '-',
                  uri: '-',
                )
              ],
              contactId: localContact?.contactNumber ?? '',
              displayName: localContact?.displayName ?? '',
              isLocalContact: true,
            ),
          );
        }
      }

      // sort by display name
      contactModelList.sort((a, b) => a.displayName.toLowerCase().compareTo(b.displayName.toLowerCase()));

      emit(
        ContactsLoaded(
            favouriteContactModelList: hiveService.getFavouriteContactList(),
            contactModelList: contactModelList,
            filteredContactModelList: contactModelList,
            bookmarkGroupList: state.bookmarkGroupList,
            filterBookmarkGroupList: state.filterBookmarkGroupList),
      );
    }).onError((error, stackTrace) {
      emit(
        const ContactsError(),
      );
    });
  }

  void setBookmarkGroupList({required BookmarkListCubit bookmarkListCubit}) {
    final bookmarkState = bookmarkListCubit.state;

    if (bookmarkState is BookmarkListUpdated) {
      emit(
        ContactsLoaded(
          favouriteContactModelList: hiveService.getFavouriteContactList(),
          contactModelList: state.contactModelList,
          filteredContactModelList: state.contactModelList,
          bookmarkGroupList: bookmarkState.bookmarkList,
          filterBookmarkGroupList: bookmarkState.bookmarkList,
        ),
      );
      // print("bookmarkListCubitbookmarkListCubit ${bookmarkState.bookmarkList}");
    }
  }

  void filterContactList(String value) {
    List<ContactModel> tempContactModelList = [];
    List<ContactModel> tempFavouriteContactModelList = [];

    tempContactModelList = state.contactModelList
        .where((element) =>
            element.displayName.toLowerCase().contains(value.toLowerCase()) ||
            element.contactEntries[0].uri.toLowerCase().contains(value.toLowerCase()))
        .toList();

    tempFavouriteContactModelList = hiveService
        .getFavouriteContactList()
        .where((element) => element.displayName.toLowerCase().contains(value.toLowerCase()))
        .toList();

    emit(
      ContactsLoaded(
        favouriteContactModelList:
            value.isEmpty ? hiveService.getFavouriteContactList() : tempFavouriteContactModelList,
        contactModelList: state.contactModelList,
        filteredContactModelList: value.isEmpty ? state.contactModelList : tempContactModelList,
        bookmarkGroupList: state.bookmarkGroupList,
        filterBookmarkGroupList: state.filterBookmarkGroupList,
      ),
    );
  }

  void filterBookmarkList(String value) {
    List<ConferenceInfo> tempBookmarkList = [];

    tempBookmarkList =
        state.bookmarkGroupList.where((element) => element.name.toLowerCase().contains(value.toLowerCase())).toList();

    emit(
      ContactsLoaded(
        favouriteContactModelList: hiveService.getFavouriteContactList(),
        contactModelList: state.contactModelList,
        filteredContactModelList: state.contactModelList,
        bookmarkGroupList: state.bookmarkGroupList,
        filterBookmarkGroupList: tempBookmarkList,
      ),
    );

    // print('tempBookmarkListtempBookmarkList ${state.bookmarkGroupList}');
    // print('tempBookmarkListtempBookmarkList $tempBookmarkList');
  }

  void clearContacts({required BookmarkListCubit bookmarkListCubit}) {
    bookmarkListCubit.emit(const BookmarkListUpdated([]));
    emit(const ContactsInitial(
      favouriteContactModelList: [],
      contactModelList: [],
      filteredContactModelList: [],
      bookmarkGroupList: [],
      filterBookmarkGroupList: [],
    ));
  }

  /// Requests contacts permission using PermissionUtil with FlutterContacts.requestPermission
  Future<bool> requestContactsPermission(BuildContext context) async {
    if (isDesktop || // Desktop doesn't need contacts permission
            !userHasLoggedIn() // no need to request before login
        ) {
      return true;
    }

    return await PermissionUtil.handleCustomBoolPermissionRequest(
      () => FlutterContacts.requestPermission(),
      context,
      title: 'Contacts Access Required',
      message: 'We need access to your contacts to include your phone book contacts in the app.',
      checkFunction: () => FlutterContacts.requestPermission(readonly: true),
    );
  }

  /// Fetches contacts from device phone book and converts them to ContactModel format
  Future<List<ContactModel>> _getPhoneBookContacts() async {
    List<ContactModel> phoneBookContacts = [];

    try {
      // Check if permission is already granted
      bool hasPermission = await FlutterContacts.requestPermission(readonly: true);

      if (!hasPermission) {
        log.w('Contact permission not granted');
        return phoneBookContacts;
      }

      // Fetch all contacts with phone numbers
      final contacts = await FlutterContacts.getContacts(
        withProperties: true,
        withPhoto: false,
      );

      for (final contact in contacts) {
        // Skip contacts without phone numbers
        if (contact.phones.isEmpty) continue;

        // Create a ContactModel for each phone number
        for (final phone in contact.phones) {
          // Clean the phone number (remove spaces, dashes, etc.)
          final cleanedPhoneNumber = _cleanPhoneNumber(phone.number);

          // Skip if phone number is empty after cleaning
          if (cleanedPhoneNumber.isEmpty) continue;

          // Create display name with label for multiple numbers
          String displayName;
          if (contact.displayName.isNotEmpty) {
            if (contact.phones.length > 1) {
              // Multiple numbers: add label (e.g., "LohZJ (work)")
              final label = phone.label.name.toLowerCase();
              displayName = '${contact.displayName} ($label)';
            } else {
              // Single number: use contact name as is
              displayName = contact.displayName;
            }
          } else {
            // No contact name: use phone number
            displayName = cleanedPhoneNumber;
          }

          // Create ContactEntry for this phone number
          final contactEntry = ContactEntry(
            entryId: '${contact.id}_${phone.label.name}',
            label: phone.label.name,
            type: 'phone',
            uri: cleanedPhoneNumber,
          );

          // Create ContactModel
          final contactModel = ContactModel(
            contactEntries: [contactEntry],
            contactId: cleanedPhoneNumber,
            displayName: displayName,
            isLocalContact: true, // Phone book contacts get treated as local contact
          );

          phoneBookContacts.add(contactModel);
        }
      }

      log.t('Fetched ${phoneBookContacts.length} phone numbers from ${contacts.length} contacts');
    } catch (e) {
      log.e('Error fetching phone book contacts: $e');
    }

    return phoneBookContacts;
  }

  /// Cleans phone number by removing non-digit characters and ensuring
  /// only a single '+' character appears at the very beginning if present.
  String _cleanPhoneNumber(String phoneNumber) {
    if (phoneNumber.isEmpty) {
      return '';
    }

    String cleaned = '';
    bool hasLeadingPlus = false;

    // Check if the first character is '+'
    if (phoneNumber.startsWith('+')) {
      hasLeadingPlus = true;
    }

    // Remove all non-digit characters
    // If we had a leading '+', we'll re-add it later
    String digitsOnly = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // If the original number had a leading '+', prepend it to the digits
    if (hasLeadingPlus) {
      cleaned = '+$digitsOnly';
    } else {
      cleaned = digitsOnly;
    }

    // Handle cases like "++123" or "123+456" which after the above steps
    // might become "+123" or "123456" respectively.
    // The goal is to ensure only one leading '+' if present.
    if (cleaned.startsWith('+')) {
      // If it starts with +, ensure there's only one + and then digits
      // Example: "++123" -> "+123", "+1+23" -> "+123" (if leading + was there)
      cleaned = '+${cleaned.replaceAll(RegExp(r'\D'), '')}'; // Remove all non-digits again after the initial '+'
    }

    return cleaned;
  }
}

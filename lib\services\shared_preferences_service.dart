import 'dart:async';

import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// A service that extends the functionality of Flutter's SharedPreferences to
/// support Time-To-Live (TTL) for stored values.
///
/// This class provides methods to store and retrieve various data types with optional
/// expiration times. When a value is retrieved, the service automatically checks if
/// it has expired and returns null if it has. Expired values are automatically removed
/// from storage when accessed.
///
/// The service uses a singleton pattern to ensure a single instance is used throughout the app.
///
/// ## Features:
/// * TTL support for all SharedPreferences value types (String, Int, Double, Bool, StringList)
/// * TTL support for JSON-serializable objects
/// * Automatic expiration checking and cleanup
/// * Methods to manage TTL (check remaining time, refresh, remove)
/// * Get all non-expired stored values
///
/// ## Usage:
/// ```dart
/// // Initialize the service
/// final prefsService = await SharedPreferencesService.getInstance();
///
/// // Store values with TTL
/// await prefsService.setString('username', 'john_doe', ttlInSeconds: 3600); // expires in 1 hour
/// await prefsService.setInt('loginCount', 5, ttlInSeconds: 86400); // expires in 1 day
///
/// // Store an object
/// await prefsService.setObject('user', userObject, ttlInSeconds: 7200);
///
/// // Retrieve values (returns null if expired)
/// final username = prefsService.getString('username');
/// final loginCount = prefsService.getInt('loginCount');
/// final user = prefsService.getObject<User>('user', (json) => User.fromJson(json));
///
/// // Check remaining TTL in seconds
/// final ttl = prefsService.getRemainingTTL('username');
///
/// // Refresh TTL
/// await prefsService.refreshTTL('username', 7200);
///
/// // Remove TTL (make value permanent)
/// await prefsService.removeTTL('username');
///
/// // Check if a key exists and isn't expired
/// if (prefsService.containsKey('username')) {
///   // Use the value
/// }
///
/// // Get all non-expired values
/// final allValues = prefsService.getAll();
///
/// // Remove a key and its TTL
/// await prefsService.remove('username');
/// ```
///
/// TTL information is stored as a separate entry with a special suffix.
/// When a value is accessed, its expiration time is checked, and if it has expired,
/// both the value and its TTL information are automatically removed.
class SharedPreferencesService {
  late SharedPreferences _prefs;
  final String _ttlKeySuffix = '_ttl';

  // Constructor
  SharedPreferencesService._();

  // Singleton instance
  static SharedPreferencesService? _instance;

  // Factory constructor to return the same instance
  static Future<SharedPreferencesService> getInstance() async {
    if (_instance == null) {
      _instance = SharedPreferencesService._();
      await _instance!._init();
    }
    return _instance!;
  }

  // Initialize SharedPreferences
  Future<void> _init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Check if key is expired
  bool _isExpired(String key) {
    final ttlKey = key + _ttlKeySuffix;
    final expiryTime = _prefs.getInt(ttlKey);

    if (expiryTime == null) {
      return false; // No TTL set, not expired
    }

    return DateTime.now().millisecondsSinceEpoch > expiryTime;
  }

  // Remove a key and its TTL
  Future<bool> remove(String key) async {
    final ttlKey = key + _ttlKeySuffix;
    final removeTtl = await _prefs.remove(ttlKey);
    final removeKey = await _prefs.remove(key);
    return removeTtl && removeKey;
  }

  // Clear all preferences
  Future<bool> clear() async {
    return await _prefs.clear();
  }

  // Get all keys
  Set<String> getKeys() {
    return _prefs.getKeys().where((key) => !key.endsWith(_ttlKeySuffix)).toSet();
  }

  // Get keys with prefix
  Set<String> getKeysWithPrefix(String prefix) {
    return getKeys().where((key) => key.startsWith(prefix)).toSet();
  }

  // SETTERS
  // Set a string value with optional TTL in seconds
  Future<bool> setString(String key, String value, {int? ttlInSeconds}) async {
    if (ttlInSeconds != null) {
      final expiryTime = DateTime.now().millisecondsSinceEpoch + (ttlInSeconds * 1000);
      await _prefs.setInt(key + _ttlKeySuffix, expiryTime);
    }
    return await _prefs.setString(key, value);
  }

  // Set an int value with optional TTL in seconds
  Future<bool> setInt(String key, int value, {int? ttlInSeconds}) async {
    if (ttlInSeconds != null) {
      final expiryTime = DateTime.now().millisecondsSinceEpoch + (ttlInSeconds * 1000);
      await _prefs.setInt(key + _ttlKeySuffix, expiryTime);
    }
    return await _prefs.setInt(key, value);
  }

  // Set a double value with optional TTL in seconds
  Future<bool> setDouble(String key, double value, {int? ttlInSeconds}) async {
    if (ttlInSeconds != null) {
      final expiryTime = DateTime.now().millisecondsSinceEpoch + (ttlInSeconds * 1000);
      await _prefs.setInt(key + _ttlKeySuffix, expiryTime);
    }
    return await _prefs.setDouble(key, value);
  }

  // Set a bool value with optional TTL in seconds
  Future<bool> setBool(String key, bool value, {int? ttlInSeconds}) async {
    if (ttlInSeconds != null) {
      final expiryTime = DateTime.now().millisecondsSinceEpoch + (ttlInSeconds * 1000);
      await _prefs.setInt(key + _ttlKeySuffix, expiryTime);
    }
    return await _prefs.setBool(key, value);
  }

  // Set a list of strings with optional TTL in seconds
  Future<bool> setStringList(String key, List<String> value, {int? ttlInSeconds}) async {
    if (ttlInSeconds != null) {
      final expiryTime = DateTime.now().millisecondsSinceEpoch + (ttlInSeconds * 1000);
      await _prefs.setInt(key + _ttlKeySuffix, expiryTime);
    }
    return await _prefs.setStringList(key, value);
  }

  // Set any object that can be JSON encoded with optional TTL in seconds
  Future<bool> setObject(String key, Object value, {int? ttlInSeconds}) async {
    if (ttlInSeconds != null) {
      final expiryTime = DateTime.now().millisecondsSinceEpoch + (ttlInSeconds * 1000);
      await _prefs.setInt(key + _ttlKeySuffix, expiryTime);
    }
    return await _prefs.setString(key, jsonEncode(value));
  }

  // GETTERS
  // Get a string value and check expiration
  String? getString(String key) {
    if (_isExpired(key)) {
      remove(key);
      return null;
    }
    return _prefs.getString(key);
  }

  // Get an int value and check expiration
  int? getInt(String key) {
    if (_isExpired(key)) {
      remove(key);
      return null;
    }
    return _prefs.getInt(key);
  }

  // Get a double value and check expiration
  double? getDouble(String key) {
    if (_isExpired(key)) {
      remove(key);
      return null;
    }
    return _prefs.getDouble(key);
  }

  // Get a bool value and check expiration
  bool? getBool(String key) {
    if (_isExpired(key)) {
      remove(key);
      return null;
    }
    return _prefs.getBool(key);
  }

  // Get a list of strings and check expiration
  List<String>? getStringList(String key) {
    if (_isExpired(key)) {
      remove(key);
      return null;
    }
    return _prefs.getStringList(key);
  }

  // Get an object that was JSON encoded and check expiration
  T? getObject<T>(String key, T Function(Map<String, dynamic> json) fromJson) {
    if (_isExpired(key)) {
      remove(key);
      return null;
    }

    final jsonString = _prefs.getString(key);
    if (jsonString == null) {
      return null;
    }

    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return fromJson(json);
    } catch (e) {
      return null;
    }
  }

  // Check if a key exists and isn't expired
  bool containsKey(String key) {
    if (!_prefs.containsKey(key)) {
      return false;
    }
    return !_isExpired(key);
  }

  Future<void> reload() async {
    return _prefs.reload();
  }

  // Get remaining TTL in seconds for a key, or null if no TTL or expired
  int? getRemainingTTL(String key) {
    final ttlKey = key + _ttlKeySuffix;
    final expiryTime = _prefs.getInt(ttlKey);

    if (expiryTime == null) {
      return null; // No TTL set
    }

    final now = DateTime.now().millisecondsSinceEpoch;
    if (now > expiryTime) {
      return 0; // Expired
    }

    return (expiryTime - now) ~/ 1000; // Convert milliseconds to seconds
  }

  // Refresh TTL for an existing key
  Future<bool> refreshTTL(String key, int ttlInSeconds) async {
    if (!_prefs.containsKey(key)) {
      return false;
    }

    final expiryTime = DateTime.now().millisecondsSinceEpoch + (ttlInSeconds * 1000);
    return await _prefs.setInt(key + _ttlKeySuffix, expiryTime);
  }

  // Remove TTL for a key, making it permanent
  Future<bool> removeTTL(String key) async {
    final ttlKey = key + _ttlKeySuffix;
    if (!_prefs.containsKey(ttlKey)) {
      return true; // Already has no TTL
    }
    return await _prefs.remove(ttlKey);
  }

  // Get all non-expired key-value pairs
  Map<String, dynamic> getAll() {
    final Map<String, dynamic> result = {};

    final keys = getKeys();
    for (final key in keys) {
      if (!_isExpired(key)) {
        if (_prefs.getString(key) != null) {
          result[key] = _prefs.getString(key);
        } else if (_prefs.getInt(key) != null) {
          result[key] = _prefs.getInt(key);
        } else if (_prefs.getDouble(key) != null) {
          result[key] = _prefs.getDouble(key);
        } else if (_prefs.getBool(key) != null) {
          result[key] = _prefs.getBool(key);
        } else if (_prefs.getStringList(key) != null) {
          result[key] = _prefs.getStringList(key);
        }
      } else {
        // Remove expired keys
        remove(key);
      }
    }

    return result;
  }
}

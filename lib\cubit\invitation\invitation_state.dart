part of 'invitation_cubit.dart';

class InvitationState extends Equatable {
  final List<InvitationModel> invitationList;
  const InvitationState({this.invitationList = const []});

  @override
  List<Object> get props => [invitationList];
}

class InvitationInitial extends InvitationState {
  // const InvitationInitial({required List<InvitationModel> invitations})
  //     : super(invitationList: invitations);
  @override
  List<Object> get props => super.props..addAll([]);
}

class InvitationReceived extends InvitationState {
  const InvitationReceived({required List<InvitationModel> invitations}) : super(invitationList: invitations);

  @override
  List<Object> get props => super.props..addAll([]);
}

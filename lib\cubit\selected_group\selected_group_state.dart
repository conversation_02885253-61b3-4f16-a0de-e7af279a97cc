part of 'selected_group_cubit.dart';

abstract class SelectedGroupState extends Equatable {
  final String groupName;
  final String groupJid;
  final String autoJoin;
  final String nick;

  const SelectedGroupState({
    this.groupName = '',
    this.groupJid = '',
    this.autoJoin = '',
    this.nick = '',
  });

  @override
  List<Object> get props => [
        groupName,
        groupJid,
        autoJoin,
        nick,
      ];
}

class SelectedGroupInitial extends SelectedGroupState {
  const SelectedGroupInitial();
}

class SelectedGroupLoaded extends SelectedGroupState {
  const SelectedGroupLoaded({
    required super.groupName,
    required super.groupJid,
    required super.autoJoin,
    required super.nick,
  });
}

// import 'package:upgrader/upgrader.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:http/http.dart' as http;

class UpgrateVersionService {
  Future<String?> fetchMicrosoftStoreVersion(String storeUrl) async {
    try {
      final response = await http.get(Uri.parse(storeUrl));
      if (response.statusCode == 200) {
        // For now, just return null since we can't parse HTML without the html package
        // This prevents the FormatException from occurring
        log.d('Microsoft Store page fetched successfully, but HTML parsing is disabled');
        return null;
      }
    } catch (e) {
      log.e('Error fetching version: $e');
    }
    return null;
  }
}

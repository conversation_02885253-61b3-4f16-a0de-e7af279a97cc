part of 'contacts_cubit.dart';

abstract class ContactsState extends Equatable {
  final List<ContactModel> favouriteContactModelList;
  final List<ContactModel> contactModelList;
  final List<ContactModel> filteredContactModelList;
  final List<ConferenceInfo> bookmarkGroupList;
  final List<ConferenceInfo> filterBookmarkGroupList;

  const ContactsState({
    this.favouriteContactModelList = const [],
    this.contactModelList = const [],
    this.filteredContactModelList = const [],
    this.bookmarkGroupList = const [],
    this.filterBookmarkGroupList = const [],
  });

  @override
  List<Object?> get props => [
        favouriteContactModelList,
        contactModelList,
        filteredContactModelList,
        bookmarkGroupList,
        filterBookmarkGroupList,
      ];
}

class ContactsInitial extends ContactsState {
  const ContactsInitial({
    super.favouriteContactModelList,
    super.contactModelList,
    super.filteredContactModelList,
    super.bookmarkGroupList,
    super.filterBookmarkGroupList,
  }) : super();

  @override
  List<Object?> get props => super.props..addAll([]);
}

class ContactsLoaded extends ContactsState {
  const ContactsLoaded({
    required super.favouriteContactModelList,
    required super.contactModelList,
    required super.filteredContactModelList,
    required super.bookmarkGroupList,
    required super.filterBookmarkGroupList,
  });

  @override
  List<Object?> get props => super.props..addAll([]);
}

class ContactsError extends ContactsState {
  const ContactsError() : super();

  @override
  List<Object?> get props => super.props..addAll([]);
}

enum AudioDeviceType {
  earpiece,
  speaker,
  bluetoothHeadphones,
  bluetoothSpeaker,
  wiredHeadphones,
  systemDefault,
  none,
  other,
}

extension AudioDeviceTypeExtension on AudioDeviceType {
  String get displayName {
    switch (this) {
      case AudioDeviceType.earpiece:
        return 'Earpiece';
      case AudioDeviceType.speaker:
        return 'Speaker';
      case AudioDeviceType.bluetoothHeadphones:
        return 'Bluetooth Headphones';
      case AudioDeviceType.bluetoothSpeaker:
        return 'Bluetooth Speaker';
      case AudioDeviceType.wiredHeadphones:
        return 'Wired Headphones';
      case AudioDeviceType.systemDefault:
        return 'System Default';
      case AudioDeviceType.none:
        return 'None (Turn off speaker)';
      case AudioDeviceType.other:
        return 'Other';
    }
  }

  int get priority {
    // Higher number = higher priority for default selection
    switch (this) {
      case AudioDeviceType.wiredHeadphones:
        return 6;
      case AudioDeviceType.bluetoothHeadphones:
        return 5;
      case AudioDeviceType.bluetoothSpeaker:
        return 4;
      case AudioDeviceType.other:
        return 3;
      case AudioDeviceType.earpiece:
        return 2;
      case AudioDeviceType.speaker:
        return 1;
      case AudioDeviceType.systemDefault:
        return 0;
      case AudioDeviceType.none:
        return -1; // Lowest priority - should not be auto-selected
    }
  }
}

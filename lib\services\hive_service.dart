import 'package:ddone/constants/regex_constants.dart';
import 'package:ddone/models/contact_entry.dart';
import 'package:ddone/models/contact_model.dart';
import 'package:ddone/models/hive/call_records.dart';
import 'package:ddone/models/hive/favourite_contact.dart';
import 'package:ddone/models/hive/local_contact.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:hive/hive.dart';
import 'package:logger/logger.dart';

final List<String> registeredBoxes = [];

class HiveService {
  final Logger log = sl.get<Logger>();

  HiveService();

  //It is perfectly fine to leave a box open for the runtime of the app. If you need a box again in the future, just leave it open.
  Future<void> _openBoxAndRegisterLazySingleton<T>({required String boxName}) async {
    await Hive.openBox<T>(boxName);

    Box<T> box = Hive.box<T>(boxName);

    if (sl.isRegistered<Box<T>>()) {
      await sl.unregister<Box<T>>();
    }

    sl.registerLazySingleton<Box<T>>(() => box);
  }

  Box<T> returnBox<T>() {
    return sl.get<Box<T>>();
  }

  Future<void> initialHiveBox() async {
    //Below all are initial boxes before user login
    await _openBoxAndRegisterLazySingleton<CallRecords>(boxName: 'defaultCallRecords');
    await _openBoxAndRegisterLazySingleton<LocalContact>(boxName: 'defaultLocalContacts');
    await _openBoxAndRegisterLazySingleton<FavouriteContact>(boxName: 'defaultFavouriteContacts');
  }

  Future<void> registerUniqueHiveServices(String user, String domain) async {
    //Hive box name cannot have '.'
    final userName = '$user@${domain.replaceAll(noSpecialCharacterRegex, '')}';

    //Below all are unique boxes after user login
    await _openBoxAndRegisterLazySingleton<CallRecords>(boxName: '${userName}callRecords');
    await _openBoxAndRegisterLazySingleton<LocalContact>(boxName: '${userName}localContacts');
    await _openBoxAndRegisterLazySingleton<FavouriteContact>(boxName: '${userName}favouriteContacts');
  }

  Future<void> clearAllData() async {
    // Close all open boxes
    await Hive.close();

    // Delete all boxes from disk
    await Hive.deleteFromDisk();
  }

  List<T> getAllData<T>() {
    try {
      final box = sl.get<Box<T>>();

      return box.values.toList();
    } catch (e) {
      log.w('getAllData error: $e');

      EasyLoadingService().showErrorWithText('getAllData error: $e');

      return [];
    }
  }

  T? getDataByIndex<T>({required int index}) {
    try {
      final box = sl.get<Box<T>>();

      return box.getAt(index);
    } catch (e) {
      log.w('getDataByIndex error: $e');

      EasyLoadingService().showErrorWithText('getDataByIndex error: $e');

      return null;
    }
  }

  T? getDataByKey<T>({required String key}) {
    try {
      final box = sl.get<Box<T>>();

      return box.get(key);
    } catch (e) {
      log.w('getDataByKey error: $e');

      EasyLoadingService().showErrorWithText('getDataByKey error: $e');

      return null;
    }
  }

  T? getDataByParameter<T>({required bool Function(T) predicate}) {
    try {
      final box = sl.get<Box<T>>();

      final values = box.values.where((element) => predicate(element));

      return values.isNotEmpty ? values.first : null;
    } catch (e) {
      log.w('getDataByParameter error: $e');

      EasyLoadingService().showErrorWithText('getDataByParameter error: $e');

      return null;
    }
  }

  Future<void> addData<T>({required T data}) async {
    try {
      final box = sl.get<Box<T>>();

      await box.add(data);
    } catch (e) {
      log.w('addData error: $e');

      EasyLoadingService().showErrorWithText('addData error: $e');
    }
  }

  Future<void> addDataByKey<T>({required String key, required T data}) async {
    try {
      final box = sl.get<Box<T>>();

      await box.put(key, data);
    } catch (e) {
      log.w('addDataByKey error: $e');

      EasyLoadingService().showErrorWithText('addDataByKey error: $e');
    }
  }

  Future<void> updateDataByKey<T>({required String key, required T data}) async {
    try {
      final box = sl.get<Box<T>>();

      await box.put(key, data);
    } catch (e) {
      log.w('updateDataByKey error: $e');

      EasyLoadingService().showErrorWithText('updateDataByKey error: $e');
    }
  }

  Future<void> updateDataByIndex<T>({required int index, required T data}) async {
    try {
      final box = sl.get<Box<T>>();
      print('IndexinupdateDataByIndex: $index');
      // int? localIndex;

      // for (int i = 0; i < box.length; i++) {
      //   var localContact = box.getAt(i);

      //   if (localContact is LocalContact) {
      //     print('Checking Hive Index $i -> ${localContact.displayName}, ${localContact.contactNumber}');
      //     print((data as LocalContact).contactNumber);
      //     print(localContact.contactNumber);
      //     if (localContact.contactNumber == (data as LocalContact).contactNumber ||
      //         localContact.displayName == (data as LocalContact).displayName) {
      //       localIndex = i;
      //       print('{{{i}}}: $i');

      //       break;
      //     }
      //   }
      // }
      // if (localIndex == null) {
      //   throw Exception("Contact with ID ${(data as LocalContact).contactNumber} not found in local storage.");
      // }
      print('Box.length: ${box.length}');
      print('Data.String: ${data.toString()}');

      await box.putAt(index, data);
    } catch (e) {
      log.w('updateDataByIndex error: $e');

      EasyLoadingService().showErrorWithText('updateDataByIndex error: $e');
    }
  }

  Future<void> deleteDataByIndex<T>({required int index}) async {
    try {
      final box = sl.get<Box<T>>();

      await box.deleteAt(index);
    } catch (e) {
      log.w('deleteDataByIndex error: $e');

      EasyLoadingService().showErrorWithText('deleteDataByIndex error: $e');
    }
  }

  Future<void> deleteData<T>({required T data}) async {
    try {
      final box = sl.get<Box<T>>();

      await box.delete(data);
    } catch (e) {
      log.w('deleteData error: $e');

      EasyLoadingService().showErrorWithText('deleteData error: $e');
    }
  }

  /// Below are extra functions with specific use

  Future<void> updateAllCallRecordsName({
    required String newName,
    required String did,
  }) async {
    try {
      final box = sl.get<Box<CallRecords>>();

      for (int i = 0; i < box.length; i++) {
        var currentData = box.getAt(i);

        if (currentData != null && currentData.did == did) {
          await box.putAt(
            i,
            CallRecords(
              contactName: newName,
              did: currentData.did,
              duration: currentData.duration,
              type: currentData.type,
              datetime: currentData.datetime,
            ),
          );
        }
      }
    } catch (e) {
      EasyLoadingService().showErrorWithText('updateAllCallRecordsName error: $e');
    }
  }

  List<ContactModel> getFavouriteContactList() {
    List<ContactModel> favouriteContactModelList = [];

    final favouriteContactBox = sl.get<Box<FavouriteContact>>();

    if (favouriteContactBox.length != 0) {
      for (int i = 0; i < favouriteContactBox.length; i++) {
        var favouriteContact = favouriteContactBox.getAt(i);

        favouriteContactModelList.add(
          ContactModel(
            contactEntries: [
              ContactEntry(
                entryId: '-',
                label: '-',
                type: '-',
                uri: favouriteContact?.uri ?? '-',
              )
            ],
            contactId: favouriteContact?.contactNumber ?? '',
            displayName: favouriteContact?.displayName ?? '',
            isLocalContact: favouriteContact?.isLocalContact ?? false,
          ),
        );
      }
    }

    return favouriteContactModelList.reversed.toList();
  }
}

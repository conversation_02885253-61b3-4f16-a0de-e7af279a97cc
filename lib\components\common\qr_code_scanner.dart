import 'dart:io';

import 'package:ddone/constants/keys/widget_keys.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

class QRCodeScannerPage extends StatefulWidget {
  static const routeName = '/qrCodeScannerPage';

  const QRCodeScannerPage({
    super.key,
  });

  @override
  QRCodeScannerPageState createState() => QRCodeScannerPageState();
}

class QRCodeScannerPageState extends State<QRCodeScannerPage> {
  QRViewController? controller;

  String? currentCode;

  @override
  void initState() {
    super.initState();
  }

  // In order to get hot reload to work we need to pause the camera if the platform
  // is android, or resume the camera if the platform is iOS.
  @override
  void reassemble() {
    super.reassemble();

    if (isAndroid) {
      controller!.pauseCamera();
    } else if (isIOS) {
      controller!.resumeCamera();
    }
  }

  @override
  void dispose() {
    controller?.dispose();

    super.dispose();
  }

  void _onQRViewScanned(QRViewController qrViewController) {
    controller = qrViewController;

    if (Platform.isAndroid) {
      controller!.resumeCamera();
    }

    controller!.scannedDataStream.listen((scanData) async {
      await controller!.pauseCamera();

      String newCode = scanData.code ?? '';

      // To prevent multiple pop when scanned
      if (currentCode == newCode) {
        return;
      }

      currentCode = newCode;

      pop(newCode);

      return;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final textTheme = themeState.themeData.textTheme;
        final colorTheme = themeState.colorTheme;

        return Scaffold(
          appBar: AppBar(
            leading: IconButton(
              onPressed: pop,
              icon: Icon(
                Icons.close,
                color: colorTheme.onPrimaryColor,
              ),
            ),
            title: Text(
              'Scanner',
              style: textTheme.bodyLarge!.copyWith(color: colorTheme.onPrimaryColor),
            ),
            centerTitle: true,
          ),
          body: SafeArea(
            child: Column(
              children: [
                Expanded(
                  child: QRView(
                    key: WidgetKeys.qrScannerPageKey,
                    onQRViewCreated: _onQRViewScanned,
                    overlay: QrScannerOverlayShape(borderColor: Colors.black),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

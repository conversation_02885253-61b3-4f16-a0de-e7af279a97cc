import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/models/invitation_model.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'invitation_state.dart';

class InvitationCubit extends Cubit<InvitationState> {
  InvitationCubit._() : super(InvitationInitial());

  factory InvitationCubit.initial() {
    return InvitationCubit._();
  }

  void receiveInvitation({
    required LoginCubit loginCubit,
    required String to,
    required String from,
    required String roomJid,
    required String reason,
  }) {
    if (loginCubit.state is LoginAuthenticated) {
      // LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;
      // final muc = currentLoginState.connection.getManagerById<MUCManager>(mucManager);
      final newInvitation = InvitationModel(
        to: to,
        from: from,
        roomJid: roomJid,
        reason: reason,
      );

      debugPrint('state: ${state.invitationList}');

      final List<InvitationModel> updatedInvitations = List<InvitationModel>.from(state.invitationList)
        ..add(newInvitation);
      debugPrint(
        'Invitation: $updatedInvitations',
      );

      emit(InvitationReceived(invitations: updatedInvitations));
      debugPrint('state: ${state.invitationList}');
    }
  }
}

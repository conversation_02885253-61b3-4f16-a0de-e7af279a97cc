import 'package:http/http.dart' as http;
import 'package:xml/xml.dart';

Future<Map<String, String>?> fetchAndParseXml(url) async {
  try {
    // URL of the XML file
    // const url = 'https://raw.githubusercontent.com/username/repository/branch/path/to/file.xml';

    // Fetch the XML
    final response = await http.get(Uri.parse(url));
    if (response.statusCode == 200) {
      // Parse the XML
      final document = XmlDocument.parse(response.body);
      // Parse the XML
      // final document = XmlDocument.parse(response.body);

      // Extract data from the first <item> tag
      final item = document.findAllElements('item').first;
      final version = item.findElements('version').single.text;
      final downloadPath = item.findElements('enclosure').single.getAttribute('url')!;
      final description = item.findElements('description').single.text;

      // Return details as a map
      return {
        'version': version,
        'downloadPath': downloadPath,
        'description': description,
      };

      // // Extract data
      // final items = document.findAllElements('item');
      // for (var item in items) {
      //   final version = item.findElements('version').single.text;
      //   final downloadPath = item.findElements('enclosure').single.getAttribute('url');
      //   final description = item.findElements('description').single.text;

      //   print('Version: $version');
      //   print('Download Path: $downloadPath');
      //   print('Description: $description');
      // }
    } else {
      print('Failed to fetch XML. Status code: ${response.statusCode}');
    }
  } catch (e) {
    print('Error fetching XML: $e');
  }
  return null;
}


@echo off
echo Building Flutter Windows release exe...
fvm flutter build windows --dart-define=FLAVOR=production -t lib/main_production.dart --release

echo Removing conflicting runtime DLLs...
cd build\windows\x64\runner\Release
if exist msvcp140.dll (
    del msvcp140.dll
    echo Deleted msvcp140.dll
)
if exist vcruntime140.dll (
    del vcruntime140.dll
    echo Deleted vcruntime140.dll
)
if exist vcruntime140_1.dll (
    del vcruntime140_1.dll
    echo Deleted vcruntime140_1.dll
)

echo Going back to project root...
cd ..\..\..\..\..\

echo Done!
pause
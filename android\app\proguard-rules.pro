# Flutter wrapper classes
-keep class io.flutter.** { *; }
-dontwarn io.flutter.embedding.**

# WebRTC
-keep class org.webrtc.** { *; }
-dontwarn org.webrtc.**

# CallKit Incoming
-keep class com.hiennv.flutter_callkit_incoming.** { *; }

# This is generated automatically by the Android Gradle plugin.
-dontwarn com.google.android.play.core.splitcompat.SplitCompatApplication
-dontwarn java.beans.ConstructorProperties
-dontwarn java.beans.Transient
-dontwarn org.conscrypt.Conscrypt
-dontwarn org.conscrypt.OpenSSLProvider
-dontwarn org.w3c.dom.bootstrap.DOMImplementationRegistry
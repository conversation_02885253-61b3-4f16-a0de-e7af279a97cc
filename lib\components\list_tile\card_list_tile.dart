import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CardListTile extends StatelessWidget {
  final String listTileTitle;
  final Icon leadingIcon;
  final Widget? trailingWidget;
  final VoidCallback? onTap;
  final bool isTileSelected;

  const CardListTile({
    required this.listTileTitle,
    required this.leadingIcon,
    this.trailingWidget,
    this.onTap,
    this.isTileSelected = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        return Card(
          elevation: 3,
          margin: const EdgeInsets.only(
            bottom: spacingExtraSmall,
            left: spacingExtraLarge,
            right: spacingExtraLarge,
          ),
          color: isTileSelected ? colorTheme.primaryColor.withOpacity(opacityExtraLow) : Colors.transparent,
          shape: RoundedRectangleBorder(
            side: BorderSide(
              width: 0.8,
              color: isTileSelected ? colorTheme.primaryColor : Colors.grey.shade800,
            ),
            borderRadius: BorderRadius.circular(10),
          ),
          child: InkWell(
            onTap: () {
              if (onTap != null) {
                onTap!();
              }
            },
            child: ListTile(
              leading: leadingIcon,
              title: Text(
                listTileTitle,
                style: textTheme.titleSmall!.copyWith(color: colorTheme.onPrimaryColor),
                maxLines: 2,
              ),
              trailing: trailingWidget,
            ),
          ),
        );
      },
    );
  }
}

import 'dart:io';

import 'package:ddone/cubit/common/network/network_cubit.dart';
import 'package:ddone/events/network_event.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:event_bus_plus/res/event_bus.dart';
import 'package:moxxmpp/moxxmpp.dart';

class ServerAvailabilityConnectivityManager extends ConnectivityManager {
  final IEventBus eventBus;
  final Duration timeout = const Duration(seconds: 3);
  final Duration pollingInterval = const Duration(seconds: 2);
  String host;
  int port;
  bool _noNetworkConnection = false;

  ServerAvailabilityConnectivityManager(this.host, {this.port = 5222}) : eventBus = sl.get<IEventBus>() {
    eventBus.on<NetworkEvent>().listen((event) {
      log.t('ServerAvailabilityConnectivityManager - eventBus: networkEvent:$event');
      if (event.state is NetworkDisconnected) {
        _noNetworkConnection = true;
      } else {
        _noNetworkConnection = false;
      }
    });
  }

  @override
  Future<bool> hasConnection() async {
    // make sure server is reachable
    bool isServerReachable = false;
    try {
      final socket = await Socket.connect(host, port, timeout: timeout);
      socket.destroy();
      isServerReachable = true;
    } catch (_) {
      log.w('ServerAvailabilityConnectivityManager - xmpp server not reachable. host:$host port:$port');
    }
    return isServerReachable && !_noNetworkConnection;
  }

  @override
  Future<void> waitForConnection() async {
    log.t('ServerAvailabilityConnectivityManager - Waiting for connection to become available...');
    while (!(await hasConnection())) {
      log.t(
          'ServerAvailabilityConnectivityManager - No connection available, waiting ${pollingInterval.inSeconds}s before retry...');
      await Future.delayed(pollingInterval);
    }
    log.t('ServerAvailabilityConnectivityManager - Connection is now available');
  }
}

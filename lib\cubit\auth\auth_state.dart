part of 'auth_cubit.dart';

abstract class AuthState extends Equatable {
  final String displaySipName;
  final String displaySipDomain;
  final String displaySipNumber;

  const AuthState({
    this.displaySipName = '-',
    this.displaySipDomain = '-',
    this.displaySipNumber = '-',
  });

  @override
  List<Object?> get props => [
        displaySipName,
        displaySipDomain,
        displaySipNumber,
      ];
}

class AuthInitial extends AuthState {
  const AuthInitial() : super();

  @override
  List<Object?> get props => super.props..addAll([]);
}

class AuthRegistering extends AuthState {
  final String sipName;
  final String sipNumber;
  final String sipDomain;
  final String sipProxy;
  final String sipSecret;
  final String sipContactUrlBase;
  final String sipContactUrlParams;
  final String sipWsUrl;

  const AuthRegistering({
    required this.sipName,
    required this.sipNumber,
    required this.sipDomain,
    required this.sipProxy,
    required this.sipSecret,
    required this.sipContactUrlBase,
    required this.sipContactUrlParams,
    required this.sipWsUrl,
  }) : super();

  @override
  List<Object?> get props => super.props
    ..addAll([
      sipName,
      sipNumber,
      sipDomain,
      sipProxy,
      sipSecret,
      sipWsUrl,
    ]);
}

class AuthManualRegistering extends AuthRegistering {
  const AuthManualRegistering({
    required super.sipName,
    required super.sipNumber,
    required super.sipDomain,
    required super.sipProxy,
    required super.sipSecret,
    required super.sipContactUrlBase,
    required super.sipContactUrlParams,
    required super.sipWsUrl,
  });

  @override
  List<Object?> get props => super.props..addAll([]);
}

class AuthSuccess extends AuthState {
  const AuthSuccess({
    required super.displaySipName,
    required super.displaySipDomain,
    required super.displaySipNumber,
  });

  @override
  List<Object?> get props => super.props..addAll([]);
}

class AuthFail extends AuthState {
  const AuthFail() : super();

  @override
  List<Object?> get props => super.props..addAll([]);
}

class AuthAutoLoginFail extends AuthFail {
  const AuthAutoLoginFail() : super();

  @override
  List<Object?> get props => super.props..addAll([]);
}

class AuthLogOutSuccess extends AuthInitial {
  const AuthLogOutSuccess() : super();

  @override
  List<Object?> get props => super.props..addAll([]);
}

class AuthLogOutFail extends AuthSuccess {
  const AuthLogOutFail({
    required super.displaySipName,
    required super.displaySipDomain,
    required super.displaySipNumber,
  });

  @override
  List<Object?> get props => super.props..addAll([]);
}

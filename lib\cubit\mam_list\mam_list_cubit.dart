import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/constants/misc_constants.dart';
import 'package:ddone/constants/regex_constants.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/models/contact_model.dart';
import 'package:ddone/models/xmpp_model/mam_info.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:ddone/utils/extensions/map_ext.dart';
import 'package:ddone/utils/extensions/string_ext.dart';
import 'package:equatable/equatable.dart';
import 'package:logger/logger.dart';
import 'package:moxxmpp/model/conference_info.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'mam_list_state.dart';

class MamListCubit extends Cubit<MamListState> {
  final HiveService hiveService;
  final SharedPreferences sharedPreferences;
  final Logger log;

  MamListCubit()
      : hiveService = sl.get<HiveService>(),
        sharedPreferences = sl.get<SharedPreferences>(),
        log = sl.get<Logger>(),
        super(const MamListInitial(
          mamList: {},
          mamgroupList: {},
          mamFullList: [],
          filteredMamFullList: [],
          contactModelList: [],
          filteredContactModelList: [],
          bookmarkModelList: [],
          filterBookmarkModelList: [],
        ));

  static Map<String, List<MamInfo>> stanza = {};
  final Set<String> processedMessageIds = {};
  static Map<String, List<MamInfo>> test = {};

  void getCombinedFullList({required ContactsCubit contactsCubit}) {
    final List<ContactModel> contactModelList = contactsCubit.state.contactModelList;
    final Map<String, List<MamInfo>> mamList = state.mamList;
    final Map<String, List<MamInfo>> mamgroupList = state.mamgroupList;

    // From mamList (individual chats)
    List<Map<String, dynamic>> mamListItems = mamList.entries
        .map((e) => {
              'isGroup': false,
              'displayName':
                  contactModelList.firstWhereOrNull((element) => element.contactEntries[0].uri == e.key)?.displayName,
              'jid': e.key,
              'datetime': e.value.isNotEmpty ? e.value.last.time : null,
            })
        .toList();

    // From mamgroupList (group chats)
    List<Map<String, dynamic>> groupListItems = mamgroupList.entries
        .map((e) => {
              'isGroup': true,
              'displayName': e.key,
              'jid': e.key,
              'datetime': e.value.isNotEmpty ? e.value.last.time : null,
            })
        .toList();

    List<Map<String, dynamic>> combinedList = [
      ...mamListItems,
      ...groupListItems,
    ];
    combinedList.sort(
      (a, b) {
        // Sort by datetime in descending order first
        final dateA = a['datetime'] != null ? DateTime.parse(a['datetime']) : hundredYearsAgo;
        final dateB = b['datetime'] != null ? DateTime.parse(b['datetime']) : hundredYearsAgo;
        final dateTimeComparison = dateB.compareTo(dateA);

        // If datetimes are equal, sort by displayName alphabetically
        if (dateTimeComparison == 0) {
          final displayNameA = a['displayName'] as String?;
          final displayNameB = b['displayName'] as String?;
          if (displayNameA != null && displayNameB != null) {
            return displayNameA.toLowerCase().compareTo(displayNameB.toLowerCase());
          } else if (displayNameA != null) {
            return -1; // Non-null comes before null
          } else if (displayNameB != null) {
            return 1; // Null comes after non-null
          } else {
            return 0; // Both are null, so they are equal
          }
        }
        return dateTimeComparison; // Return the datetime comparison result
      },
    );

    emit(
      MamListUpdated(
        mamList: state.mamList,
        mamgroupList: state.mamgroupList,
        mamFullList: combinedList,
        filteredMamFullList: combinedList,
        contactModelList: state.contactModelList,
        filteredContactModelList: state.filteredContactModelList,
        bookmarkModelList: state.bookmarkModelList,
        filterBookmarkModelList: state.filterBookmarkModelList,
      ),
    );
  }

  void getCombinedContactList({required ContactsCubit contactsCubit}) {
    final List<ContactModel> contactModelList = List.from(contactsCubit.state.contactModelList);
    contactModelList
        .sort((a, b) => a.displayName.toLowerCase().compareTo(b.displayName.toLowerCase())); // sort alphabetically
    emit(
      MamListUpdated(
        mamList: state.mamList,
        mamgroupList: state.mamgroupList,
        mamFullList: state.mamFullList,
        filteredMamFullList: state.filteredMamFullList,
        contactModelList: contactModelList,
        filteredContactModelList: contactModelList,
        bookmarkModelList: state.bookmarkModelList,
        filterBookmarkModelList: state.filterBookmarkModelList,
      ),
    );
  }

  void getBookmarkList({required ContactsCubit contactsCubit}) {
    List<ConferenceInfo> bookmarkModelList = [
      ...state.mamgroupList.entries.map((e) => ConferenceInfo(e.key, '', e.key, e.key)),
    ];

    // Convert to Set to improve searching lookup time
    Set<String> bookmarkModelSet = bookmarkModelList.map((e) => e.jid.split('@').first.split('_').first).toSet();

    List<ConferenceInfo> missingBookmark = contactsCubit.state.bookmarkGroupList
        .where((element) => !bookmarkModelSet.contains(element.jid.split('@').first.split('_').first))
        .toList();
    bookmarkModelList.addAll(missingBookmark);

    bookmarkModelList.sort((a, b) => a.name.compareTo(b.name));

    emit(
      MamListUpdated(
        mamList: state.mamList,
        mamgroupList: state.mamgroupList,
        mamFullList: state.mamFullList,
        filteredMamFullList: state.filteredMamFullList,
        contactModelList: state.contactModelList,
        filteredContactModelList: state.filteredContactModelList,
        bookmarkModelList: bookmarkModelList,
        filterBookmarkModelList: bookmarkModelList,
      ),
    );
  }

  void filterFullList(String value) {
    List<Map<String, dynamic>> tempFullModelList = [];

    tempFullModelList = state.mamFullList
        .where((element) =>
            element['displayName'].toString().toLowerCase().contains(value.toLowerCase()) ||
            element['jid'].toLowerCase().contains(value.toLowerCase()))
        .toList();

    emit(
      MamListUpdated(
        mamList: state.mamList,
        mamgroupList: state.mamgroupList,
        mamFullList: state.mamFullList,
        filteredMamFullList: value.isEmpty ? state.mamFullList : tempFullModelList,
        contactModelList: state.contactModelList,
        filteredContactModelList: state.filteredContactModelList,
        bookmarkModelList: state.bookmarkModelList,
        filterBookmarkModelList: state.filterBookmarkModelList,
      ),
    );
    // print("state.state.bookmarkModelList(filterFullList) ${state.filterBookmarkModelList}");
    // print("state.state.bookmarkModelList(filterFullList) ${state.bookmarkModelList}");
  }

  void filterContactList(String value) {
    List<ContactModel> tempContactModelList = [];

    tempContactModelList = state.contactModelList.where((element) {
      final filteredDisplayNameDomain = element.displayName.userIDFilter();

      final filteredUriDomain = element.contactEntries[0].uri.userIDFilter();

      return filteredDisplayNameDomain.toLowerCase().contains(value.toLowerCase()) ||
          filteredUriDomain.toLowerCase().contains(value.toLowerCase());
    }).toList();

    emit(
      MamListUpdated(
        mamList: state.mamList,
        mamgroupList: state.mamgroupList,
        mamFullList: state.mamFullList,
        filteredMamFullList: state.filteredMamFullList,
        contactModelList: state.contactModelList,
        filteredContactModelList: value.isEmpty ? state.contactModelList : tempContactModelList,
        bookmarkModelList: state.bookmarkModelList,
        filterBookmarkModelList: state.filterBookmarkModelList,
      ),
    );

    // print("state.state.bookmarkModelList(filterContactList) $tempContactModelList");
    // print("state.state.bookmarkModelList(filterContactList) ${state.bookmarkModelList}");
  }

  void filterBookmarkList(String value) {
    List<ConferenceInfo> tempContactModelList = [];

    tempContactModelList = state.bookmarkModelList.where((element) {
      final filteredGroupJidDomain = element.name.mucIDFilter();
      final regexGroupJidUid = groupNameRegex.firstMatch(filteredGroupJidDomain);
      final String? groupJid = regexGroupJidUid?.group(0);
      final String groupJidFiltered = filteredGroupJidDomain.replaceAll(groupJid ?? '', '');

      final filteredGroupNameDomain = element.name.mucIDFilter();
      final regexGroupNameUid = groupNameRegex.firstMatch(filteredGroupNameDomain);
      final String? groupName = regexGroupNameUid?.group(0);
      final String groupNameFiltered = filteredGroupNameDomain.replaceAll(groupName ?? '', '');

      return groupNameFiltered.mucIDFilter().toLowerCase().contains(value.toLowerCase()) ||
          groupJidFiltered.toLowerCase().contains(value.toLowerCase());
    }).toList();

    emit(
      MamListUpdated(
        mamList: state.mamList,
        mamgroupList: state.mamgroupList,
        mamFullList: state.mamFullList,
        filteredMamFullList: state.filteredMamFullList,
        contactModelList: state.contactModelList,
        filteredContactModelList: state.contactModelList,
        bookmarkModelList: state.bookmarkModelList,
        filterBookmarkModelList: value.isEmpty ? state.bookmarkModelList : tempContactModelList,
      ),
    );

    // print("state.state.bookmarkModelList(filterBookmarkList) ${state.bookmarkModelList}");
    // print("state.state.bookmarkModelList(filterBookmarkList) $tempContactModelList");
  }

  void clearAllContactList() {
    emit(
      const MamListInitial(
        mamList: {},
        mamgroupList: {},
        mamFullList: [],
        filteredMamFullList: [],
        contactModelList: [],
        filteredContactModelList: [],
        bookmarkModelList: [],
        filterBookmarkModelList: [],
      ),
    );
    // print('state.mamgroupListstate.mamgroupListwww ${state.mamgroupList}');
    // print('state.mamgroupListstate.mamgroupListwww ${state.bookmarkModelList}');
    // print('state.mamgroupListstate.mamgroupListwww ${state.filterBookmarkModelList}');
  }

  void clearMsg(String uid) {
    state.mamList.forEach((key, value) {
      if (uid == key) {
        value.clear();
      }
    });
  }

  void clearGroupMsg(String uid) {
    state.mamgroupList.forEach((key, value) {
      if (uid == key) {
        value.clear();
      }
    });
  }

  Map<String, List<MamInfo>> sortMessage() {
    final sortedMamList = state.mamList.map((key, mamList) {
      mamList.sort((a, b) => a.time.compareTo(b.time)); // Sort in ascending order
      return MapEntry(key, mamList);
    });

    return sortedMamList;

    // state.mamList.sortedBy(
    //   (u) {
    //     if (u.isNotEmpty) {
    //       debugPrint('Message Sorted');
    //       return DateTime.parse(u.last.time);
    //     }

    //     // else {
    //     //   return '';
    //     // }

    //     return DateTime.now();
    //   },
    //   isAsc: false,
    // );
  }

  void sortGroupMessage() {
    state.mamgroupList.sortedBy(
      (u) {
        if (u.isNotEmpty) {
          return DateTime.parse(u.last.time);
        }
        // else {
        //   return '';
        // }

        return DateTime.now();
      },
      isAsc: true,
    );
  }

  void getMamList({bool isLazyLoad = false}) {
    emit(
      MamListLoading(
        mamList: state.mamList,
        mamgroupList: state.mamgroupList,
        mamFullList: state.mamFullList,
        filteredMamFullList: state.filteredMamFullList,
        contactModelList: state.contactModelList,
        filteredContactModelList: state.filteredContactModelList,
        bookmarkModelList: state.bookmarkModelList,
        filterBookmarkModelList: state.filterBookmarkModelList,
      ),
    );
    try {
      final sipNumber = sharedPreferences.getString(CacheKeys.sipNumber) ?? '';
      final sipDomain = sharedPreferences.getString(CacheKeys.sipDomain) ?? '';
      String xmppAccount = '$sipNumber@$sipDomain';

      final Map<String, List<MamInfo>> conversationMap = Map<String, List<MamInfo>>.from(state.mamList);
      final Map<String, List<MamInfo>> conversationGroupMap = Map<String, List<MamInfo>>.from(state.mamgroupList);
      final Map<String, List<MamInfo>> updatedList = Map<String, List<MamInfo>>.from(state.mamList)
        ..addAll(MamListCubit.stanza);
      final Map<String, List<MamInfo>> updatedGroupList = Map<String, List<MamInfo>>.from(state.mamgroupList)
        ..addAll(MamListCubit.test);

      for (var entry in updatedList.entries) {
        final reveredEntries = entry.value;
        for (MamInfo message in reveredEntries) {
          final from = message.sender.split('/');
          final to = message.to.split('/');

          final oppositeParty = (to[0] == xmppAccount) ? from[0] : to[0];

          if (!conversationMap.containsKey(oppositeParty)) {
            conversationMap[oppositeParty] = [];
          }

          // Check for duplicate message and replace the old message if found
          // To consider as duplicate:
          // 1) when message has msgId, and msgId is the same
          //    - same message id so is duplicate
          // 2) when message has msgId, but msgId is different, but time is same
          //    - when we retreive message from server, it will give a different set of msgId.
          //      In such case we compare its time down to microseconds. The chance of having multiple
          //      message in a chat that has the same microseconds record in server is very slim.
          // 3) when message dont' have msgId.
          //    - when user send message, we won't know the message id until we retreive from server.
          //      But we still need to display to user that the message has been sent in UI so we added
          //      message with msgId=''. In such case we can only compare its content because the time that we
          //      add the message to UI and the time that it get recorded in server is different.
          bool isDuplicate = false;
          if (conversationMap.containsKey(oppositeParty)) {
            for (int i = 0; i < conversationMap[oppositeParty]!.length; i++) {
              if ((conversationMap[oppositeParty]![i].msgId.isEmpty &&
                      conversationMap[oppositeParty]![i].body == message.body) ||
                  (conversationMap[oppositeParty]![i].msgId == message.msgId ||
                      conversationMap[oppositeParty]![i].time == message.time)) {
                conversationMap[oppositeParty]![i] = message;
                isDuplicate = true;
                break;
              }
            }
          }

          if (!isDuplicate) {
            conversationMap[oppositeParty]!.add(message);
          }

          processedMessageIds.add(message.msgId);
        }
      }

      for (var entry in updatedGroupList.entries) {
        for (MamInfo message in entry.value) {
          final from = message.sender.split('/');
          final to = message.to.split('/');

          final oppositeParty = (to[0] == xmppAccount) ? from[0] : to[0];
          final groupOrChatKey = message.grpSender.isEmpty ? oppositeParty : message.to;

          if (!conversationGroupMap.containsKey(groupOrChatKey)) {
            conversationGroupMap[groupOrChatKey] = [];
          }

          // Check for duplicate message and replace the old message if found
          bool isDuplicate = false;
          if (conversationGroupMap.containsKey(groupOrChatKey)) {
            for (int i = 0; i < conversationGroupMap[groupOrChatKey]!.length; i++) {
              if ((conversationGroupMap[groupOrChatKey]![i].msgId.isEmpty &&
                      conversationGroupMap[groupOrChatKey]![i].body == message.body) ||
                  (conversationGroupMap[groupOrChatKey]![i].msgId == message.msgId ||
                      conversationGroupMap[groupOrChatKey]![i].time == message.time)) {
                conversationGroupMap[groupOrChatKey]![i] = message;
                isDuplicate = true;
                break;
              }
            }
          }

          if (!isDuplicate) {
            conversationGroupMap[groupOrChatKey]!.add(message);
          }

          processedMessageIds.add(message.msgId);
        }
      }

      // sort by time asc
      for (List<MamInfo> conversation in conversationMap.values) {
        conversation.sort((a, b) {
          DateTime timeA = DateTime.parse(a.time);
          DateTime timeB = DateTime.parse(b.time);
          return timeA.compareTo(timeB);
        });
      }
      for (List<MamInfo> conversationGroup in conversationGroupMap.values) {
        conversationGroup.sort((a, b) {
          DateTime timeA = DateTime.parse(a.time);
          DateTime timeB = DateTime.parse(b.time);
          return timeA.compareTo(timeB);
        });
      }

      emit(MamListUpdated(
        mamList: conversationMap,
        mamgroupList: conversationGroupMap,
        mamFullList: state.mamFullList,
        filteredMamFullList: state.filteredMamFullList,
        contactModelList: state.contactModelList,
        filteredContactModelList: state.filteredContactModelList,
        bookmarkModelList: state.bookmarkModelList,
        filterBookmarkModelList: state.filterBookmarkModelList,
      ));
    } catch (e) {
      log.e('Error in getMamList', error: e);
    }
  }

  void addMessage(
    String to,
    String sender,
    String body,
    String type,
    String time,
    String msgId,
    String grpSender,
  ) {
    final currentState = state as MamListUpdated;
    final newMessage = MamInfo(
      to,
      sender,
      body,
      type,
      time,
      msgId,
      '', //TODO: put id
      grpSender,
    );
    // final map = Map<String, List<newMessage>>;

    final senderSplit = sender.split('/');
    final toSplit = to.split('/');

    if (msgId.isNotEmpty) {
      if (processedMessageIds.contains(msgId)) {
        return;
      }
    }

    final updatedList = Map<String, List<MamInfo>>.from(currentState.mamList);
    final updatedGroupList = Map<String, List<MamInfo>>.from(currentState.mamgroupList);
    if (to.isEmpty) {
      if (sender.contains('muc')) {
        updatedGroupList.putIfAbsent(senderSplit[0], () => []).add(newMessage);
        // updatedGroupList[senderSplit[0]]?.insert(0, newMessage);
      } else {
        updatedList.putIfAbsent(senderSplit[0], () => []).add(newMessage);
        // updatedList[senderSplit[0]]?.insert(0, newMessage);
      }
    } else {
      if (to.contains('muc')) {
        updatedGroupList.putIfAbsent(toSplit[0], () => []).add(newMessage);
        // updatedGroupList[toSplit[0]]?.insert(0, newMessage);
        // updatedGroupList[toSplit[0]]?.add(newMessage);
      } else {
        updatedList.putIfAbsent(toSplit[0], () => []).add(newMessage);
        // updatedList[toSplit[0]]?.insert(0, newMessage);
        // updatedList[toSplit[0]]?.add(newMessage);
      }
    }

    // debugPrint("IN ADD MESSAGE(New): $newMessage");
    // debugPrint("IN ADD MESSAGE(User): $stanza");
    // debugPrint("IN ADD MESSAGE(Group): $test");
    emit(MamListUpdated(
      mamList: updatedList,
      mamgroupList: updatedGroupList,
      mamFullList: state.mamFullList,
      filteredMamFullList: state.filteredMamFullList,
      contactModelList: state.contactModelList,
      filteredContactModelList: state.filteredContactModelList,
      bookmarkModelList: state.bookmarkModelList,
      filterBookmarkModelList: state.filterBookmarkModelList,
    ));
  }

  void addReceiveMessage(
    String to,
    String sender,
    String body,
    String type,
    String time,
    String msgId,
    String grpSender,
  ) {
    final currentState = state as MamListUpdated;
    final newMessage = MamInfo(
      to,
      sender,
      body,
      type,
      time,
      msgId,
      '', //TODO: put id
      grpSender,
    );
    // final map = Map<String, List<newMessage>>;

    final senderSplit = sender.split('/');
    final toSplit = to.split('/');

    if (processedMessageIds.contains(msgId)) {
      return;
    }

    final updatedList = Map<String, List<MamInfo>>.from(currentState.mamList);
    final updatedGroupList = Map<String, List<MamInfo>>.from(currentState.mamgroupList);

    if (to.isEmpty) {
      if (sender.contains('muc')) {
        updatedGroupList.putIfAbsent(senderSplit[0], () => []).add(newMessage);
      } else {
        updatedList.putIfAbsent(senderSplit[0], () => []).add(newMessage);
      }
    } else {
      if (to.contains('muc')) {
        updatedGroupList.putIfAbsent(toSplit[0], () => []).add(newMessage);
      } else {
        updatedList.putIfAbsent(toSplit[0], () => []).add(newMessage);
      }
    }

    // debugPrint("IN ADD MESSAGE: $newMessage");
    emit(MamListUpdated(
      mamList: updatedList,
      mamgroupList: updatedGroupList,
      mamFullList: state.mamFullList,
      filteredMamFullList: state.filteredMamFullList,
      contactModelList: state.contactModelList,
      filteredContactModelList: state.filteredContactModelList,
      bookmarkModelList: state.bookmarkModelList,
      filterBookmarkModelList: state.filterBookmarkModelList,
    ));
  }

  void filterMamList(String value) {
    Map<String, List<MamInfo>> filteredMap = {};

    // List<MamInfo> tempFavouriteContactModelList = [];
    final map = state.mamList;

    for (var key in map.keys) {
      if (key.toLowerCase().contains(value.toLowerCase())) {
        filteredMap[key] = map[key]!;
      }
    }

    // tempFavouriteContactModelList = hiveService
    //     .getFavouriteContactList()
    //     .where((element) => element.displayName.toLowerCase().contains(value.toLowerCase()))
    //     .toList();

    emit(MamListUpdated(
      mamList: state.mamList,
      mamgroupList: state.mamgroupList,
      mamFullList: state.mamFullList,
      filteredMamFullList: state.filteredMamFullList,
      contactModelList: state.contactModelList,
      filteredContactModelList: state.filteredContactModelList,
      bookmarkModelList: state.bookmarkModelList,
      filterBookmarkModelList: state.filterBookmarkModelList,
    ));
  }

  void filterMamGroupList(String value) {
    Map<String, List<MamInfo>> filteredGroupMap = {};

    // List<MamInfo> tempFavouriteContactModelList = [];
    final map = state.mamgroupList;

    for (var key in map.keys) {
      if (key.toLowerCase().contains(value.toLowerCase())) {
        filteredGroupMap[key] = map[key]!;
      }
    }

    // tempFavouriteContactModelList = hiveService
    //     .getFavouriteContactList()
    //     .where((element) => element.displayName.toLowerCase().contains(value.toLowerCase()))
    //     .toList();
    // print('state.mamgroupListstate.mamgroupListstate.mamgroupList ${state.mamgroupList}');

    emit(MamListUpdated(
      mamList: state.mamList,
      mamgroupList: state.mamgroupList,
      mamFullList: state.mamFullList,
      filteredMamFullList: state.filteredMamFullList,
      contactModelList: state.contactModelList,
      filteredContactModelList: state.filteredContactModelList,
      bookmarkModelList: state.bookmarkModelList,
      filterBookmarkModelList: state.filterBookmarkModelList,
    ));
  }

  // void addNewLoadMessage(
  //   String to,
  //   String sender,
  //   String body,
  //   String type,
  //   String time,
  //   String msgId,
  //   String grpSender,
  // ) {
  //   final newLoadMessage = MamInfo(
  //     to,
  //     sender,
  //     body,
  //     type,
  //     time,
  //     msgId,
  //     grpSender,
  //   );

  //   final Map<String, List<MamInfo>> loadingMessage = {to: List.from(state.mamList)..addAll()};
  // }
}

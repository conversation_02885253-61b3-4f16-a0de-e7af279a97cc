import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SquareShapeInkWell extends StatefulWidget {
  final VoidCallback? onTap, onLongPress;
  final Widget contentWidget;
  final Color? color;
  final double? height, width;

  const SquareShapeInkWell({
    required this.contentWidget,
    this.color,
    this.onTap,
    this.onLongPress,
    this.height,
    this.width,
    super.key,
  });

  @override
  State<SquareShapeInkWell> createState() => _SquareShapeInkWellState();
}

class _SquareShapeInkWellState extends State<SquareShapeInkWell> {
  // bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;

        return Padding(
          padding: paddingExtraSmall,
          child: InkWell(
            borderRadius: BorderRadius.circular(5.0),
            onTap: widget.onTap,
            onLongPress: widget.onLongPress,
            child: Ink(
              height: widget.height ?? heightLarge,
              width: widget.width ?? widthLarge,
              decoration: BoxDecoration(
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(
                  5.0,
                ),
                color: widget.color ?? colorTheme.onSurfaceColor,
              ),
              child: widget.contentWidget,
            ),
          ),
        );
      },
    );
  }
}

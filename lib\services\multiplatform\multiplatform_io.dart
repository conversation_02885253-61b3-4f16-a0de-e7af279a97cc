import 'dart:io';

import 'package:ddone/models/enums/platform_enum.dart';

PlatformEnum getPlatform() {
  if (Platform.isAndroid) return PlatformEnum.android;
  if (Platform.isIOS) return PlatformEnum.ios;
  if (Platform.isMacOS) return PlatformEnum.macos;
  if (Platform.isWindows) return PlatformEnum.windows;
  if (Platform.isLinux) return PlatformEnum.linux;

  throw UnimplementedError('Unsupported');
}

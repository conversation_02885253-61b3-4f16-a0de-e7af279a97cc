import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/events/firebase_event.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/callkit_service.dart';
import 'package:ddone/services/foreground_service.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:event_bus_plus/res/event_bus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:ddone/services/notification_service.dart';
import 'package:ddone/screens/chat_recent.dart';

class BackgroundService with PrefsAware {
  late final CallkitService _callkitService;
  late final NotificationService _localNotificationService;
  late final IEventBus _eventBus;

  static bool fromBackgroundIsolate = false;

  BackgroundService()
      : _callkitService = sl.get<CallkitService>(),
        _localNotificationService = sl.get<NotificationService>(),
        _eventBus = sl.get<IEventBus>();

  Future<void> handleFirebaseBackgroundMessage(RemoteMessage remoteMessage) async {
    // no need to run when app is open. Sometimes this function will be triggered
    // when user just opened the app.
    if (await isAppOpen()) return;

    String? messageId = remoteMessage.messageId;

    // prevent same mesage get processed more than once.
    String prefsMessageIdKey = '${CacheKeys.firebaseMessageId}_$messageId';
    await prefs.reload();
    if (prefs.getBool(prefsMessageIdKey) == true) {
      log.t('SKIPPED handleFirebaseBackgroundMessage for messageId:$messageId');
      return;
    }
    await prefs.setBool(prefsMessageIdKey, true, ttlInSeconds: 60);
    log.t('handleFirebaseBackgroundMessage for messageId:$messageId');

    String? category = remoteMessage.data['category'];
    String caller = remoteMessage.data['caller'] ?? '';
    String callerId = remoteMessage.data['callerId'] ?? '';
    String sender = remoteMessage.data['sender'] ?? '';
    String messageType = remoteMessage.data['messageType'] ?? '';
    String message = remoteMessage.data['message'] ?? '';
    _eventBus.fire(FirebaseEvent('$category', remoteMessage.data));
    switch (category) {
      case 'call':
        {
          // ios use APNS PushKit, handled in native iOS swift code.
          // For Android, start the foreground service to handle VoIP pre-warming and call management
          if (isAndroid) {
            _callkitService.init(acceptedCallCallback: (callDirection) async {
              if (callDirection == CallDirection.incoming) {
                log.t('in background service callkit accept');
                // Ensure foreground service is running before accepting call
                try {
                  if (!await ForegroundService.isRunning()) {
                    await ForegroundService.start();
                    log.t('Started foreground service on call accept');
                  }
                } catch (e) {
                  log.e('Failed to start foreground service on accept', error: e);
                }
                ForegroundService.invokeMethod(ForegroundMethod.acceptCall);
                prefs.setBool(CacheKeys.acceptedCallFromBackground, true, ttlInSeconds: 30);
              }
            }, declinedCallCallback: (callDirection) async {
              if (callDirection == CallDirection.incoming) {
                log.t('in background service callkit decline');
                // Ensure foreground service is running before declining call
                try {
                  if (!await ForegroundService.isRunning()) {
                    await ForegroundService.start();
                    log.t('Started foreground service on call decline');
                  }
                } catch (e) {
                  log.e('Failed to start foreground service on decline', error: e);
                }
                ForegroundService.invokeMethod(ForegroundMethod.declineCall);
              }
            }, endedCallCallback: () async {
              log.t('in background service callkit ended');
              ForegroundService.invokeMethod(ForegroundMethod.stop);
            }, missedCallCallback: () async {
              log.t('in background service callkit missed call');
              ForegroundService.invokeMethod(ForegroundMethod.stop);
            });
            _callkitService.incomingCall(caller, callerId);
            try {
              // Start foreground service immediately after showing incoming call
              // This should work because we're in the context of a high-priority FCM message
              await ForegroundService.start();
              log.t('Successfully started foreground service from background');
            } catch (e) {
              log.e('Failed to start foreground service from background', error: e);
              // Alternative: Try to start the service when user accepts the call
              // The foreground service will be started in the acceptedCallCallback
              log.w('Will attempt to start foreground service when user accepts call');
            }
          }
          break;
        }
      case 'miss-call':
        {
          // user didn't accept call in all devices
          _callkitService.endAllCall();
          _callkitService.showMissCall(caller, callerId);
          await Future.wait([
            prefs.setString(CacheKeys.missCall, 'miss call !!!!!'),
            prefs.setString(CacheKeys.missCallName, caller),
            prefs.setString(CacheKeys.missCallId, callerId),
            prefs.setString(CacheKeys.missCallTime, '${DateTime.now()}'),
          ]);

          // Stop the foreground service for Android
          if (isAndroid) await ForegroundService.invokeMethod(ForegroundMethod.stop);
          break;
        }
      case 'end-call':
        {
          // user accepted call in other device, this device ringing should get ended.
          Map<String, String> callInfo = await _callkitService.getCallerInfo();
          if (callInfo['callerId'] == callerId) {
            _callkitService.endAllCall();
            // Stop the foreground service for Android
            if (isAndroid) await ForegroundService.invokeMethod(ForegroundMethod.stop);
          }
          break;
        }
      case 'message':
        {
          _localNotificationService.showAttachmentLocalNotification(
              title: sender.split('@').firstOrNull ?? sender,
              messageType: messageType,
              message: message,
              payload: json.encode({
                'routeName': RecentChat.routeName,
                'jid': sender,
                'isGroup': false,
              }));
          break;
        }
      default:
        throw Exception('Unhandled category=$category in message');
    }
  }
}

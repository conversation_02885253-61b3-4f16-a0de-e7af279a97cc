import 'package:bloc/bloc.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/models/language_option.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:ddone/utils/status_bar_util.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'theme_state.dart';

class ThemeCubit extends Cubit<ThemeState> {
  final SharedPreferences sharedPrefs;

  ThemeCubit(
    ThemeType? themeType,
    LanguageOption language,
    this.sharedPrefs,
  ) : super(
          ThemeState.initial(
            themeType: themeType,
            language: language,
          ),
        );

  factory ThemeCubit.initial() {
    //the case of is register only happen when you mock the cubit in test case
    if (sl.isRegistered<ThemeCubit>()) {
      return sl.get<ThemeCubit>();
    }

    final sharedPrefs = sl.get<SharedPreferences>();

    final storeTheme = sharedPrefs.getString(CacheKeys.themeKey);
    final storeLanguage = sharedPrefs.getString(CacheKeys.languageKey);

    final initialTheme =
        storeTheme != null ? EnumToString.fromString(ThemeType.values, storeTheme) : ThemeType.defaultTheme;

    final initialLanguage = storeLanguage != null ? LanguageOption.fromString(storeLanguage) : defaultLanguage;

    sharedPrefs.setString(CacheKeys.languageKey, initialLanguage.locale.toString());

    return ThemeCubit(
      initialTheme,
      initialLanguage,
      sharedPrefs,
    );
  }

  void changeLanguage(LanguageOption languageOption) {
    sharedPrefs.setString(CacheKeys.languageKey, languageOption.locale.toString());

    emit(state.changeLanguage(languageOption));
  }

  void changeTheme() {
    var themeType = ThemeType.defaultTheme;

    if (state.themeType == ThemeType.defaultTheme) {
      themeType = ThemeType.darkTheme;
    }

    sharedPrefs.setString(CacheKeys.themeKey, EnumToString.convertToString(themeType));

    final newState = ThemeState.initial(
      themeType: themeType,
      language: state.language,
    );

    StatusBarUtil.setTransparentStatusBar(
      appBrightness: newState.themeData.brightness,
    );

    emit(newState);
  }
}

import 'dart:convert';
import 'dart:math';

import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/constants/value_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/services.dart';

class DDMeet extends StatefulWidget {
  const DDMeet({super.key});

  @override
  State<DDMeet> createState() => _DDMeetState();
}

class _DDMeetState extends State<DDMeet> {
  final _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
  late Uri url;
  final Random _rnd = Random();
  late String sipAddr;
  late final myStr;
  late final theString;
  late var bytes1;
  late var h;

  String status = 'Redirecting to DDMeet';

  String getRandomString(int length) =>
      String.fromCharCodes(Iterable.generate(length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));

  getInfo() async {
    final SharedPreferences prefs = sl.get<SharedPreferences>();

    // final String? sipProxy = prefs.getString(CacheKeys.sipProxy);
    final String? sipDomain = prefs.getString(CacheKeys.sipDomain);
    final String? sipNumber = prefs.getString(CacheKeys.sipNumber);
    final String? sipName = prefs.getString(CacheKeys.sipName);
    final String? sipPwd = prefs.getString(CacheKeys.sipPwd);

    if (sipName == '' || sipName == null) {
      EasyLoadingService().showInfoWithText('Please login account first');
      return;
    }

    sipAddr = '$sipNumber@$sipDomain';
    myStr = getRandomString(20);
    theString = "hashbrownisverygoood$sipAddr$sipPwd$myStr";
    bytes1 = utf8.encode(theString);
    h = sha256.convert(bytes1);
    // print("random string:" + myStr);
    // print("string to be hashed:" + theString);
    // print(h);
    url = Uri.parse('https://meet.dotdashtech.com/apiLogin?addr=$sipAddr&potato=$h&myString=$myStr');
  }

  _lauchUrl(sipAddr, h, myStr) async {
    // final Uri url = Uri.parse('https://meet.dotdashtech.com/apiLogin?addr=500%40192.168.50.66&potato=46e2bb813be3f40ea29c37cbbd93bba51eaf42b249cee9b000bbe7292493999e&myString=xM58SkJ5ZyhRp635806C');
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  @override
  initState() {
    super.initState();

    getInfo();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.colorTheme().surface,
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: context.colorTheme().surface,
        title: BlocBuilder<ThemeCubit, ThemeState>(
          builder: (context, themeState) {
            // final colorTheme = themeState.colorTheme;
            // final textTheme = themeState.themeData.textTheme;

            return const Text('DDMeet');
          },
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              '$imagePathPrefix/dot_dash_logo.png',
              width: context.responsiveSize(
                moileSize: widthXXXLarge,
                tabletSize: widthXXXLarge,
                desktopSize: widthXXXXLarge,
                largeScreenSize: width5XLarge,
              ),
            ),
            SizedBox(height: context.deviceHeight(0.1)),
            RectangleInkWell(
              title: 'Open DDMeet',
              onTap: () {
                _lauchUrl(
                  sipAddr,
                  h,
                  myStr,
                );
              },
            ),
            SizedBox(
              height: context.deviceHeight(0.05),
            ),
            RectangleInkWell(
              title: 'Copy URL',
              onTap: () {
                Clipboard.setData(ClipboardData(text: url.toString()));

                EasyLoadingService().showSuccessWithText('URL Copied');
              },
            ),
          ],
        ),
      ),
    );
  }
}

class RectangleInkWell extends StatelessWidget {
  final String title;
  final GestureTapCallback onTap;

  const RectangleInkWell({
    required this.title,
    required this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        final height = context.responsiveSize(
          moileSize: heightMedium,
          tabletSize: heightMedium,
          desktopSize: heightLarge,
          largeScreenSize: heightExtraLarge,
        );

        return InkWell(
          onTap: onTap,
          borderRadius: const BorderRadius.all(
            Radius.circular(20),
          ),
          child: Container(
            decoration: const BoxDecoration(
              color: Color.fromARGB(255, 60, 60, 60),
              borderRadius: BorderRadius.all(
                Radius.circular(20),
              ),
            ),
            alignment: Alignment.center,
            width: width9XLarge,
            height: height,
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: textTheme.displayMedium!.copyWith(color: colorTheme.primaryColor),
            ),
          ),
        );
      },
    );
  }
}

import 'package:ddone/environments/env.dart';
import 'package:ddone/services/xmpp/connectivity_manager.dart';
import 'package:ddone/services/xmpp/self_signed_tcp_socket_wrapper.dart';
import 'package:ddone/utils/async_utils.dart';
import 'package:ddone/utils/auth_util.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:moxlib/moxlib.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:moxxmpp_socket_tcp/moxxmpp_socket_tcp.dart';
import 'package:uuid/uuid.dart';

/// A class that handle all interaction with XMPP chat server (MongooseIM)
class XmppService {
  static final XmppService _instance = XmppService._internal();
  factory XmppService() {
    return _instance;
  }
  XmppService._internal();
  XmppConnection? xmppConnection;
  XmppConnectionState xmppConnectionState = XmppConnectionState.notConnected;
  bool negotiationDone = false;
  JID? _myJid;

  Future<bool> init({
    required String sipNumber,
    required String sipDomain,
    required String sipPwd,
    Future<void> Function(MessageEvent)? messageEventCallback,
    Future<void> Function(ConnectionStateChangedEvent)? connectionStateChangedEventCallback,
  }) async {
    _myJid = JID.fromString('$sipNumber@$sipDomain');
    String hashPassword = AuthUtil.hashSecret('$sipNumber:$sipDomain:$sipPwd');
    log.d('XmppService init - hashPassword:$hashPassword');
    xmppConnection = XmppConnection(
      RandomBackoffReconnectionPolicy(1, 60),
      ServerAvailabilityConnectivityManager(env!.mongooseimXmppHost),
      ClientToServerNegotiator(),
      env!.flavor.name == BuildFlavor.production.name ? TCPSocketWrapper(false) : SelfSignedTcpSocketWrapper(),
    );
    xmppConnection!.connectionSettings = ConnectionSettings(
      jid: _myJid!,
      password: hashPassword,
      host: env!.mongooseimXmppHost,
      port: int.tryParse(env!.mongooseimXmppPort),
    );
    xmppConnection!.registerManagers([
      StreamManagementManager(),
      PresenceManager(),
      MessageManager(),
      MessageArchiveManagementManager(),
      MUCManager(),
      HttpFileUploadManager(),
      StableIdManager(),
    ]);
    xmppConnection!.registerFeatureNegotiators([
      ResourceBindingNegotiator(),
      StartTlsNegotiator(),
      SaslPlainNegotiator(),
      SaslScramNegotiator(10, '', '', ScramHashType.sha512),
      SaslScramNegotiator(9, '', '', ScramHashType.sha256),
      SaslScramNegotiator(8, '', '', ScramHashType.sha1),
      StreamManagementNegotiator(),
    ]);
    xmppConnection!.asBroadcastStream().listen((event) {
      log.t('xmpp stream [<-- $event]');
      if (event is MessageEvent) {
        if (event.get<MAMData>() == null) return;
        final messageBodydata = event.extensions.get<MessageBodyData>();
        final body = messageBodydata?.body;
        log.t('xmpp stream [=== ${event.from} MAM] $body');
        if (messageEventCallback != null) messageEventCallback(event);
      } else if (event is ConnectionStateChangedEvent) {
        xmppConnectionState = event.state;
        log.t('xmpp stream [=== xmppConnectionState:$xmppConnectionState]');
        if (connectionStateChangedEventCallback != null) connectionStateChangedEventCallback(event);
      } else if (event is StreamNegotiationsDoneEvent) {
        negotiationDone = true;
        log.t('xmpp stream [=== negotiationDone:$negotiationDone resumed:${event.resumed}]');
      } else if (event is ResourceBoundEvent) {
        log.t('xmpp stream [=== resource:${event.resource}]');
      } else if (event is StreamManagementEnabledEvent) {
        log.t('xmpp stream [=== resource:${event.resource} id:${event.id} location:${event.location}]');
      }
    });
    final result = await xmppConnection!.connect(
      shouldReconnect: true,
      waitForConnection: true,
      waitUntilLogin: true,
    );
    if (result.isType<XmppError>()) {
      log.e('XMPP Connection Failed: ${result.get<XmppError>()}');
      return false;
    }

    await userOnline();
    await retrieveChatHistory();

    return true;
  }

  /// moxxmpp package doesn't send "open session" stanza, especially the one used for XMPP
  /// session establishment as defined in RFC3921, because this has already been superseded by
  /// RFC6121. So if mongooseIM server has 'backwards_compatible_session=true', then it will hit
  /// 'packat_before_session_establish_sent' and no session will be created by mongooseIM 6.1.2 server.
  ///
  /// So if we no choice must use back backwards_compatible_session=true, then need to extend
  /// StreamManagementNegotiator to send this before sending <enable> stanza.
  Future<void> openSession() async {
    XMLNode opensessionStanza = XMLNode.xmlns(
      tag: 'iq',
      xmlns: 'jabber:client',
      attributes: {
        'type': 'set',
        'id': const Uuid().v4(),
      },
      children: [
        XMLNode.xmlns(
          tag: 'session',
          xmlns: 'urn:ietf:params:xml:ns:xmpp-session',
        ),
      ],
    );
    xmppConnection!.sendRawXML(opensessionStanza);
  }

  Future<void> dispose() async {
    await userOffline();
    await xmppConnection?.disconnect();
    xmppConnection = null;
  }

  Future<void> userOnline() async {
    bool waitResult = await waitForCondition(() async {
      return xmppConnectionState == XmppConnectionState.connected && negotiationDone;
    }, timeout: const Duration(seconds: 3));
    if (!waitResult) return;
    PresenceManager presenceManager = xmppConnection!.getPresenceManager()!;
    await presenceManager.sendInitialPresence();
  }

  Future<void> userOffline() async {
    PresenceManager? presenceManager = xmppConnection?.getPresenceManager();
    await presenceManager?.sendUnavailablePresence();
  }

  Future<void> retrieveChatHistory() async {
    bool waitResult = await waitForCondition(() async {
      return xmppConnectionState == XmppConnectionState.connected && negotiationDone;
    }, timeout: const Duration(seconds: 3));
    if (!waitResult) {
      log.e('Cannot retrieve chat history. XMPP connection not ready.');
    }
    MessageArchiveManagementManager msgArchManager =
        xmppConnection!.getManagerById<MessageArchiveManagementManager>(mamManager)!;
    Result<MAMError, int> result = await msgArchManager.requestMessages(_myJid!, afterId: 'CEEQN13BH6G1', pageSize: 3);
    if (result.isType<MAMError>()) {
      log.e('Failed to retrieve chat history. result=$result');
    }
    log.d('Retrieved chat history. Expecting ${result.get<int>()} messages.');
  }
}

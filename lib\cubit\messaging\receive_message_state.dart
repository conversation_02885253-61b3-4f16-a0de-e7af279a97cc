part of 'receive_message_cubit.dart';

sealed class ReceiveMessageState extends Equatable {
  final String jid;
  final String name;
  final String message;
  final bool isGroup;

  const ReceiveMessageState({
    this.jid = '',
    this.name = '',
    this.message = '',
    this.isGroup = false,
  });

  @override
  List<Object> get props => [
        jid,
        name,
        message,
        isGroup,
      ];
}

class ReceiveMessageInitial extends ReceiveMessageState {
  const ReceiveMessageInitial();
}

class ReceiveMessageUpdated extends ReceiveMessageState {
  const ReceiveMessageUpdated({
    required super.jid,
    required super.name,
    required super.message,
    required super.isGroup,
  });
}

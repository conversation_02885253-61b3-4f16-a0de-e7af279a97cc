import 'package:ddone/constants/value_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/ntp_datetime_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';

class DeclineChatBubble extends StatefulWidget {
  final String groupOnly;
  final bool isMe;
  // final List<String> inviteSplit;
  final String groupName;
  // static TextEditingController declinereasonEditingController = TextEditingController();
  final DateTime date;
  final String time;
  final String body;

  const DeclineChatBubble({
    required this.groupOnly,
    required this.isMe,
    // required this.inviteSplit,
    required this.groupName,
    required this.date,
    required this.time,
    required this.body,
    super.key,
  });

  @override
  State<DeclineChatBubble> createState() => _DeclineChatBubbleState();
}

class _DeclineChatBubbleState extends State<DeclineChatBubble> {
  // TextEditingController declinereasonEditingController = TextEditingController();
  late TimeService ntpTime;

  @override
  void initState() {
    super.initState();

    ntpTime = sl.get<TimeService>();
    debugPrint('ntpTime: ${ntpTime.ntpTime}');
  }

  // final String

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final textTheme = themeState.themeData.textTheme;
        final declineIdRegex = RegExp(r'.*\[~InvitationDeclined~\]');
        final replaced = widget.body.replaceAll(declineIdRegex, '');
        final replacedSplit = replaced.split(widget.groupName);
        final text = replacedSplit[0];
        final textAfterSplit = replaced.split(widget.groupOnly);
        final textAfter = textAfterSplit[1];

        return BlocBuilder<BookmarkListCubit, BookmarkListState>(
          builder: (context, state) {
            return Column(
              children: [
                if (widget.body.contains(invitationDeclineRegex) && widget.groupName.isNotEmpty)
                  textAfter.isEmpty
                      ? Text(
                          text + widget.groupName,
                          style: textTheme.labelLarge?.copyWith(
                            color: Colors.white54,
                            fontWeight: FontWeight.w500,
                          ),
                        )
                      : Text(
                          text + widget.groupName + textAfter,
                          style: textTheme.labelLarge?.copyWith(
                            color: Colors.white54,
                            fontWeight: FontWeight.w500,
                          ),
                        ),

                // if (!state.bookmarkList.toString().contains(widget.groupOnly) &&
                //     !widget.isMe &&
                //     widget.date.isBefore(ntpTime.ntpTime.yyyyMMdd()))
                //   Text(
                //     "Invitation Expired",
                //     style: textTheme.labelLarge?.copyWith(
                //       color: widget.isMe ? Colors.black87 : Colors.white70,
                //       fontWeight: FontWeight.w700,
                //     ),
                //   ),
                // Card(
                //   color: Colors.amber[800],
                //   elevation: 5,
                //   child: Padding(
                //     padding: const EdgeInsets.all(10.0),
                //     child: Row(
                //       mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                //       children: [
                //         Icon(
                //           Icons.people,
                //           color: colorTheme.backgroundColor,
                //           size: 35,
                //         ),
                //         const SizedBox(
                //           width: 10,
                //         ),
                //         Text(
                //           widget.groupName.toString(),
                //           style: textTheme.bodyMedium?.copyWith(
                //             color: widget.isMe ? Colors.black : Colors.white70,
                //             fontWeight: FontWeight.w500,
                //           ),
                //           maxLines: null,
                //         ),
                //       ],
                //     ),
                //   ),
                // ),
                // !isMe?
                // state.bookmarkList.toString().contains(widget.groupOnly) || widget.isMe
                //     // || widget.date.isBefore(ntpTime.ntpTime.yyyyMMdd())
                //     ? Container()
                //     : Row(
                //         mainAxisAlignment: MainAxisAlignment.center,
                //         children: [
                //           Tooltip(
                //             message: 'Decline',
                //             child: ElevatedButton(
                //               onPressed: () {
                //                 showDialog(
                //                   context: context,
                //                   builder: (context) {
                //                     return AlertDialog(
                //                       backgroundColor: colorTheme.backgroundColor,
                //                       title: const Center(
                //                         child: Text(
                //                           'Confirm to decline?',
                //                           style: TextStyle(
                //                             color: Colors.white,
                //                             fontSize: 17,
                //                           ),
                //                         ),
                //                       ),
                //                       content: Column(
                //                         mainAxisSize: MainAxisSize.min,
                //                         children: [
                //                           TextField(
                //                             style: const TextStyle(
                //                               color: Colors.white,
                //                             ),
                //                             cursorColor: colorTheme.primaryColor,
                //                             controller: DeclineChatBubble.declinereasonEditingController,
                //                             decoration: InputDecoration(
                //                               hintText: 'Reason',
                //                               hintStyle: textTheme.bodyLarge!.copyWith(
                //                                 color: Colors.white54,
                //                                 fontWeight: FontWeight.w500,
                //                               ),
                //                               // TextStyle(
                //                               //   color: Colors.white54,
                //                               // ),
                //                               // filled: true,
                //                               // fillColor: greyBackground,
                //                               enabledBorder: const OutlineInputBorder(
                //                                 borderSide: BorderSide(
                //                                   color: Colors.white70,
                //                                 ),
                //                               ),
                //                               focusedBorder: const OutlineInputBorder(
                //                                 borderSide: BorderSide(
                //                                   color: Colors.white70,
                //                                 ),
                //                               ),
                //                             ),
                //                           ),
                //                           SizedBox(
                //                             height: 10,
                //                           ),
                //                           Row(
                //                             mainAxisAlignment: MainAxisAlignment.center,
                //                             children: [
                //                               const TextButton(
                //                                 onPressed: pop,
                //                                 child: Text(
                //                                   "Cancel",
                //                                   style: TextStyle(
                //                                     color: Colors.red,
                //                                   ),
                //                                 ),
                //                               ),
                //                               TextButton(
                //                                 onPressed: () {},
                //                                 //  widget.onDeclinePressed,
                //                                 // onPressed: () {
                //                                 //   declineGroupJoin(
                //                                 //     JID.fromString(groupOnly),
                //                                 //     JID.fromString(ChatPage.receiver.toString()),
                //                                 //     declinereasonEditingController.text,
                //                                 //   );
                //                                 // },
                //                                 child: const Text(
                //                                   "Confirm",
                //                                   style: TextStyle(
                //                                     color: Colors.green,
                //                                   ),
                //                                 ),
                //                               ),
                //                             ],
                //                           ),
                //                         ],
                //                       ),
                //                     );
                //                   },
                //                 );
                //               },
                //               style: ButtonStyle(
                //                 maximumSize: MaterialStateProperty.all(
                //                   const Size(100, 40),
                //                 ),
                //                 padding: MaterialStateProperty.all(
                //                   const EdgeInsets.symmetric(
                //                     horizontal: 10,
                //                     vertical: 5,
                //                   ),
                //                 ),
                //               ),
                //               child: Icon(
                //                 Icons.close,
                //                 color: Colors.redAccent[400],
                //               ),
                //             ),
                //           ),
                //           const SizedBox(
                //             width: 5,
                //           ),
                //           Tooltip(
                //             message: 'Accept',
                //             child: ElevatedButton(
                //               onPressed: () {},
                //               // widget.onAcceptPressed,

                //               // () async {
                //               //   print('$groupOnly');
                //               //   await joinChatRoom(
                //               //     groupOnly.toString(),
                //               //     'testing2',
                //               //   );
                //               //   groupUiCubit.update();
                //               // },
                //               style: ButtonStyle(
                //                 maximumSize: MaterialStateProperty.all(
                //                   const Size(100, 40),
                //                 ),
                //                 padding: MaterialStateProperty.all(
                //                   const EdgeInsets.symmetric(
                //                     horizontal: 10,
                //                     vertical: 5,
                //                   ),
                //                 ),
                //               ),
                //               child: Icon(
                //                 Icons.check,
                //                 color: Colors.greenAccent[400],
                //               ),
                //             ),
                //           ),
                //         ],
                //       ),
                // : Container(),
              ],
            );
          },
        );
      },
    );
  }
}

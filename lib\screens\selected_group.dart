import 'package:ddone/components/button/round_shape_ink_well.dart';
import 'package:ddone/components/chat_category/chat_category.dart';
import 'package:ddone/components/common/common_card.dart';
import 'package:ddone/components/common/round_shape_avatar.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/constants/regex_constants.dart';
import 'package:ddone/cubit/chat_recent/chat_recent_cubit.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/cubit/home/<USER>';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';
import 'package:ddone/cubit/selected_group/selected_group_cubit.dart';
import 'package:ddone/cubit/update/info_cubit.dart';
import 'package:ddone/utils/extensions/context_ext.dart';
import 'package:ddone/utils/extensions/string_ext.dart';
import 'package:ddone/utils/page_view_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';

class SelectedGroup extends StatefulWidget {
  static const routeName = '/selectedGroup';

  const SelectedGroup({super.key});

  @override
  State<SelectedGroup> createState() => _SelectedGroupState();
}

class _SelectedGroupState extends State<SelectedGroup> {
  late SelectedGroupCubit _selectedGroupCubit;
  late HomeCubit _homeCubit;
  late InfoCubit _infoCubit;
  late MamListCubit _mamListCubit;
  late LoginCubit _loginCubit;
  // late MemberCubit _memberCubit;
  late ChatRecentCubit _chatRecentCubit;

  @override
  void initState() {
    super.initState();

    _selectedGroupCubit = BlocProvider.of<SelectedGroupCubit>(context);
    _homeCubit = BlocProvider.of<HomeCubit>(context);
    _infoCubit = BlocProvider.of<InfoCubit>(context);
    _mamListCubit = BlocProvider.of<MamListCubit>(context);
    _loginCubit = BlocProvider.of<LoginCubit>(context);
    // _memberCubit = BlocProvider.of<MemberCubit>(context);
    _chatRecentCubit = BlocProvider.of<ChatRecentCubit>(context);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;

        return Scaffold(
          appBar: AppBar(
            backgroundColor: context.colorTheme().surface,
            leading: isDesktop
                ? IconButton(
                    splashRadius: context.deviceHeight(0.035),
                    onPressed: () {
                      _selectedGroupCubit.removeSelectedGroupInfo();
                    },
                    icon: Icon(
                      Icons.close,
                      color: colorTheme.primaryColor,
                    ),
                  )
                : null,
            title: BlocBuilder<ThemeCubit, ThemeState>(
              builder: (context, themeState) {
                final colorTheme = themeState.colorTheme;
                final textTheme = themeState.themeData.textTheme;

                return Center(
                  child: Text(
                    'Group Details',
                    style: textTheme.headlineLarge!.copyWith(color: colorTheme.primaryColor),
                  ),
                );
              },
            ),
          ),
          body: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: maxWidthForDesktop),
              child: BlocBuilder<SelectedGroupCubit, SelectedGroupState>(
                builder: (context, selectedGroupState) {
                  final groupNameReplaced = selectedGroupState.groupJid.mucIDFilter();
                  final groupNameFilter = groupNameRegex.firstMatch(groupNameReplaced);
                  final String? groupNameOnly = groupNameFilter?.group(0);
                  final String groupNameOnlyReplaced = groupNameReplaced.replaceAll(groupNameOnly ?? '', '');

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      const Center(
                        child: RoundShapeAvatar(
                          icon: Icons.group,
                        ),
                      ),
                      CommonCard(
                        title: 'Group Name',
                        desc: groupNameOnlyReplaced,
                      ),
                      CommonCard(
                        title: 'Group Id',
                        desc: selectedGroupState.groupJid,
                      ),
                      Padding(
                        padding: EdgeInsets.all(context.deviceHeight(0.05)),
                        child: RoundShapeInkWell(
                          onTap: () {
                            _homeCubit.jumpToPageWithName(PageViewNameEnum.recentChat);
                            _chatRecentCubit.setPageIndex(kGroupChatsIndex);
                            _infoCubit.getGroupChatHistory(
                              receiver: selectedGroupState.groupJid,
                              nick: selectedGroupState.nick,
                              name: selectedGroupState.groupName,
                              loginCubit: _loginCubit,
                              mamListCubit: _mamListCubit,
                              // memberCubit: _memberCubit,
                            );
                          },
                          contentWidget: Icon(
                            Icons.message,
                            color: colorTheme.roundShapeInkWellColor,
                            size: iconSizeLarge,
                          ),
                          color: colorTheme.primaryColor,
                        ),
                      )
                    ],
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }
}

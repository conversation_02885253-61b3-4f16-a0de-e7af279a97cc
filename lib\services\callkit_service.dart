import 'package:ddone/constants/callkit_constants.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_callkit_incoming/entities/android_params.dart';
import 'package:flutter_callkit_incoming/entities/call_event.dart';
import 'package:flutter_callkit_incoming/entities/call_kit_params.dart';
import 'package:flutter_callkit_incoming/entities/ios_params.dart';
import 'package:flutter_callkit_incoming/entities/notification_params.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';
import 'package:uuid/uuid.dart';

enum CallDirection {
  outgoing,
  incoming;
}

class CallkitService with PrefsAware {
  String? _uuid;
  bool _ringing = false;
  CallDirection? _callDirection;
  bool acceptedCall = false;

  CallkitService();

  bool get isRinging => _ringing;
  String? get uuid => _uuid;

  Future<bool> _isAndroid14OrNewer() async {
    if (isAndroid) {
      final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      // Android 14 is API Level 34
      return androidInfo.version.sdkInt >= 34;
    }
    return false; // Not an Android device
  }

  /// For VoIP app, as long as we declare USE_FULL_SCREEN_INTENT in AndroidManifest.xml,
  /// then it will be granted by default upon installation. No need to explicitly request for it.
  /// Even if user revoke the permission, it won't break. Only the incoming call notifications will
  /// no longer be displayed as full-screen intent.
  Future<bool> requestCallKitFullIntentPermission(BuildContext context) async {
    if (await _isAndroid14OrNewer() && context.mounted) {
      log.t('requestCallKitFullIntentPermission - checking permission status');

      // Use SharedPreferences to track if we've already requested permission
      bool hasRequestedBefore = prefs.getBool(CacheKeys.fullIntentPermissionRequested) ?? false;

      if (!hasRequestedBefore) {
        log.t('First time requesting full screen intent permission');
        try {
          // Add timeout to prevent hanging
          await FlutterCallkitIncoming.requestFullIntentPermission().timeout(const Duration(seconds: 15));

          // Mark as requested regardless of result to prevent repeated requests
          await prefs.setBool(CacheKeys.fullIntentPermissionRequested, true);
          return true;
        } catch (e) {
          log.w('Error requesting full intent permission: $e');
          // Mark as requested even if it failed to prevent repeated attempts
          await prefs.setBool(CacheKeys.fullIntentPermissionRequested, true);
          return false;
        }
      } else {
        log.t('Full screen intent permission already requested before, skipping');
        return true;
      }
    }
    return true;
  }

  /// Reset the full intent permission request flag (useful for testing or if user wants to be asked again)
  Future<void> resetFullIntentPermissionFlag() async {
    await prefs.remove(CacheKeys.fullIntentPermissionRequested);
    log.d('Full intent permission request flag reset');
  }

  Future<void> init({
    Function()? incomingCallCallback,
    Function()? outgoingCallCallback,
    Function(CallDirection?)? acceptedCallCallback,
    Function(CallDirection?)? declinedCallCallback,
    Function()? endedCallCallback,
    Function()? missedCallCallback,
    Function()? callBackCallback,
    Function()? iosToggleHoldCallCallback,
    Function(bool)? iosToggleMuteCallCallback,
    Function()? iosToggleDmtfCallback,
    Function()? iosToggleGroupCallback,
    Function(bool)? iosToggleAudioSessionCallback,
    Function()? iosUpdateDevicePushTokenVoipCallback,
  }) async {
    if (!isMobile) return;

    FlutterCallkitIncoming.onEvent.listen((CallEvent? event) {
      if (event == null) return;
      log.t('FlutterCallkitIncoming event:${event.toString()}');
      switch (event.event) {
        case Event.actionCallIncoming:
          // received an incoming call
          _ringing = true;
          Map extra = event.body['extra'] as Map;
          if (extra.containsKey('iosUuid') && extra['iosUuid'] != null) {
            // set uuid for ios incoming call from Native Swift
            _uuid = event.body['extra']['iosUuid'];
            _callDirection = CallDirection.incoming;
          } else if (uuid == null) {
            // set uuid for android incoming call from fcm background isolate
            setActiveCallUuid();
          }
          if (incomingCallCallback != null) incomingCallCallback();
          break;
        case Event.actionCallStart:
          // started an outgoing call
          if (outgoingCallCallback != null) outgoingCallCallback();
          break;
        case Event.actionCallAccept:
          // accepted an incoming call
          _ringing = false;
          if (acceptedCallCallback != null && !acceptedCall) acceptedCallCallback(_callDirection);
          acceptedCall = true;
          break;
        case Event.actionCallDecline:
          // declined an incoming call
          _ringing = false;
          _uuid = null;
          if (declinedCallCallback != null) declinedCallCallback(_callDirection);
          break;
        case Event.actionCallEnded:
          // ended an incoming/outgoing call
          acceptedCall = false;
          _uuid = null;
          if (endedCallCallback != null) endedCallCallback();
          break;
        case Event.actionCallTimeout:
          // missed an incoming call
          _ringing = false;
          _uuid = null;
          if (missedCallCallback != null) missedCallCallback();
          break;
        case Event.actionCallCallback:
          // only Android - click action `Call back` from missed call notification
          if (callBackCallback != null) callBackCallback();
          break;
        case Event.actionCallToggleHold:
          // only iOS
          if (iosToggleHoldCallCallback != null) iosToggleHoldCallCallback();
          break;
        case Event.actionCallToggleMute:
          // only iOS
          bool isMuted = event.body['isMuted'] as bool;
          if (iosToggleMuteCallCallback != null) iosToggleMuteCallCallback(isMuted);
          break;
        case Event.actionCallToggleDmtf:
          // only iOS
          if (iosToggleDmtfCallback != null) iosToggleDmtfCallback();
          break;
        case Event.actionCallToggleGroup:
          // only iOS
          if (iosToggleGroupCallback != null) iosToggleGroupCallback();
          break;
        case Event.actionCallToggleAudioSession:
          // only iOS
          bool isActivate = event.body['isActivate'] as bool;
          if (iosToggleAudioSessionCallback != null) iosToggleAudioSessionCallback(isActivate);
          break;
        case Event.actionDidUpdateDevicePushTokenVoip:
          // only iOS
          if (iosUpdateDevicePushTokenVoipCallback != null) iosUpdateDevicePushTokenVoipCallback();
          break;
        case Event.actionCallCustom:
          // for custom action
          break;
      }
    });

    log.t('callkitService init completed');
  }

  /// Android and iOS
  Future<void> incomingCall(String callerName, String callerId) async {
    if (!isMobile) return;

    // We don't support multiple calls at once
    bool hasActiveCall = await hasActiveCalls();
    if (hasActiveCall) return;

    _uuid = const Uuid().v4();

    CallKitParams params = CallKitParams(
      id: _uuid,
      nameCaller: callerName,
      appName: 'DDOne',
      handle: callerId,
      type: 0,
      duration: callkitExpire,
      textAccept: 'Accept',
      textDecline: 'Decline',
      missedCallNotification: const NotificationParams(
        showNotification: true,
        isShowCallback: false,
        subtitle: 'Missed call',
        callbackText: 'Call back',
      ),
      extra: <String, dynamic>{
        'caller': callerName,
        'callerId': callerId,
      },
      // headers: <String, dynamic> {},
      android: const AndroidParams(
        isCustomNotification: true,
        isCustomSmallExNotification: false,
        isShowLogo: false,
        isShowCallID: false,
        ringtonePath: 'system_ringtone_default',
        // ringtonePath: 'incoming_call2',
        backgroundColor: '#0955fa',
        // backgroundUrl: 'https://i.pravatar.cc/500',
        actionColor: '#4CAF50',
        textColor: '#ffffff',
        incomingCallNotificationChannelName: 'Incoming Call',
        missedCallNotificationChannelName: 'Missed Call',
        isShowFullLockedScreen: true,
        isImportant: false,
        isBot: false,
      ),
      ios: const IOSParams(
        iconName: 'CallKitLogo',
        handleType: 'generic',
        supportsVideo: false,
        maximumCallGroups: 1,
        maximumCallsPerCallGroup: 1,
        audioSessionMode: 'voiceChat',
        audioSessionActive: true,
        audioSessionPreferredSampleRate: 44100.0,
        audioSessionPreferredIOBufferDuration: 0.005,
        supportsDTMF: false,
        supportsHolding: true,
        supportsGrouping: false,
        supportsUngrouping: false,
        ringtonePath: 'system_ringtone_default',
        // ringtonePath: 'system_ringtone_default',
      ),
    );
    _ringing = true;
    _callDirection = CallDirection.incoming;
    await FlutterCallkitIncoming.showCallkitIncoming(params);
  }

  /// Android and iOS
  Future<void> showMissCall(String callerName, String callerId) async {
    if (!isMobile) return;
    if (uuid == null) return;
    CallKitParams params = CallKitParams(
      id: _uuid,
      nameCaller: callerName,
      handle: callerId,
      type: 0,
      missedCallNotification: const NotificationParams(
        showNotification: true,
        isShowCallback: false,
        subtitle: 'Missed call',
        callbackText: 'Call back',
      ),
      android: const AndroidParams(
        isCustomNotification: true,
        isShowCallID: false,
      ),
      extra: <String, dynamic>{
        'caller': callerName,
        'callerId': callerId,
      },
    );
    await FlutterCallkitIncoming.showMissCallNotification(params);
  }

  /// Android
  Future<void> hideNotificationCall() async {
    if (!isMobile) return;
    if (_uuid == null) return;
    CallKitParams params = CallKitParams(
      id: _uuid,
    );
    await FlutterCallkitIncoming.hideCallkitIncoming(params);
  }

  /// Android and iOS
  Future<void> outgoingCall(String callerName, String callerId) async {
    if (!isMobile) return;

    _uuid = const Uuid().v4();

    CallKitParams params = CallKitParams(
        id: _uuid,
        nameCaller: callerName,
        handle: callerId,
        type: 0,
        extra: <String, dynamic>{
          'caller': callerName,
          'callerId': callerId,
        },
        ios: const IOSParams(handleType: 'generic'),
        android: const AndroidParams(
          isCustomNotification: true,
          isShowCallID: true,
        ));
    _callDirection = CallDirection.outgoing;
    await FlutterCallkitIncoming.startCall(params);
  }

  /// Android and iOS
  Future<void> endCall() async {
    if (!isMobile) return;
    if (_uuid == null) return;
    await FlutterCallkitIncoming.endCall(_uuid!);
  }

  /// Android and iOS
  Future<void> endAllCall() async {
    if (!isMobile) return;
    await FlutterCallkitIncoming.endAllCalls();
  }

  /// Android and iOS
  Future<dynamic> getActiveCalls() async {
    if (!isMobile) return null;
    dynamic activeCalls = await FlutterCallkitIncoming.activeCalls();
    log.t('callkitService - getActiveCalls:$activeCalls');
    return activeCalls;
  }

  /// iOS
  /// - this is required after call connection is establised
  Future<void> connectedCall() async {
    if (!isIOS) return;
    if (_uuid == null) return;
    await FlutterCallkitIncoming.setCallConnected(_uuid!);
  }

  /// iOS
  Future<String?> getDevicePushTokenVoIP() async {
    if (!isIOS) return null;
    dynamic iosVoipToken = await FlutterCallkitIncoming.getDevicePushTokenVoIP();
    return iosVoipToken as String;
  }

  Future<bool> hasActiveCalls() async {
    dynamic activeCalls = await getActiveCalls();
    if (activeCalls is! List || activeCalls.isNotEmpty) return true;
    return false;
  }

  Future<void> setActiveCallUuid() async {
    dynamic activeCalls = await getActiveCalls();
    if (activeCalls is List && activeCalls.isNotEmpty) {
      _uuid ??= (activeCalls[0] as Map)['id'] as String;
    }
  }

  Future<Map?> getCallExtraInfo() async {
    dynamic activeCalls = await getActiveCalls();
    if (activeCalls is List && activeCalls.isNotEmpty) {
      return (activeCalls[0] as Map)['extra'] as Map;
    }
    return null;
  }

  /// Retrieves information about the current caller.
  ///
  /// This function asynchronously fetches call-related details using `getCallExtraInfo()`.
  /// It then extracts the `callerId` and `caller` name from the retrieved information.
  /// If `getCallExtraInfo()` returns `null`, default empty strings are used for
  /// both `callerId` and `caller`.
  ///
  /// Returns a `Future<Map<String, String>>` containing:
  /// - `'callerId'`: The ID of the caller (String).
  /// - `'caller'`: The name of the caller (String).
  ///
  /// Example:
  /// ```dart
  /// Map<String, String> info = await getCallerInfo();
  /// print('Caller ID: ${info['callerId']}');
  /// print('Caller Name: ${info['caller']}');
  /// ```
  Future<Map<String, String>> getCallerInfo() async {
    Map? callInfo = await getCallExtraInfo();
    String callerId = '';
    String caller = '';
    if (callInfo != null) {
      callerId = callInfo['callerId'] as String? ?? '';
      caller = callInfo['caller'] as String? ?? '';
    }
    return {
      'callerId': callerId,
      'caller': caller,
    };
  }
}

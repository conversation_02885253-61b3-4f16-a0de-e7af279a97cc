// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'login_cubit.dart';

@Deprecated('Use XmppConnectionState')
class LoginState extends Equatable {
  const LoginState();

  @override
  List<Object> get props => [];
}

@Deprecated('Use XmppConnectionInitial')
class LoginInitial extends LoginState {}

@Deprecated('Use XmppConnectionLoading')
class LoginLoading extends LoginState {}

@Deprecated('Use XmppConnectionSuccess')
class LoginAuthenticated extends LoginState {
  final XmppConnection connection;

  const LoginAuthenticated({
    required this.connection,
  });

  @override
  String toString() => 'LoginAuthenticated(connection: $connection)';
}

@Deprecated('Use XmppConnectionFailed')
class LoginAuthenticationFailed extends LoginState {
  final String error;
  const LoginAuthenticationFailed({
    required this.error,
  });
}

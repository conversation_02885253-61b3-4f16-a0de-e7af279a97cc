part of 'mam_list_cubit.dart';

sealed class MamListState extends Equatable {
  final Map<String, List<MamInfo>> mamList;
  final Map<String, List<MamInfo>> mamgroupList;
  final List<Map<String, dynamic>> mamFullList;
  final List<Map<String, dynamic>> filteredMamFullList;
  final List<ContactModel> contactModelList;
  final List<ContactModel> filteredContactModelList;
  final List<ConferenceInfo> bookmarkModelList;
  final List<ConferenceInfo> filterBookmarkModelList;

  const MamListState({
    this.mamList = const {},
    this.mamgroupList = const {},
    this.mamFullList = const [],
    this.filteredMamFullList = const [],
    this.contactModelList = const [],
    this.filteredContactModelList = const [],
    this.bookmarkModelList = const [],
    this.filterBookmarkModelList = const [],
  });

  @override
  List<Object> get props => [
        mamList,
        mamgroupList,
        mamFullList,
        filteredMamFullList,
        contact<PERSON><PERSON><PERSON><PERSON>ist,
        filteredContactModelList,
        bookmarkModelList,
        filterBookmarkModelList,
      ];
}

final class MamListInitial extends MamListState {
  const MamListInitial({
    super.mamList,
    super.mamgroupList,
    super.mamFullList,
    required super.filteredMamFullList,
    super.contactModelList,
    super.filteredContactModelList,
    super.bookmarkModelList,
    super.filterBookmarkModelList,
  });
}

final class MamListLoading extends MamListState {
  const MamListLoading({
    super.mamList,
    super.mamgroupList,
    super.mamFullList,
    required super.filteredMamFullList,
    super.contactModelList,
    super.filteredContactModelList,
    super.bookmarkModelList,
    super.filterBookmarkModelList,
  });
}

final class MamListUpdated extends MamListState {
  const MamListUpdated({
    super.mamList,
    super.mamgroupList,
    super.mamFullList,
    required super.filteredMamFullList,
    super.contactModelList,
    super.filteredContactModelList,
    super.bookmarkModelList,
    super.filterBookmarkModelList,
  });
}

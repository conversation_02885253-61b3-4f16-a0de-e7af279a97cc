import 'package:azlistview/azlistview.dart';
import 'package:ddone/models/contact_model.dart';

/// Wrapper class for ContactModel to work with AzListView
class ContactAzItem extends ISuspensionBean {
  final ContactModel contactModel;
  final String tag;

  ContactAzItem({
    required this.contactModel,
    required this.tag,
  });

  @override
  String getSuspensionTag() => tag;

  /// Factory method to create ContactAzItem from ContactModel
  factory ContactAzItem.fromContactModel(ContactModel contactModel) {
    String displayName = contactModel.displayName.trim();
    String tagValue;

    if (displayName.isEmpty) {
      tagValue = '#';
    } else {
      String firstChar = displayName[0].toUpperCase();
      if (!RegExp(r'^[A-Z]$').hasMatch(firstChar)) {
        tagValue = '#'; // Group non-alphabetic characters under '#'
      } else {
        tagValue = firstChar;
      }
    }

    return ContactAzItem(
      contactModel: contactModel,
      tag: tagValue,
    );
  }

  /// Create a list of ContactAzItem from a list of ContactModel
  static List<ContactAzItem> fromContactModelList(List<ContactModel> contacts) {
    // Filter out invalid contacts
    final validContacts =
        contacts.where((contact) => contact.displayName.isNotEmpty && contact.contactEntries.isNotEmpty).toList();

    if (validContacts.isEmpty) {
      return [];
    }

    final List<ContactAzItem> azItems =
        validContacts.map((contact) => ContactAzItem.fromContactModel(contact)).toList();

    // Sort by display name
    azItems.sort((a, b) {
      final tagA = a.tag;
      final tagB = b.tag;

      // Rule 1: '#' comes before any alphabetic tag
      if (tagA == '#' && tagB != '#') return -1;
      if (tagA != '#' && tagB == '#') return 1;

      // Rule 2: If tags are the same (both '#' or both same alphabetic letter)
      // then sort by display name
      if (tagA == tagB) {
        final nameA = a.contactModel.displayName.toLowerCase().trim();
        final nameB = b.contactModel.displayName.toLowerCase().trim();

        // Secondary sort for empty names within the '#' group, if any persisted somehow
        // (though fromContactModel should tag empty displayNames as '#')
        if (nameA.isEmpty && nameB.isEmpty) return 0;
        if (nameA.isEmpty) return 1; // Empty name to the end of this specific group
        if (nameB.isEmpty) return -1;

        return nameA.compareTo(nameB);
      }

      // Rule 3: If tags are different and NEITHER is '#' (e.g., 'A' vs 'B')
      // then sort by tag alphabetically
      return tagA.compareTo(tagB);
    });

    // Set suspension tags - add try-catch for safety
    try {
      SuspensionUtil.setShowSuspensionStatus(azItems);
    } catch (e) {
      // Manually ensure the first item of the sorted list (which could be '#' or 'A')
      // is marked to show suspension, and then subsequent different tags.
      // For simplicity, just marking the first:
      if (azItems.isNotEmpty) {
        azItems[0].isShowSuspension = true;
      }
      // A more robust fallback would re-implement parts of setShowSuspensionStatus logic.
      // However, SuspensionUtil should generally work if data is sorted.
      // The azlistview package's ISuspensionBean should have isShowSuspension.
      // If you directly implement ISuspensionBean, ensure it has a settable isShowSuspension.
    }

    return azItems;
  }

  /// Get unique alphabetic tags from contact list, with '#' first
  static List<String> getAlphabeticTags(List<ContactAzItem> contacts) {
    if (contacts.isEmpty) {
      return [];
    }

    final Set<String> tagSet = contacts.map((item) => item.tag).toSet();
    final List<String> tags = tagSet.toList();

    // Sort tags: '#' first, then A-Z
    tags.sort((a, b) {
      if (a == '#' && b != '#') return -1; // '#' comes first
      if (a != '#' && b == '#') return 1; // '#' comes first
      // For other tags (A-Z), sort alphabetically
      return a.compareTo(b);
    });

    return tags;
  }
}

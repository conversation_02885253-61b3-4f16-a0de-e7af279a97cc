import 'package:flutter/material.dart';
import 'package:flutter_context_menu/flutter_context_menu.dart';

const chatPopUpRightClickMenu = <ContextMenuEntry>[
  MenuItem(
    label: 'Delete',
    value: 'Delete',
    icon: Icons.delete_forever_outlined,
  ),
  // MenuItem(
  //   label: 'Pin',
  //   value: 'Pin',
  //   icon: Icons.push_pin_outlined,
  // ),
];

const chatPopUpRightClickMenuNotOwner = <ContextMenuEntry>[
  // MenuItem(
  //   label: 'Pin',
  //   value: 'Pin',
  //   icon: Icons.push_pin_outlined,
  // ),
];

const defaultContextMenuItems = <ContextMenuEntry>[
  MenuItem.submenu(
    label: "New",
    icon: Icons.add_rounded,
    items: [
      MenuItem(
        label: "Node",
        value: "Node",
      ),
      MenuItem(
        label: "Item",
        value: "Item",
      ),
      MenuItem(
        label: "Group",
        value: "Group",
      ),
    ],
  ),
  MenuItem(
    label: "Open...",
    value: "Open...",
    icon: Icons.file_open_rounded,
  ),
  MenuItem.submenu(
    label: "View",
    icon: Icons.view_comfy_alt_rounded,
    items: [
      MenuHeader(text: "Visibility"),
      MenuItem(
        label: "Comapct",
        value: "Comapct",
        icon: Icons.view_compact_rounded,
      ),
      MenuItem(
        label: "Comfortable",
        value: "Comfortable",
        icon: Icons.view_comfortable_rounded,
      ),
      MenuDivider(),
      MenuItem.submenu(label: "Show Mini Map", icon: Icons.screen_search_desktop_rounded, items: [
        MenuItem(
          label: "Show",
          value: "Show",
        ),
        MenuItem(
          label: "Hide",
          value: "Hide",
        ),
        MenuItem.submenu(label: "Position", items: [
          MenuItem(
            label: "Left",
            value: "Left",
          ),
          MenuItem(
            label: "Right",
            value: "Right",
          ),
          MenuItem(
            label: "Top",
            value: "Top",
          ),
          MenuItem(
            label: "Bottom",
            value: "Bottom",
          ),
          MenuItem(
            label: "Center",
            value: "Center",
          ),
        ]),
      ]),
    ],
  ),
];

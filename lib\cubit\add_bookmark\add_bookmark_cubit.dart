import 'package:bloc/bloc.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:moxxmpp/model/conference_info.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'add_bookmark_state.dart';

/// Not sure what this cubit is for. It only emit the state but none of the state be being consumed.
class AddBookmarkCubit extends Cubit<AddBookmarkState> {
  final SharedPreferences _sharedPreferences;
  final Logger log;
  AddBookmarkCubit._()
      : _sharedPreferences = sl.get<SharedPreferences>(),
        log = sl.get<Logger>(),
        super(AddBookmarkInitial(const []));

  factory AddBookmarkCubit.initial() {
    return AddBookmarkCubit._();
  }

  Future<void> addPendingBookmark(
    String name,
    String autojoin,
    String jid,
    String nick,
  ) async {
    try {
      final newPending = ConferenceInfo(name, autojoin, jid, nick);

      final List<ConferenceInfo> pendingList = List.from(state.pendingbookmark)..add(newPending);

      debugPrint('Pending List : $pendingList');

      emit(AddBookmarkPending(pendingList));
    } catch (e) {
      emit(
        AddBookmarkFailed(
          const [],
          error: toString(),
        ),
      );
    }
  }

  Future<void> addBookmark({
    required LoginCubit loginCubit,
    required BookmarkListCubit bookmarkListCubit,
    required GroupUiCubit groupUiCubit,
  }) async {
    try {
      if (loginCubit.state is LoginAuthenticated) {
        emit(AddBookmarkPending(state.pendingbookmark));
        LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;
        final pendingBookmarks = List<ConferenceInfo>.from(state.pendingbookmark);
        debugPrint('State Bookmark Add : ${bookmarkListCubit.state}');

        final bookmarkList = List<ConferenceInfo>.from(bookmarkListCubit.state.bookmarkList);
        debugPrint('HIHI $bookmarkList');

        final pendingSet = pendingBookmarks.map((item) => item.name).toSet();
        final newListSet = bookmarkList.map((item) => item.name).toSet();
        debugPrint('Pending Set : $pendingSet <> New List Set : $newListSet');

        final itemsToAdd = pendingSet.difference(newListSet);

        String? sipNumber = _sharedPreferences.getString(CacheKeys.sipNumber);
        String? sipDomain = _sharedPreferences.getString(CacheKeys.sipDomain);
        var userAtDomain = '${sipNumber!}@${sipDomain!}';

        if (itemsToAdd.isNotEmpty) {
          for (var newItemName in itemsToAdd) {
            final newItem = pendingBookmarks.firstWhere((item) => item.name == newItemName);
            bookmarkList.add(newItem);
            debugPrint('newItem: $newItem ;;; itemsToAdd $itemsToAdd');
          }
        } else {
          debugPrint('All items already exist in pending bookmarks');
        }

        debugPrint('bookmarkListbookmarkList $bookmarkList');

        debugPrint('Add Bookmark New List : ~ $pendingBookmarks');
        debugPrint('New Existing List : ~ $bookmarkList');

        final muc = currentLoginState.connection.getManagerById<MUCManager>(mucManager)!;

        muc.uploadBookmarks(bookmarkList, userAtDomain);

        emit(AddBookmarkDone(bookmarkList));

        debugPrint('Add Bookmark New List : ~ $pendingBookmarks ~ $pendingSet');
      }
    } catch (e) {
      log.e('Failed to add bookmark', error: e);
      emit(
        AddBookmarkFailed(
          state.pendingbookmark,
          error: e.toString(),
        ),
      );
    }
  }
}

import 'package:ddone/components/list_tile/card_list_tile.dart';
import 'package:ddone/constants/dimen_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CardListTileWithTitle extends StatelessWidget {
  final String title, listTileTitle;
  final Icon leadingIcon;
  final bool showTitle;
  final VoidCallback? onTap;
  final Widget? trailingWidget;
  final bool isTileSelected;

  const CardListTileWithTitle({
    required this.title,
    required this.listTileTitle,
    required this.leadingIcon,
    required this.showTitle,
    this.onTap,
    this.trailingWidget,
    this.isTileSelected = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            if (showTitle)
              Padding(
                padding: listTileTitlePadding,
                child: Text(
                  title,
                  maxLines: 1,
                  style: textTheme.titleLarge!.copyWith(
                    color: colorTheme.onPrimaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            CardListTile(
              listTileTitle: listTileTitle,
              leadingIcon: leadingIcon,
              onTap: onTap,
              trailingWidget: trailingWidget,
              isTileSelected: isTileSelected,
            ),
          ],
        );
      },
    );
  }
}

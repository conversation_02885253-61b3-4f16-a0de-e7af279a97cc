// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'info_cubit.dart';

sealed class InfoState {
  const InfoState({
    this.receiver = '',
    this.nick = '',
    this.name = '',
  });
  final String receiver;
  final String nick;
  final String name;

  List<Object> get props => [
        receiver,
        nick,
      ];

  bool isGroupInfo() {
    return receiver.contains('muc');
  }
}

class InfoInitial extends InfoState {
  InfoInitial();
}

class InfoLoading extends InfoState {
  InfoLoading({required super.receiver, required super.nick, super.name});
}

class InfoLoaded extends InfoState {
  InfoLoaded({required super.receiver, required super.nick, super.name});

  InfoLoaded copyWith({
    String? receiver,
    String? nick,
    String? name,
  }) {
    return InfoLoaded(
      receiver: receiver ?? this.receiver,
      nick: nick ?? this.nick,
      name: name ?? this.name,
    );
  }
}

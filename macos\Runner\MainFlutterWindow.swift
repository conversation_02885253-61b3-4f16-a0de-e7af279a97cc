import Cocoa
import FlutterMacOS
import UserNotifications
import LaunchAtLogin

class MainFlutterWindow: NSWindow {
    let flutterViewController = FlutterViewController.init()
    
  override func awakeFromNib() {
    
    let windowFrame = self.frame
    self.contentViewController = flutterViewController
    self.setFrame(windowFrame, display: true)

      setupLaunchAtStartupChannel()
      
      let notificationChannel = FlutterMethodChannel(name: "notificationPlatform", binaryMessenger: flutterViewController.engine.binaryMessenger)
      
      notificationChannel.setMethodCallHandler {(call, result) in
          switch call.method {
          case "requestPermission":
              
              let notificationCenter = UNUserNotificationCenter.current()
              notificationCenter.requestAuthorization(options: [.alert, .sound, .badge] ) {(value, error) in
                  
                  print("requestAuthorization: \(value)")
                  if value {
                      result(true)
                  } else if (!value){
                      result(false)
                  } else {
                      print("request error: \(error?.localizedDescription ?? "Unknown Error")")
                      result(FlutterError(code: "UNAVAILABLE", message: error?.localizedDescription ?? "Error", details: nil))
                  }
                  
              }
              
          case "openNotificationCenter":
              print("openNotificationCenter")
              self.openNotificationCenter()
              result(nil)
              
          case "checkNotificationPermission":
              self.checkNotificationPermission { isGranted in
                  result(isGranted)
              }
              
          default:
              result(FlutterMethodNotImplemented)
          }
      }

    RegisterGeneratedPlugins(registry: flutterViewController)

    super.awakeFromNib()
  }
    
    private func setupLaunchAtStartupChannel(){
        FlutterMethodChannel(
          name: "launch_at_startup", binaryMessenger: flutterViewController.engine.binaryMessenger
        )
        .setMethodCallHandler { (_ call: FlutterMethodCall, result: @escaping FlutterResult) in
          switch call.method {
          case "launchAtStartupIsEnabled":
            result(LaunchAtLogin.isEnabled)
          case "launchAtStartupSetEnabled":
            if let arguments = call.arguments as? [String: Any] {
              LaunchAtLogin.isEnabled = arguments["setEnabledValue"] as! Bool
            }
            result(nil)
          default:
            result(FlutterMethodNotImplemented)
          }
        }
    }
    
    private func openNotificationCenter() {
        let url = URL(string: "x-apple.systempreferences:com.apple.preference.notifications")!
                NSWorkspace.shared.open(url)
    }
    
    private func checkNotificationPermission(completion: @escaping (Bool) -> Void) {
            let center = UNUserNotificationCenter.current()
            center.getNotificationSettings { settings in
                switch settings.authorizationStatus {
                case .authorized, .provisional:
                    completion(true)  // Notifications are enabled
                case .denied, .notDetermined:
                    completion(false) // Notifications are disabled or not determined
                @unknown default:
                    completion(false)
                }
            }
        }
}

import 'package:bloc/bloc.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/xmpp/xmpp_service.dart';
import 'package:ddone/utils/logger_util.dart';
import 'package:equatable/equatable.dart';
import 'package:moxxmpp/moxxmpp.dart' as moxxmpp;

part 'xmpp_connection_state.dart';

/// This should replace login_cubit
/// - login_cubit name is confusing, it clash with auth_cubit.
/// - should use the new XmppService to interact with xmpp server.
class XmppConnectionCubit extends Cubit<XmppConnectionState> with PrefsAware {
  late final XmppService xmppService;

  XmppConnectionCubit._({
    XmppConnectionState? state,
  })  : xmppService = sl.get<XmppService>(),
        super(state ?? XmppConnectionInitial());

  factory XmppConnectionCubit.initial({XmppConnectionState? state}) {
    return sl.isRegistered<XmppConnectionCubit>() ? sl.get<XmppConnectionCubit>() : XmppConnectionCubit._(state: state);
  }

  Future<void> connect() async {
    if (!userHasLoggedIn()) {
      log.w('User not logged in. Cannot connect to XMPP server.');
      emit(const XmppConnectionFailed('User not logged in. Cannot connect to chat server.'));
      return;
    }

    emit(XmppConnectionLoading());

    String? sipNumber = prefs.getString(CacheKeys.sipNumber);
    String? sipDomain = prefs.getString(CacheKeys.sipDomain);
    String? sipPwd = prefs.getString(CacheKeys.sipPwd);
    bool initResult = await xmppService.init(
        sipNumber: sipNumber!,
        sipDomain: sipDomain!,
        sipPwd: sipPwd!,
        connectionStateChangedEventCallback: (event) async {
          switch (event.state) {
            case moxxmpp.XmppConnectionState.notConnected:
              emit(XmppConnectionInitial());
              break;
            case moxxmpp.XmppConnectionState.connecting:
              emit(XmppConnectionLoading());
              break;
            case moxxmpp.XmppConnectionState.connected:
              emit(XmppConnectionSuccess());
              break;
            case moxxmpp.XmppConnectionState.error:
              emit(const XmppConnectionFailed('Failed to connect to chat server'));
              break;
            default:
          }
        },
        messageEventCallback: (event) async {
          log.t('recevied xmpp message event');
        });
    if (initResult) {
      emit(XmppConnectionSuccess());
    } else {
      emit(const XmppConnectionFailed('Failed to connect to chat server.'));
    }
  }

  Future<void> disconnect() async {
    xmppService.dispose();
    emit(XmppConnectionInitial());
  }
}

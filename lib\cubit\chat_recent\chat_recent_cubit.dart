import 'package:bloc/bloc.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/contacts/contacts_cubit.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/add_bookmark/add_bookmark_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/mixins/prefs_aware.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:moxxmpp/moxxmpp.dart';

part 'chat_recent_state.dart';

class ChatRecentCubit extends Cubit<ChatRecentState> with PrefsAware {
  ChatRecentCubit() : super(ChatRecentInitial());

  void setPageIndex(int pageIndex) {
    emit(ChatRecentLoaded(pageIndex: pageIndex));
  }

  /// <PERSON> also forgot the original intention, he say he feels like he put it just to print out
  /// something for debugging.
  Future<void> getItmes(
    LoginCubit loginCubit,
  ) async {
    if (loginCubit.state is LoginAuthenticated) {
      LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;
      final upFile = currentLoginState.connection.getManagerById<HttpFileUploadManager>(httpFileUploadManager)!;
      await upFile.isSupported();
    }
  }

  /// Original intention is to use this to check whether the user is a 'owner', 'admin' or 'member' of the group
  /// But william say last time no time to do this so just wrote the function but didn't use it.
  Future<void> getConfigure(
    LoginCubit loginCubit,
    String mucEditingController,
    String nickEditingController,
  ) async {
    if (loginCubit.state is LoginAuthenticated) {
      LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;
      final mucName = JID.fromString(mucEditingController);
      final nickName = JID.fromString(nickEditingController);

      final muc = currentLoginState.connection.getManagerById<MUCManager>(mucManager)!;
      await muc.setConfigure(
        mucName,
        nickName.toString(),
      );
    }
  }

  // create group chat
  Future<void> createChatRoom(
    LoginCubit loginCubit,
    String mucEditingController,
    String nickEditingController,
    String roomNameEditingController,
    AddBookmarkCubit addBookmarkCubit,
    BookmarkListCubit bookmarkListCubit,
    GroupUiCubit groupUiCubit,
    ContactsCubit contactsCubit,
  ) async {
    try {
      if (loginCubit.state is LoginAuthenticated) {
        LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;
        String? sipNumber = prefs.getString(CacheKeys.sipNumber);
        String? sipDomain = prefs.getString(CacheKeys.sipDomain);
        var userAtDomain = '${sipNumber!}@${sipDomain!}';

        final mucName = JID.fromString(mucEditingController);
        final mucNick = roomNameEditingController.isNotEmpty && roomNameEditingController != ''
            ? JID.fromString(roomNameEditingController)
            : mucName;

        final nickName = JID.fromString(userAtDomain);

        final muc = currentLoginState.connection.getManagerById<MUCManager>(mucManager)!;

        if (mucName.toString().isNotEmpty && nickName.toString().isNotEmpty) {
          await addBookmarkCubit.addPendingBookmark(
            mucNick.toString(),
            'true',
            mucName.toString(),
            nickName.toString(),
          );
          await addBookmarkCubit.addBookmark(
            loginCubit: loginCubit,
            bookmarkListCubit: bookmarkListCubit,
            groupUiCubit: groupUiCubit,
          );

          await muc.joinRoom(
            mucName,
            nickName.toString(),
            maxHistoryStanzas: 0,
          );
        }
        await muc.setConfigure(
          mucName,
          nickName.toString(),
        );
      }
    } catch (e) {
      debugPrint('Error creating chat room: $e');
    }
  }
}

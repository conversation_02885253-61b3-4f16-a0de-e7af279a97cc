part of 'member_count_cubit.dart';

class MemberCountState {
  const MemberCountState({this.selectedGroup});
  final String? selectedGroup;

  @override
  List<Object> get props => [];

  MemberCountState copyWith({
    String? selectedGroup,
  }) {
    return MemberCountState(
      selectedGroup: selectedGroup ?? this.selectedGroup,
    );
  }
}

class MemberCountInitial extends MemberCountState {
  MemberCountInitial({required super.selectedGroup});
}

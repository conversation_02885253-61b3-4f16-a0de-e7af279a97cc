part of 'delete_bookmark_cubit.dart';

class DeleteBookmarkState extends Equatable {
  const DeleteBookmarkState(this.pendingDelete);
  final List<ConferenceInfo> pendingDelete;

  @override
  List<Object> get props => [];
}

class DeleteBookmarkInitial extends DeleteBookmarkState {
  DeleteBookmarkInitial(super.pendingDelete);
}

class DeleteBookmarkPending extends DeleteBookmarkState {
  DeleteBookmarkPending(super.pendingDelete);
}

class DeleteBookmarkDone extends DeleteBookmarkState {
  DeleteBookmarkDone(super.pendingDelete);
}

class DeleteBookmarkFailed extends DeleteBookmarkState {
  final String error;
  DeleteBookmarkFailed(
    super.pendingDelete, {
    required this.error,
  });
}

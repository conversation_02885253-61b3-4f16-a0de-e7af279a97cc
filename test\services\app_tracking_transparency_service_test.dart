import 'package:flutter_test/flutter_test.dart';
import 'package:ddone/services/app_tracking_transparency_service.dart';
import 'package:ddone/utils/logger_util.dart';

void main() {
  setUpAll(() async {
    // Initialize logger for tests
    await initializeLogger();
  });
  group('AppTrackingTransparencyService', () {
    test('should handle non-iOS platforms gracefully', () async {
      // This test will pass on non-iOS platforms (ATT is iOS-only)
      final status = await AppTrackingTransparencyService.getTrackingStatus();
      expect(status, isNotNull);
    });

    test('should handle tracking authorization check', () async {
      final isAuthorized = await AppTrackingTransparencyService.isTrackingAuthorized();
      expect(isAuthorized, isA<bool>());
    });

    test('should handle advertising identifier request', () async {
      final identifier = await AppTrackingTransparencyService.getAdvertisingIdentifier();
      // Should return null on non-iOS or when not authorized
      expect(identifier, isA<String?>());
    });
  });
}

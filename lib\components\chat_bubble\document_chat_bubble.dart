import 'package:ddone/constants/value_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/models/download_result_model.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/download_file_service.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/utils/screen_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

enum BubbleActionState { idle, opening, saving }

class DocumentChatBubble extends StatefulWidget {
  final String fileName;
  final bool isMe;
  final String formattedTime;
  final String fileUrl;

  const DocumentChatBubble({
    required this.fileName,
    required this.isMe,
    required this.formattedTime,
    required this.fileUrl,
    super.key,
  });

  @override
  State<DocumentChatBubble> createState() => _DocumentChatBubbleState();
}

class _DocumentChatBubbleState extends State<DocumentChatBubble> {
  final DownloadFileService _downloadService = sl.get<DownloadFileService>();
  var _actionState = BubbleActionState.idle;

  Future<void> _openFile() async {
    if (_actionState != BubbleActionState.idle) return;

    setState(() {
      _actionState = BubbleActionState.opening;
    });

    final result = await _downloadService.downloadAndOpenFile(widget.fileUrl);

    // If opening fails, show an error. On success, the OS handles it.
    if (result.status != DownloadResultStatus.successful && result.status != DownloadResultStatus.fileAlreadyExists) {
      EasyLoadingService().showErrorWithText(result.message ?? 'Could not open file.');
    }

    setState(() {
      _actionState = BubbleActionState.idle;
    });
  }

  Future<void> _saveFile() async {
    if (_actionState != BubbleActionState.idle) return;

    setState(() {
      _actionState = BubbleActionState.saving;
    });

    final result = await _downloadService.saveFileToDownloads(widget.fileUrl);

    // Provide clear feedback for the save action
    switch (result.status) {
      case DownloadResultStatus.successful:
        String successText = 'Saved!';
        if (isIOS) {
          successText = 'Saved to Files!';
        } else if (isAndroid) {
          successText = 'Saved to Downloads!';
        } else if (isWindows) {
          successText = 'Saved to Downloads!';
        }
        EasyLoadingService().showSuccessWithText(successText);
        break;
      case DownloadResultStatus.failed:
      case DownloadResultStatus.fileNotAvailable:
        EasyLoadingService().showErrorWithText(result.message ?? 'Download failed');
        break;
      case DownloadResultStatus.canceled:
      case DownloadResultStatus.paused:
        EasyLoadingService().showInfoWithText(result.message ?? 'Download interrupted');
        break;
      case DownloadResultStatus.fileAlreadyExists:
        EasyLoadingService().showInfoWithText('File already exists.');
        break;
    }

    setState(() {
      _actionState = BubbleActionState.idle;
    });
  }

  // Helper widget for the loading indicator to avoid code duplication.
  Widget _buildLoadingIndicator() {
    return const SizedBox(
      width: 20,
      height: 20,
      child: CircularProgressIndicator(strokeWidth: 2.0, color: Colors.white),
    );
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;
        String fileExtension = '';

        if (widget.fileName.split('.').length > 1) {
          fileExtension = widget.fileName.split('.')[1];
        } else {
          return const Padding(
            padding: EdgeInsets.only(
              right: 5.0,
            ),
            child: Icon(Icons.error),
          );
        }

        String getFileTypeIcon(String? extension) {
          switch (extension?.toLowerCase()) {
            case 'doc':
            case 'docx':
              return '$iconPathPrefix/docx.png'; // Custom Word icon
            case 'pdf':
              return '$iconPathPrefix/pdf.png'; //Custom PDF icon
            case 'ppt':
            case 'pptx':
              return '$iconPathPrefix/ppt.png'; // Custom PowerPoint icon
            case 'xls':
            case 'xlsx':
              return '$iconPathPrefix/xlsx.png'; // Custom Excel icon
            case 'py':
              return '$iconPathPrefix/python.png'; // Custom python icon
            case 'html':
            case 'xml':
            case 'css':
              return '$iconPathPrefix/xml.png'; // Custom code icon
            case 'js':
              return '$iconPathPrefix/js.png'; // Custom js icon
            case 'json':
              return '$iconPathPrefix/json.png'; // Custom json icon
            case 'txt':
              return '$iconPathPrefix/text.png'; // Custom text icon
            case 'ini':
              return '$iconPathPrefix/ini.png'; // Custom ini icon
            case 'mp4':
              return '$iconPathPrefix/video.png'; // Custom video icon
            // case 'mp3':
            //   return '$iconPathPrefix/music.png'; // Custom music icon
            default:
              return '$iconPathPrefix/genericfile.png'; // Default file icon
          }
        }

        return Column(
          children: [
            Card(
              color: Colors.amber[800],
              elevation: 5,
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Image.asset(
                          getFileTypeIcon(
                            fileExtension,
                          ),
                          height: 35,
                          width: 35,
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Text(
                              widget.fileName,
                              style: textTheme.bodyMedium?.copyWith(
                                color: widget.isMe ? Colors.black : Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.fade,
                              // softWrap: false,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Divider(
                    height: 1,
                    color: colorTheme.backgroundColor.withOpacity(0.2),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        MaterialButton(
                          // Disable the button if any action is in progress
                          onPressed: _actionState != BubbleActionState.idle ? null : _openFile,
                          elevation: 5,
                          color: colorTheme.backgroundColor.withOpacity(0.8),
                          hoverColor: colorTheme.backgroundColor,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10.0),
                            // Show loading indicator specific to this button's action
                            child: _actionState == BubbleActionState.opening
                                ? _buildLoadingIndicator()
                                : const Text('Open'),
                          ),
                        ),
                        const SizedBox(width: 7),
                        MaterialButton(
                          onPressed: _actionState != BubbleActionState.idle ? null : _saveFile,
                          elevation: 10,
                          color: colorTheme.backgroundColor.withOpacity(0.8),
                          hoverColor: colorTheme.backgroundColor,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12.0),
                            child: _actionState == BubbleActionState.saving
                                ? _buildLoadingIndicator()
                                : const Text('Save'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Align(
              alignment: Alignment.centerRight,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 0.0),
                child: Text(
                  widget.formattedTime,
                  style: TextStyle(
                    color: widget.isMe ? Colors.black : Colors.white70,
                    fontSize: 10,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

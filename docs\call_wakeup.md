# Wake Up Flow

## iOS

It is the same for all cases: App foreground, background, background + lock screen, terminated, terminated + lock screen.

1. Receive APN VoIP push
2. FlutterCallkitIncoming show callkit (in native swift, AppDelegate.swift)
3. home initState start to run.
4. _callkitService.init() define the callback that FlutterCallkitIncoming event will trigger.

## Android

check whether background service is still required in android, ios don't need it ady.

### App foreground

### App background

### App temrinated

## Desktop

Always connected.

# Wake up time log

## iOS

**20250716**

1. 14:42:39.970 (wake up) -> 14:42:50.117 (registered) = 10.147
2. 15:05:58.074 (start up) -> 5:06:02.976 (registered) = 4.702
3. 15:07:36.286 (wake up) -> 15:07:38.145 (registered) = 1.859  -> 15:07:49.532 (incoming call)
4. 16:35:19.719 (wake up) -> 

# Call test cases

1. Outgoing call > Call accepted on callee side
2. Outgoing call > Call rejected on callee side
3. Outgoing call > Callee missed call
4. Outgoing call > Caller early end call before callee react
5. Incoming call (app foreground) > Accept call
6. Incoming call (app foreground) > Decline call
7. Incoming call (app foreground) > Miss call
8. Incoming call (app foreground) > Caller hang up before accept/decline + before receive incoming call
9. Incoming call (app foreground) > Caller hang up before accept/decline + after receive incoming call 
10. Incoming call (app foreground + lock screen) > Accept call
11. Incoming call (app foreground + lock screen) > Decline call <<<< [THIS GOT PROBLEM!!!]
    In IOS, after decline call, cannot wait until call proceed because IOS will stop it after short period of time. (13:38:23.856 to 13:38:24.676 = 820ms)
    Need to have a quick way for client side to decline call. 
12. Incoming call (app foreground + lock screen) > Miss call
13. Incoming call (app foreground + lock screen) > Caller hang up before accept/decline + before receive incoming call
14. Incoming call (app foreground + lock screen) > Caller hang up before accept/decline + after receive incoming call 
15. Incoming call (app background) > Accept call
16. Incoming call (app background) > Decline call <<<< [THIS GOT PROBLEM!!!]
    13:52:46.938 to 13:52:52.256 = 5318ms
17. Incoming call (app background) > Miss call
18. Incoming call (app background) > Caller hang up before accept/decline + before receive incoming call
19. Incoming call (app background) > Caller hang up before accept/decline + after receive incoming call 
20. Incoming call (app background + lock screen) > Accept call
21. Incoming call (app background + lock screen) > Decline call
22. Incoming call (app background + lock screen) > Miss call
23. Incoming call (app background + lock screen) > Caller hang up before accept/decline + before receive incoming call
24. Incoming call (app background + lock screen) > Caller hang up before accept/decline + after receive incoming call 
25. Incoming call (app terminated) > Accept call
26. Incoming call (app terminated) > Decline call
27. Incoming call (app terminated) > Miss call
28. Incoming call (app terminated) > Caller hang up before accept/decline + before receive incoming call
29. Incoming call (app terminated) > Caller hang up before accept/decline + after receive incoming call 
30. Incoming call (app terminated + lock screen) > Accept call
31. Incoming call (app terminated + lock screen) > Decline call
32. Incoming call (app terminated + lock screen) > Miss call
33. Incoming call (app terminated + lock screen) > Caller hang up before accept/decline + before receive incoming call
34. Incoming call (app terminated + lock screen) > Caller hang up before accept/decline + after receive incoming call 
35. In call > network disconnected
36. In call > mute
37. In call > speaker toggle
38. In call > transfer call
39. In call > hold call
40. In call > dtmf
41. In call (ios lock screen) > network disconnected
42. In call (ios lock screen) > mute
43. In call (ios lock screen) > speaker toggle
44. In call (ios lock screen) > transfer call
45. In call (ios lock screen) > hold call
46. In call (ios lock screen) > dtmf

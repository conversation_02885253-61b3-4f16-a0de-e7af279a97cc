import 'package:json_annotation/json_annotation.dart';

part 'endpointmgt_models.g.dart';

@JsonSerializable()
class User {
  final String? name;
  final String? domain;
  @JsonKey(name: 'extension_number')
  final String? extensionNumber;

  User({
    this.name,
    this.domain,
    this.extensionNumber,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);
}

// MiscellaneousInfo can be any JSON structure
typedef MiscellaneousInfo = Map<String, dynamic>;

@JsonSerializable()
class Device {
  @JsonKey(name: 'endpoint_id')
  final String? endpointId;
  @Json<PERSON>ey(name: 'device_id')
  final String? deviceId;
  @JsonKey(name: 'operating_system')
  final String? operatingSystem;
  @JsonKey(name: 'os_version')
  final String? osVersion;
  @Json<PERSON>ey(name: 'miscellaneous_info')
  final MiscellaneousInfo? miscellaneousInfo;

  Device({
    this.endpointId,
    this.deviceId,
    this.operatingSystem,
    this.osVersion,
    this.miscellaneousInfo,
  });

  factory Device.fromJson(Map<String, dynamic> json) => _$DeviceFromJson(json);
  Map<String, dynamic> toJson() => _$DeviceToJson(this);
}

@JsonSerializable()
class App {
  @JsonKey(name: 'app_id')
  final String appId;
  @JsonKey(name: 'app_name')
  final String appName;
  @JsonKey(name: 'current_app_version')
  final String? currentAppVersion;

  App({
    required this.appId,
    required this.appName,
    this.currentAppVersion,
  });

  factory App.fromJson(Map<String, dynamic> json) => _$AppFromJson(json);
  Map<String, dynamic> toJson() => _$AppToJson(this);
}

@JsonSerializable()
class CheckUpdateRequest {
  final Device device;
  final App app;

  CheckUpdateRequest({
    required this.device,
    required this.app,
  });

  factory CheckUpdateRequest.fromJson(Map<String, dynamic> json) => _$CheckUpdateRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CheckUpdateRequestToJson(this);
}

@JsonSerializable()
class UpdateUserRequest {
  final User user;
  final Device device;

  UpdateUserRequest({
    required this.user,
    required this.device,
  });

  factory UpdateUserRequest.fromJson(Map<String, dynamic> json) => _$UpdateUserRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateUserRequestToJson(this);
}

@JsonSerializable()
class CallHistoryRequest {
  @JsonKey(name: 'call_party_name')
  final String callPartyName;
  @JsonKey(name: 'call_party_number')
  final String callPartyNumber;
  @JsonKey(name: 'call_direction')
  final String callDirection;
  final String duration;
  @JsonKey(name: 'user_extension_number')
  final String userExtensionNumber;
  final String domain;

  CallHistoryRequest({
    required this.callPartyName,
    required this.callPartyNumber,
    required this.callDirection,
    required this.duration,
    required this.userExtensionNumber,
    required this.domain,
  });

  factory CallHistoryRequest.fromJson(Map<String, dynamic> json) => _$CallHistoryRequestFromJson(json);
  Map<String, dynamic> toJson() => _$CallHistoryRequestToJson(this);
}

@JsonSerializable()
class CheckUpdateResponse {
  @JsonKey(name: 'endpoint_id')
  final String? endpointId;
  @JsonKey(name: 'requires_update')
  final bool? requiresUpdate;
  @JsonKey(name: 'latest_version')
  final String? latestVersion;
  @JsonKey(name: 'update_url')
  final String? updateUrl;

  CheckUpdateResponse({
    this.endpointId,
    this.requiresUpdate,
    this.latestVersion,
    this.updateUrl,
  });

  factory CheckUpdateResponse.fromJson(Map<String, dynamic> json) => _$CheckUpdateResponseFromJson(json);
  Map<String, dynamic> toJson() => _$CheckUpdateResponseToJson(this);
}

@JsonSerializable()
class CallHistoryItem {
  final String id;
  @JsonKey(name: 'date_created')
  final String dateCreated;
  final String user;
  @JsonKey(name: 'call_direction')
  final String callDirection;
  @JsonKey(name: 'call_party_name')
  final String callPartyName;
  @JsonKey(name: 'call_party_number')
  final String callPartyNumber;
  final String duration;

  CallHistoryItem({
    required this.id,
    required this.dateCreated,
    required this.user,
    required this.callDirection,
    required this.callPartyName,
    required this.callPartyNumber,
    required this.duration,
  });

  factory CallHistoryItem.fromJson(Map<String, dynamic> json) => _$CallHistoryItemFromJson(json);
  Map<String, dynamic> toJson() => _$CallHistoryItemToJson(this);
}

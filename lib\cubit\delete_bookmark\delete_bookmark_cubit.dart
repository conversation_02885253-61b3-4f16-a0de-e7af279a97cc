import 'package:bloc/bloc.dart';
import 'package:ddone/constants/keys/cache_key.dart';
import 'package:ddone/cubit/login/login_cubit.dart';
import 'package:ddone/cubit/update/group_ui_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:moxxmpp/model/conference_info.dart';
import 'package:moxxmpp/moxxmpp.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'delete_bookmark_state.dart';

class DeleteBookmarkCubit extends Cubit<DeleteBookmarkState> {
  final SharedPreferences _sharedPreferences;
  DeleteBookmarkCubit._()
      : _sharedPreferences = sl.get<SharedPreferences>(),
        super(DeleteBookmarkInitial(const []));

  factory DeleteBookmarkCubit.initial() {
    return DeleteBookmarkCubit._();
  }

  Future<void> deleteBookmark({
    required LoginCubit loginCubit,
    required BookmarkListCubit bookmarkListCubit,
    required GroupUiCubit groupUiCubit,
    required String bookmarkName,
  }) async {
    try {
      if (loginCubit.state is LoginAuthenticated) {
        emit(DeleteBookmarkPending(const []));
        LoginAuthenticated currentLoginState = loginCubit.state as LoginAuthenticated;
        if (bookmarkListCubit.state is BookmarkListUpdated) {
          BookmarkListUpdated bookmarkListUpdated = bookmarkListCubit.state as BookmarkListUpdated;

          final bookmarkList = bookmarkListUpdated.bookmarkList;

          String? sipNumber = _sharedPreferences.getString(CacheKeys.sipNumber);
          String? sipDomain = _sharedPreferences.getString(CacheKeys.sipDomain);
          var userAtDomain = '${sipNumber!}@${sipDomain!}';

          debugPrint('Current Bookmark List :: $bookmarkList');

          bookmarkList.removeWhere((bookmark) {
            if (bookmark.name.isEmpty) {
              return bookmark.jid == bookmarkName;
            }
            return bookmark.name == bookmarkName;
          });
          debugPrint('BOokmarkName: $bookmarkName');

          debugPrint('Updated Bookmark List :: $bookmarkList');

          final muc = currentLoginState.connection.getManagerById<MUCManager>(mucManager)!;

          await muc.uploadBookmarks(
            bookmarkList,
            userAtDomain,
          );

          emit(DeleteBookmarkDone(bookmarkList));
          groupUiCubit.update();
        }
      }
    } catch (e) {
      emit(DeleteBookmarkFailed(
        state.pendingDelete,
        error: e.toString(),
      ));
    }
  }
}

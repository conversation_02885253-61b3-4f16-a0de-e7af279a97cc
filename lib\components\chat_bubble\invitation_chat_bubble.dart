import 'package:ddone/constants/regex_constants.dart';
import 'package:ddone/cubit/update/info_cubit.dart';
import 'package:ddone/constants/value_constants.dart';
import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/navigation_service.dart';
import 'package:ddone/services/ntp_datetime_service.dart';
import 'package:ddone/utils/extensions/datetime_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:moxxmpp/cubit/bookmark_list_cubit.dart';
import 'package:ddone/cubit/mam_list/mam_list_cubit.dart';

class InvitationChatBubble extends StatefulWidget {
  final VoidCallback onDeclinePressed;
  final VoidCallback onAcceptPressed;
  final String groupOnly;
  final bool isMe;
  final List<String> inviteSplit;
  final String groupName;
  static TextEditingController declinereasonEditingController = TextEditingController();
  final DateTime date;
  final String time;
  final String body;
  final String id;

  const InvitationChatBubble({
    required this.onDeclinePressed,
    required this.onAcceptPressed,
    required this.groupOnly,
    required this.isMe,
    required this.inviteSplit,
    required this.groupName,
    required this.date,
    required this.time,
    required this.body,
    required this.id,
    super.key,
  });

  @override
  State<InvitationChatBubble> createState() => _InvitationChatBubbleState();
}

class _InvitationChatBubbleState extends State<InvitationChatBubble> {
  // TextEditingController declinereasonEditingController = TextEditingController();
  late TimeService ntpTime;

  @override
  void initState() {
    super.initState();

    ntpTime = sl.get<TimeService>();
    debugPrint('ntpTime: ${ntpTime.ntpTime}');
  }

  // void compareMessages(List<MamInfo> messages) {
  //   for (var message in messages) {
  //     if (message.body.contains(invitationDeclineRegex)) {
  //       print(message.body);
  //       print(message.time);

  //       print('yes contains');
  //     }
  //   }
  // }
  // final String

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        final textTheme = themeState.themeData.textTheme;
        final groupName = widget.groupName;

        final groupNameFilter = groupNameRegex.firstMatch(groupName);
        final String? groupNameOnly = groupNameFilter?.group(0);
        final String groupNameOnlyReplaced = groupName.replaceAll(groupNameOnly ?? "", '');

        return BlocBuilder<BookmarkListCubit, BookmarkListState>(
          builder: (context, state) {
            return Padding(
              padding: const EdgeInsets.only(
                right: 4.0,
              ),
              child: Column(
                children: [
                  if (widget.isMe)
                    Text(
                      'You sent an invite',
                      style: textTheme.labelLarge?.copyWith(
                        color: widget.isMe ? Colors.black87 : Colors.white70,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  if (state.bookmarkList.toString().contains(widget.groupOnly) && !widget.isMe)
                    Text(
                      'You have joined',
                      style: textTheme.labelLarge?.copyWith(
                        color: widget.isMe ? Colors.black87 : Colors.white70,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  if (!state.bookmarkList.toString().contains(widget.groupOnly) && !widget.isMe)
                    BlocBuilder<InfoCubit, InfoState>(
                      builder: (context, infoState) {
                        return BlocBuilder<MamListCubit, MamListState>(
                          builder: (context, mamState) {
                            // print('MamKey: ${state.mamList[ChatPage.receiver]}');
                            final compare = mamState.mamList[infoState.receiver]!;
                            // compareMessages(compare);
                            // print("widget.body ${widget.body}");
                            if (widget.body.contains(invitationRegex)) {
                              // print("widget.body ${widget.body}");
                              for (int i = 0; i < compare.length; i++) {
                                // print('compare ${compare.length}');
                                // if (msg.body.contains(invitationDeclineRegex)) {
                                final String body = compare[i].body;
                                final String time = compare[i].time;
                                // final String id = compare[i].msgId;
                                final DateTime dateTime = DateTime.parse(time);

                                // print("Previous Date: ${widget.date} ?? ${dateTime} ??? ${widget.date.isBefore(dateTime)}");
                                if (widget.date.isBefore(dateTime) &&
                                    body.contains(invitationDeclineRegex) &&
                                    body.contains(widget.id)) {
                                  return Text(
                                    'You have declined this invitation',
                                    style: textTheme.labelLarge?.copyWith(
                                      color: widget.isMe ? Colors.black87 : Colors.white70,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  );
                                }
                                if (widget.date.isBefore(dateTime) &&
                                    body.contains(invitationDeclineRegex) &&
                                    !body.contains(widget.id)) {
                                  return Text(
                                    "Invitation Expired",
                                    style: textTheme.labelLarge?.copyWith(
                                      color: widget.isMe ? Colors.black87 : Colors.white70,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  );
                                }
                              }

                              return Text(
                                widget.inviteSplit[1],
                                style: textTheme.labelLarge?.copyWith(
                                  color: widget.isMe ? Colors.black87 : Colors.white70,
                                  fontWeight: FontWeight.w700,
                                ),
                              );
                              // }
                            }

                            // print("Comapre ${compare.map((e) {
                            //   print(e.time);
                            // })}");
                            // if (compare.contains(invitationDeclineRegex) && widget.date.isBefore(ntpTime.ntpTime.yyyyMMdd())) {
                            //   return Text(widget.body.replaceAll(invitationDeclineRegex, ''));
                            // }
                            return const SizedBox();
                          },
                        );
                      },
                    ),
                  // if (!state.bookmarkList.toString().contains(widget.groupOnly) &&
                  //     !widget.isMe &&
                  //     widget.date.isBefore(ntpTime.ntpTime.yyyyMMdd()))
                  //   Text(
                  //     "Invitation Expired",
                  //     style: textTheme.labelLarge?.copyWith(
                  //       color: widget.isMe ? Colors.black87 : Colors.white70,
                  //       fontWeight: FontWeight.w700,
                  //     ),
                  //   ),
                  Card(
                    color: Colors.amber[800],
                    elevation: 5,
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Icon(
                            Icons.people,
                            color: colorTheme.backgroundColor,
                            size: 35,
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              groupNameOnlyReplaced,
                              style: textTheme.bodyMedium?.copyWith(
                                color: widget.isMe ? Colors.black : Colors.white70,
                                fontWeight: FontWeight.w500,
                              ),
                              softWrap: false,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // !isMe?
                  state.bookmarkList.toString().contains(widget.groupOnly) ||
                          widget.isMe ||
                          widget.date.isBefore(ntpTime.ntpTime.yyyyMMdd())
                      ? Container()
                      : BlocBuilder<InfoCubit, InfoState>(
                          builder: (context, infoState) {
                            return BlocBuilder<MamListCubit, MamListState>(
                              builder: (context, mamState) {
                                final compare = mamState.mamList[infoState.receiver]!;
                                // compareMessages(compare);
                                // print("widget.body ${widget.body}");
                                if (widget.body.contains(invitationRegex)) {
                                  // print("widget.body ${widget.body}");
                                  for (int i = 0; i < compare.length; i++) {
                                    // print('compare ${compare[i].body}');
                                    // if (msg.body.contains(invitationDeclineRegex)) {
                                    final String body = compare[i].body;
                                    final String time = compare[i].time;
                                    final DateTime dateTime = DateTime.parse(time);

                                    // print(
                                    //     "Previous Date: ${widget.date} ?? ${dateTime} ???? $body ??? ${compare[i].msgId} ??? ${widget.date.isBefore(dateTime)}");
                                    if (widget.date.isBefore(dateTime) &&
                                        body.contains(invitationDeclineRegex) &&
                                        body.contains(widget.id)) {
                                      return const SizedBox();
                                    }
                                    // else {
                                    //   return Text(
                                    //     'widget.inviteSplit[1]',
                                    //     style: textTheme.labelLarge?.copyWith(
                                    //       color: widget.isMe ? Colors.black87 : Colors.white70,
                                    //       fontWeight: FontWeight.w700,
                                    //     ),
                                    //   );
                                    // }
                                  }

                                  return Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Tooltip(
                                        message: 'Decline',
                                        child: ElevatedButton(
                                          onPressed: () {
                                            showDialog(
                                              context: context,
                                              builder: (context) {
                                                return AlertDialog(
                                                  backgroundColor: colorTheme.backgroundColor,
                                                  title: const Center(
                                                    child: Text(
                                                      'Confirm to decline?',
                                                      style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 17,
                                                      ),
                                                    ),
                                                  ),
                                                  content: Column(
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      TextField(
                                                        style: const TextStyle(
                                                          color: Colors.white,
                                                        ),
                                                        cursorColor: colorTheme.primaryColor,
                                                        controller: InvitationChatBubble.declinereasonEditingController,
                                                        decoration: InputDecoration(
                                                          hintText: 'Reason',
                                                          hintStyle: textTheme.bodyLarge!.copyWith(
                                                            color: Colors.white54,
                                                            fontWeight: FontWeight.w500,
                                                          ),
                                                          // TextStyle(
                                                          //   color: Colors.white54,
                                                          // ),
                                                          // filled: true,
                                                          // fillColor: greyBackground,
                                                          enabledBorder: const OutlineInputBorder(
                                                            borderSide: BorderSide(
                                                              color: Colors.white70,
                                                            ),
                                                          ),
                                                          focusedBorder: const OutlineInputBorder(
                                                            borderSide: BorderSide(
                                                              color: Colors.white70,
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                      const SizedBox(
                                                        height: 10,
                                                      ),
                                                      Row(
                                                        mainAxisAlignment: MainAxisAlignment.center,
                                                        children: [
                                                          const TextButton(
                                                            onPressed: pop,
                                                            child: Text(
                                                              "Cancel",
                                                              style: TextStyle(
                                                                color: Colors.red,
                                                              ),
                                                            ),
                                                          ),
                                                          TextButton(
                                                            onPressed: () {
                                                              widget.onDeclinePressed();

                                                              pop();
                                                            },

                                                            // onPressed: () {
                                                            //   declineGroupJoin(
                                                            //     JID.fromString(groupOnly),
                                                            //     JID.fromString(ChatPage.receiver.toString()),
                                                            //     declinereasonEditingController.text,
                                                            //   );
                                                            // },
                                                            child: const Text(
                                                              "Confirm",
                                                              style: TextStyle(
                                                                color: Colors.green,
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                );
                                              },
                                            );
                                          },
                                          style: ButtonStyle(
                                            maximumSize: WidgetStateProperty.all(
                                              const Size(100, 40),
                                            ),
                                            padding: WidgetStateProperty.all(
                                              const EdgeInsets.symmetric(
                                                horizontal: 10,
                                                vertical: 5,
                                              ),
                                            ),
                                          ),
                                          child: Icon(
                                            Icons.close,
                                            color: Colors.redAccent[400],
                                          ),
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 5,
                                      ),
                                      Tooltip(
                                        message: 'Accept',
                                        child: ElevatedButton(
                                          onPressed: widget.onAcceptPressed,

                                          // () async {
                                          //   print('$groupOnly');
                                          //   await joinChatRoom(
                                          //     groupOnly.toString(),
                                          //     'testing2',
                                          //   );
                                          //   groupUiCubit.update();
                                          // },
                                          style: ButtonStyle(
                                            maximumSize: WidgetStateProperty.all(
                                              const Size(100, 40),
                                            ),
                                            padding: WidgetStateProperty.all(
                                              const EdgeInsets.symmetric(
                                                horizontal: 10,
                                                vertical: 5,
                                              ),
                                            ),
                                          ),
                                          child: Icon(
                                            Icons.check,
                                            color: Colors.greenAccent[400],
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                  // }
                                }
                                return const SizedBox();
                              },
                            );
                          },
                        ),
                  // : Container(),
                ],
              ),
            );
          },
        );
      },
    );
  }
}

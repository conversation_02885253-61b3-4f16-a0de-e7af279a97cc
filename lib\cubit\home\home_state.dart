part of 'home_cubit.dart';

abstract class HomeState extends Equatable {
  final int navBarNotifier;
  final int navSelectedIndex;
  final bool isMicOn;
  final String? prefilledDailpad;

  const HomeState({
    this.navBarNotifier = 0,
    this.navSelectedIndex = 1,
    this.isMicOn = true,
    this.prefilledDailpad,
  });

  @override
  List<Object?> get props => [
        navBarNotifier,
        navSelectedIndex,
        isMicOn,
        prefilledDailpad,
      ];
}

class HomeInitial extends HomeState {
  const HomeInitial({
    navBarNotifier = 0,
    navSelectedIndex = 1,
    isMicOn = true,
  }) : super(
          navBarNotifier: navBarNotifier,
          navSelectedIndex: navSelectedIndex,
          isMicOn: isMicOn,
        );

  @override
  List<Object?> get props => super.props..addAll([]);
}

class HomeLoaded extends HomeState {
  const HomeLoaded({
    required super.navBarNotifier,
    required super.navSelectedIndex,
    required super.isMicOn,
    super.prefilledDailpad,
  });

  @override
  List<Object?> get props => super.props..addAll([]);
}

class HomeLoading extends HomeState {
  const HomeLoading({
    navBarNotifier = 0,
    navSelectedIndex,
    isMicOn = true,
  }) : super(
          navBarNotifier: navBarNotifier,
          navSelectedIndex: navSelectedIndex,
          isMicOn: isMicOn,
        );

  @override
  List<Object?> get props => super.props..addAll([]);
}

class HomeError extends HomeState {
  const HomeError({
    navBarNotifier = 0,
    navSelectedIndex,
    isMicOn = true,
  }) : super(
          navBarNotifier: navBarNotifier,
          navSelectedIndex: navSelectedIndex,
          isMicOn: isMicOn,
        );

  @override
  List<Object?> get props => super.props..addAll([]);
}

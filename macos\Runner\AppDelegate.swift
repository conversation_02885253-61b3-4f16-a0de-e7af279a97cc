import Cocoa
import FlutterMacOS
import UserNotifications

@main
class AppDelegate: FlutterAppDelegate, UNUserNotificationCenterDelegate {
//    var mainFlutterWindow: NSWindow?
    
  override func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> <PERSON><PERSON> {
    return false
  }

  override func applicationShouldHandleReopen(_ sender: NSApplication, hasVisibleWindows flag: Bool) -> Bool {
    if !flag {
        // mainFlutterWindow?.makeKeyAndOrderFront(nil)
        
        for window in NSApp.windows {
            if !window.isVisible {
                window.setIsVisible(true)
            }
            window.makeKeyAndOrderFront(self)
            NSApp.activate(ignoringOtherApps: true)
        }
    }
    return true
  }
    
    override func applicationDidFinishLaunching(_ aNotification: Notification){
//        super.applicationDidFinishLaunching(notification)
        
        UNUserNotificationCenter.current().delegate = self
        
        if let window = NSApplication.shared.windows.first {
                   mainFlutterWindow = window
               }
        
        addReopenMenuItem()

        // addWindowMenu()
    }
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        return completionHandler([.list, .sound])
    }
    
    func addReopenMenuItem() {
            guard let appMenu = NSApplication.shared.mainMenu?.items.first?.submenu else {
                return
            }

            let reopenMenuItem = NSMenuItem(title: "Reopen Window", action: #selector(reopenMainWindow(_:)), keyEquivalent: "r")
            reopenMenuItem.target = self
            appMenu.addItem(NSMenuItem.separator())
            appMenu.addItem(reopenMenuItem)
        }
    
    @objc func reopenMainWindow(_ sender: Any?) {
        if let window = NSApplication.shared.windows.first {
            if window.isMiniaturized {
                window.deminiaturize(nil) // Restore from minimized state
            } else if !window.isVisible {
                window.makeKeyAndOrderFront(nil) // Show the window if hidden
            }
        }
    }
    
    func addWindowMenu() {
            guard let mainMenu = NSApplication.shared.mainMenu else { return }

            // Create the Window menu
            let windowMenuItem = NSMenuItem(title: "Window", action: nil, keyEquivalent: "")
            let windowSubMenu = NSMenu(title: "Window")
            windowMenuItem.submenu = windowSubMenu

            // Add "Main Window" menu item
            let mainWindowMenuItem = NSMenuItem(
                title: "Main Window",
                action: #selector(reopenMainWindow(_:)),
                keyEquivalent: "0" // Command+0 to reopen the main window
            )
            mainWindowMenuItem.target = self
            windowSubMenu.addItem(mainWindowMenuItem)

            // Add standard macOS menu items (e.g., Minimize, Zoom)
            windowSubMenu.addItem(NSMenuItem.separator())
            windowSubMenu.addItem(NSMenuItem(title: "Minimize", action: #selector(NSWindow.performMiniaturize(_:)), keyEquivalent: "m"))
            windowSubMenu.addItem(NSMenuItem(title: "Zoom", action: #selector(NSWindow.performZoom(_:)), keyEquivalent: ""))

            // Add the Window menu to the main menu
            mainMenu.addItem(windowMenuItem)
        }


       
}

import 'package:bloc/bloc.dart';
import 'package:ddone/models/xmpp_model/presence_info.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

part 'members_presence_state.dart';

class PresencesCubit extends Cubit<PresencesState> {
  PresencesCubit() : super(const PresencesInitial({}));

  static Map<String, List<PresenceInfo>> presence = {};

  Set<String> nick = {};

  Future<void> getGroupPresences() async {
    debugPrint("The presence $presence");
    // List<String> ada = presence;
    // nick.addAll(ada);

    presence.forEach((key, value) {
      // Convert List to Set to remove duplicates and back to List
      final uniqueList = value.toSet().toList();
      presence[key] = uniqueList;
    });
    emit(PresencesLoaded(presence));
  }
}

import 'package:flutter/material.dart';

class ResponsiveImageContainer extends StatelessWidget {
  final ImageProvider imageProvider;
  final double imageWidth;
  final double imageHeight;

  const ResponsiveImageContainer({
    required this.imageProvider,
    required this.imageWidth,
    required this.imageHeight,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    double aspectRatio = imageWidth / imageHeight;

    return Center(
      child: Container(
        color: Colors.red,
        child: LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
            return IntrinsicHeight(
              child: AspectRatio(
                aspectRatio: aspectRatio,
                child: FittedBox(
                  fit: BoxFit.contain,
                  child: Image(
                    image: imageProvider,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

part of 'xmpp_connection_cubit.dart';

abstract class XmppConnectionState extends Equatable {
  const XmppConnectionState();

  @override
  List<Object> get props => [];
}

class XmppConnectionInitial extends XmppConnectionState {}

class XmppConnectionLoading extends XmppConnectionState {}

class XmppConnectionSuccess extends XmppConnectionState {}

class XmppConnectionFailed extends XmppConnectionState {
  final String error;

  const XmppConnectionFailed(this.error);
}

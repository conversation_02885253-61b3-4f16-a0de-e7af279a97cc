import 'package:bloc/bloc.dart';
import 'package:ddone/mixins/api_handler.dart';
import 'package:ddone/models/hive/call_records.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'call_history_state.dart';

class CallHistoryCubit extends Cubit<CallHistoryState> with ApiHandler {
  final HiveService hiveService;

  CallHistoryCubit._({
    CallHistoryState? state,
  })  : hiveService = sl.get<HiveService>(),
        super(
          state ?? const CallHistoryInitial(),
        ) {
    hiveService.returnBox<CallRecords>().listenable().addListener(_onBoxChanged);
  }

  factory CallHistoryCubit.initial({CallHistoryState? state}) {
    //the case of is register only happen when you mock the cubit in test case
    if (sl.isRegistered<CallHistoryCubit>()) {
      return sl.get<CallHistoryCubit>();
    }

    return CallHistoryCubit._(state: state);
  }

  void _onBoxChanged() {
    emit(CallHistoryLoaded(callRecordList: hiveService.getAllData<CallRecords>().reversed.toList()));
  }

  void getCallRecords() async {
    final List<CallRecords> callRecordList = hiveService.getAllData<CallRecords>();

    emit(CallHistoryLoaded(callRecordList: callRecordList.reversed.toList()));
  }
}

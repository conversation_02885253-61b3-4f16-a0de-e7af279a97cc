import 'package:ddone/cubit/common/theme/theme_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:split_view/split_view.dart';

class SplitViewWidget extends StatelessWidget {
  final List<Widget> splitViewWidgetList;
  final List<double>? weightList;
  final List<WeightLimit>? weightLimitList;

  const SplitViewWidget({
    required this.splitViewWidgetList,
    required this.weightList,
    required this.weightLimitList,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final colorTheme = themeState.colorTheme;
        // final textTheme = themeState.themeData.textTheme;
        return SplitView(
          viewMode: SplitViewMode.Horizontal,
          gripColor: colorTheme.roundShapeInkWellColor.withOpacity(0.3),
          gripSize: 5,
          gripColorActive: colorTheme.roundShapeInkWellColor.withOpacity(0.3),
          controller: <PERSON><PERSON>iew<PERSON>ontroller(weights: weightList, limits: weightLimitList),
          children: splitViewWidgetList,
        );
      },
    );
  }
}

import 'package:ddone/models/enums/audio_device_type_enum.dart';
import 'package:equatable/equatable.dart';

const kIOSBuiltInRecevierId = 'Built-In Receiver';

class AudioDeviceModel extends Equatable {
  final String deviceId;
  final String label;
  final AudioDeviceType type;
  final bool isSelected;
  final bool isAvailable;

  const AudioDeviceModel({
    required this.deviceId,
    required this.label,
    required this.type,
    this.isSelected = false,
    this.isAvailable = true,
  });

  AudioDeviceModel copyWith({
    String? deviceId,
    String? label,
    AudioDeviceType? type,
    bool? isSelected,
    bool? isAvailable,
  }) {
    return AudioDeviceModel(
      deviceId: deviceId ?? this.deviceId,
      label: label ?? this.label,
      type: type ?? this.type,
      isSelected: isSelected ?? this.isSelected,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }

  @override
  List<Object?> get props => [
        deviceId,
        label,
        type,
        isSelected,
        isAvailable,
      ];

  @override
  String toString() {
    return 'AudioDeviceModel(deviceId: $deviceId, label: $label, type: $type, isSelected: $isSelected, isAvailable: $isAvailable)';
  }

  /// Factory method to create AudioDeviceModel from MediaDeviceInfo
  static AudioDeviceModel fromMediaDeviceInfo(
    String deviceId,
    String label, {
    bool isSelected = false,
  }) {
    final type = _determineDeviceType(deviceId, label);
    return AudioDeviceModel(
      deviceId: deviceId,
      label: label.isEmpty ? type.displayName : label,
      type: type,
      isSelected: isSelected,
    );
  }

  /// Determine device type based on deviceId and label
  static AudioDeviceType _determineDeviceType(String deviceId, String label) {
    final lowerDeviceId = deviceId.toLowerCase();
    final lowerLabel = label.toLowerCase();
    final combined = '$lowerDeviceId $lowerLabel';

    // iOS specific device IDs
    if (deviceId == kIOSBuiltInRecevierId || lowerDeviceId.contains('receiver')) {
      return AudioDeviceType.earpiece;
    }

    // Android specific device IDs
    if (lowerDeviceId == 'earpiece') {
      return AudioDeviceType.earpiece;
    }

    // Check for speaker early to avoid checking it multiple times
    if (deviceId == 'Speaker' || lowerDeviceId == 'speaker' || combined.contains('speaker')) {
      // But not if it's a Bluetooth speaker (handled below)
      if (!combined.contains('bluetooth') && !combined.contains('bt')) {
        return AudioDeviceType.speaker;
      }
    }

    // Bluetooth devices
    if (combined.contains('bluetooth') ||
        combined.contains('bt') ||
        combined.contains('airpods') ||
        combined.contains('headset')) {
      if (combined.contains('headphone') ||
          combined.contains('headset') ||
          combined.contains('airpods') ||
          combined.contains('earbuds')) {
        return AudioDeviceType.bluetoothHeadphones;
      }
      return AudioDeviceType.bluetoothSpeaker;
    }

    // Wired devices
    if (combined.contains('wired') ||
        combined.contains('headphone') ||
        combined.contains('headset') ||
        combined.contains('earphone')) {
      return AudioDeviceType.wiredHeadphones;
    }

    // Default/system devices
    if (combined.contains('default') || combined.contains('system')) {
      return AudioDeviceType.systemDefault;
    }

    if (combined.contains('speaker')) {
      return AudioDeviceType.speaker;
    }

    return AudioDeviceType.other;
  }
}

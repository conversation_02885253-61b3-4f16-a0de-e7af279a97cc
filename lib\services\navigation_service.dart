import 'package:ddone/components/common/qr_code_scanner.dart';
import 'package:ddone/screens/chat.dart';
import 'package:ddone/screens/chat_recent.dart';
import 'package:ddone/screens/home.dart';
import 'package:ddone/screens/about_me.dart';
import 'package:ddone/constants/keys/widget_keys.dart';
import 'package:ddone/screens/dialpad.dart';
import 'package:ddone/screens/selected_contact.dart';
import 'package:flutter/material.dart';
import 'package:responsive_framework/responsive_framework.dart';

class AppRouter {
  final _allRoutes = <String, Widget Function(RouteSettings settings)>{
    Home.routeName: (setting) => const Home(),
    // MobileHomepage.routeName: (setting) => const MobileHomepage(),
    Dialpad.routeName: (setting) => const Dialpad(),
    // ContactList.routeName: (setting) => ContactList(localContactBox: ),
    // SelectedContact.routeName: (setting) => SelectedContact(contactId: '', displayName: '', uri: ''),
    AboutMe.routeName: (setting) => const AboutMe(),
    RecentChat.routeName: (setting) => const RecentChat(),
    ChatPage.routeName: (setting) => const ChatPage(),
    SelectedContact.routeName: (setting) => const SelectedContact(),
    QRCodeScannerPage.routeName: (setting) => const QRCodeScannerPage(),
    // "/callHistory": (c) => CallHistory(callRecordBox: ,),
  };

  MaterialPageRoute<dynamic> onGenerateRoute(RouteSettings settings) {
    Function(RouteSettings)? builder = _allRoutes[settings.name!];
    Widget childWidget = builder!(settings);

    return MaterialPageRoute(
      settings: settings,
      builder: (ctx) {
        return BouncingScrollWrapper.builder(
          ctx,
          childWidget,
          dragWithMouse: true,
        );
      },
    );
  }
}

Future<T?> pushNamed<T extends Object?>(
  String routeName, {
  Object? arguments,
}) {
  return WidgetKeys.navKey.currentState!.pushNamed<T>(routeName, arguments: arguments);
}

Future<T?> popAndPushNamed<T extends Object?>(String routeName, {Object? arguments}) async {
  pop();
  return pushNamed(routeName, arguments: arguments);
}

Future<T?> pushNamedAndRemoveUntil<T extends Object?>(
  String routeName,
  RoutePredicate predicate, {
  Object? arguments,
}) {
  return WidgetKeys.navKey.currentState!.pushNamedAndRemoveUntil<T>(
    routeName,
    predicate,
    arguments: arguments,
  );
}

Future<T?> pushReplacementNamed<T extends Object?, TO extends Object?>(
  String routeName, {
  TO? result,
  Object? arguments,
}) {
  return WidgetKeys.navKey.currentState!.pushReplacementNamed<T, TO>(
    routeName,
    result: result,
    arguments: arguments,
  );
}

void pop<T extends Object?>([T? data]) {
  return WidgetKeys.navKey.currentState!.pop<T>(data);
}

void popUntil(bool Function(Route<dynamic>) predicate) {
  return WidgetKeys.navKey.currentState!.popUntil(predicate);
}

void popUntilRouteName(String routeName) {
  return WidgetKeys.navKey.currentState!.popUntil(
    ModalRoute.withName(routeName),
  );
}

void popUntilInitial() {
  WidgetKeys.navKey.currentState!.popUntil((route) => route.isFirst);
}

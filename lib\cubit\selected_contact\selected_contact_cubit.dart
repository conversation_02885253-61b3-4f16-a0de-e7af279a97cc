import 'package:bloc/bloc.dart';
import 'package:ddone/models/hive/favourite_contact.dart';
import 'package:ddone/service_locator.dart';
import 'package:ddone/services/easy_loading_service.dart';
import 'package:ddone/services/hive_service.dart';
import 'package:equatable/equatable.dart';

part 'selected_contact_state.dart';

class SelectedContactCubit extends Cubit<SelectedContactState> {
  HiveService hiveService;

  SelectedContactCubit._({SelectedContactState? state})
      : hiveService = sl.get<HiveService>(),
        super(state ?? const SelectedContactInitial());

  factory SelectedContactCubit.initial({SelectedContactState? state}) {
    //the case of is register only happen when you mock the cubit in test case
    if (sl.isRegistered<SelectedContactCubit>()) {
      return sl.get<SelectedContactCubit>();
    }

    return SelectedContactCubit._(state: state);
  }

  void setContactInfo({
    required String displayName,
    required String contactId,
    required String sipAddress,
    required bool? isLocalContact,
    required bool? isPhoneBookContact,
  }) {
    final favouriteContactList = hiveService.getAllData<FavouriteContact>();

    emit(
      SelectedContactLoaded(
        displayName: displayName,
        contactId: contactId,
        sipAddress: sipAddress,
        isLocalContact: isLocalContact ?? false,
        isPhoneBookContact: isPhoneBookContact ?? false,
        isFavouriteContact: favouriteContactList
            .where((element) => element.contactNumber == contactId && element.displayName == displayName)
            .isNotEmpty,
        isContainerVisible: true,
      ),
    );
  }

  void removeSelectedContacInfo() => emit(const SelectedContactInitial());

  void addOrRemoveFavourite({bool? removeOnly = false}) {
    final List<FavouriteContact> favouriteContactList = hiveService.getAllData<FavouriteContact>();

    for (int i = 0; i < favouriteContactList.length; i++) {
      final favouriteContact = favouriteContactList[i];

      if (favouriteContact.contactNumber == state.contactId && favouriteContact.displayName == state.displayName) {
        hiveService.deleteDataByIndex<FavouriteContact>(index: i);

        emit(
          SelectedContactLoaded(
              displayName: state.displayName,
              contactId: state.contactId,
              sipAddress: state.sipAddress,
              isLocalContact: state.isLocalContact,
              isPhoneBookContact: state.isPhoneBookContact,
              isFavouriteContact: false,
              isContainerVisible: true),
        );

        return;
      }
    }

    if (removeOnly == true) {
      return;
    }

    if (favouriteContactList.length == 10) {
      EasyLoadingService().showInfoWithText('You have reached maximum of 10 favourite contacts.');

      return;
    }

    hiveService.addData<FavouriteContact>(
      data: FavouriteContact(
        contactNumber: state.contactId,
        // This is use to determine whether favaurite contact can be edited or not.
        // we can only edit it when it is local contact created in app but not phone book contact
        isLocalContact: state.isLocalContact && !state.isPhoneBookContact,
        uri: state.sipAddress,
        displayName: state.displayName,
      ),
    );

    emit(
      SelectedContactLoaded(
        displayName: state.displayName,
        contactId: state.contactId,
        sipAddress: state.sipAddress,
        isLocalContact: state.isLocalContact,
        isPhoneBookContact: state.isPhoneBookContact,
        isFavouriteContact: true,
        isContainerVisible: true,
      ),
    );
  }
}
